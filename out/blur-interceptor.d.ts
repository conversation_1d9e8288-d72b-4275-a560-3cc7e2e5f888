/**
 * Blur Effect Interceptor
 *
 * A video frame interceptor that applies blur effects to video frames.
 * Demonstrates the multi-interceptor system with CSS filter-based blur.
 *
 * Features:
 * - Real-time blur effect application
 * - Configurable blur intensity
 * - Extends BaseInterceptor for standardized interface
 * - CSS filter-based processing for performance
 */
import BaseInterceptor from "./base-interceptor.js";
import type { BlurConfig } from "./types/global.js";
type BlurType = "gaussian" | "motion";
declare class BlurInterceptor extends BaseInterceptor {
    private canvas;
    private ctx;
    config: BlurConfig & {
        maxBlurRadius: number;
        blurType: BlurType;
    };
    constructor(name?: string, options?: BlurConfig);
    /**
     * Handle configuration updates
     */
    private onConfigUpdate;
    /**
     * Process a video frame - implements BaseInterceptor interface
     */
    processVideoFrame(frame: VideoFrame): Promise<VideoFrame>;
    /**
     * Initialize canvas for frame processing
     */
    private initializeCanvas;
    /**
     * Set blur radius
     */
    setBlurRadius(radius: number): void;
    /**
     * Get current blur radius
     */
    getBlurRadius(): number;
    /**
     * Set blur type
     */
    setBlurType(type: BlurType): void;
    /**
     * Enable/disable blur effect
     */
    setBlurEnabled(enabled: boolean): void;
    /**
     * Override cleanup to handle canvas resources
     */
    cleanup(): Promise<void>;
    /**
     * Get interceptor-specific status
     */
    getStatus(): {
        name: string;
        type: string;
        isInitialized: boolean;
        isEnabled: boolean;
        config: BlurConfig;
        stats: any;
        blurRadius: number;
        blurType: BlurType;
        blurEnabled: boolean;
        canvasInitialized: boolean;
        canvasSize: string | null;
    };
}
export default BlurInterceptor;
//# sourceMappingURL=blur-interceptor.d.ts.map