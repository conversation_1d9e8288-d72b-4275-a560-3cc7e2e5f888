/**
 * Control Tab Script for POC Streaming
 *
 * This script is injected into the control tab to manage WebRTC connections
 * and coordinate streaming between target tabs and web clients
 */
declare class ControlTabManager {
    private signalingServerUrl;
    private websocket;
    private isConnected;
    private rtcConfig;
    private webClientGroups;
    private tabGroups;
    private targetConnections;
    private clientInterceptorConfigs;
    private defaultInterceptorConfig;
    private cdpManager;
    constructor();
    init(): Promise<void>;
    /**
     * Initialize CDP Manager
     */
    initializeCDPManager(): Promise<void>;
    /**
     * Initialize the interceptor registry with available interceptors
     */
    initializeInterceptorRegistry(): void;
    /**
     * Connect to signaling server
     */
    connectToSignalingServer(): Promise<void>;
    /**
     * Handle signaling messages
     */
    private handleSignalingMessage;
    /**
     * Handle web client offer
     */
    private handleWebClientOffer;
    /**
     * Handle web client ICE candidate
     */
    private handleWebClientIceCandidate;
    /**
     * Handle user events
     */
    private handleUserEvent;
    /**
     * Handle interceptor configuration updates
     */
    private handleInterceptorConfigUpdate;
    /**
     * Send message to signaling server
     */
    sendMessage(message: any): void;
    /**
     * Cleanup resources
     */
    cleanup(): Promise<void>;
}
export default ControlTabManager;
//# sourceMappingURL=control-tab-script.d.ts.map