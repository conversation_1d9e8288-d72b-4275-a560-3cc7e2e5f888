/**
 * CDP Manager Injector Script
 *
 * This script injects the CDPManager into the control tab
 * It should be injected before the control-tab-script.js
 */
interface TargetInfo {
    id: string;
    title: string;
    type: string;
    url: string;
    webSocketDebuggerUrl: string;
}
interface UserEvent {
    eventType: string;
    x: number;
    y: number;
    deltaX?: number;
    deltaY?: number;
    key?: string;
    text?: string;
    code?: string;
    keyCode?: number;
    keyType?: string;
}
interface TabInfo {
    width: number;
    height: number;
    url: string;
    title: string;
}
interface Connection {
    client: CDP;
    sessionId: string;
    targetInfo: TargetInfo;
    createdAt: number;
}
declare class CDP {
    private targetInfo;
    private ws;
    private messageId;
    private pendingMessages;
    constructor(targetInfo: TargetInfo);
    connect(): Promise<void>;
    send(method: string, params?: any, sessionId?: string | null): Promise<any>;
    get Runtime(): {
        enable: (params?: any, sessionId?: string | null) => Promise<any>;
        evaluate: (params: any, sessionId?: string | null) => Promise<any>;
    };
    get Target(): {
        getTargets: (params?: any, sessionId?: string | null) => Promise<any>;
        createTarget: (params: any, sessionId?: string | null) => Promise<any>;
        attachToTarget: (params: any, sessionId?: string | null) => Promise<any>;
        closeTarget: (params: any, sessionId?: string | null) => Promise<any>;
        activateTarget: (params: any, sessionId?: string | null) => Promise<any>;
    };
    get Input(): {
        dispatchKeyEvent: (params: any, sessionId?: string | null) => Promise<any>;
        dispatchMouseEvent: (params: any, sessionId?: string | null) => Promise<any>;
    };
    close(): Promise<void>;
}
/**
 * CDP Manager - High-level API for CDP operations
 */
declare class CDPManager {
    private options;
    private connections;
    private eventHandlers;
    constructor(options?: {
        debugPort?: number;
        debug?: boolean;
    });
    /**
     * Log messages with CDP-Manager prefix
     */
    private log;
    /**
     * Add a new CDP connection to a target tab
     */
    addConnection(targetTabId: string, targetInfo?: TargetInfo | null): Promise<Connection>;
    /**
     * Remove a CDP connection
     */
    removeConnection(targetTabId: string): Promise<void>;
    /**
     * Get connection info for a target tab
     */
    getConnection(targetTabId: string): Connection | null;
    /**
     * Get all active connections
     */
    getAllConnections(): Map<string, Connection>;
    /**
     * Execute a command on a target tab
     */
    executeCommand(targetTabId: string, method: string, params?: any): Promise<any>;
    /**
     * Get target tab information
     */
    getTargetTabInfo(targetTabId: string): Promise<TabInfo>;
    /**
     * Execute JavaScript on a target tab
     */
    executeScript(targetTabId: string, script: string): Promise<any>;
    /**
     * Register an event handler for user events
     */
    registerEventHandler(eventType: string, handler: (userEvent: UserEvent, targetTabId: string) => Promise<void>): void;
    /**
     * Unregister an event handler
     */
    unregisterEventHandler(eventType: string): void;
    /**
     * Handle a user event by dispatching it to the appropriate target tab
     */
    handleUserEvent(userEvent: UserEvent, targetTabId: string): Promise<void>;
    /**
     * Handle click events
     */
    handleClickEvent(userEvent: UserEvent, targetTabId: string): Promise<void>;
    /**
     * Handle scroll events
     */
    handleScrollEvent(userEvent: UserEvent, targetTabId: string): Promise<void>;
    /**
     * Handle key events
     */
    handleKeyEvent(userEvent: UserEvent, targetTabId: string): Promise<void>;
    /**
     * Get target info from CDP debug port
     */
    getTargetInfo(targetTabId: string): Promise<TargetInfo | null>;
    /**
     * Initialize default event handlers
     */
    initializeDefaultHandlers(): void;
    /**
     * Clean up all connections
     */
    cleanup(): Promise<void>;
    /**
     * Get connection statistics
     */
    getStats(): {
        totalConnections: number;
        eventHandlers: number;
        connections: Array<{
            tabId: string;
            sessionId: string;
            createdAt: number;
            targetInfo: TargetInfo;
        }>;
    };
}
export { CDPManager, CDP };
export default CDPManager;
//# sourceMappingURL=cdp-manager-injector.d.ts.map