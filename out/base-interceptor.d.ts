/**
 * Base Interceptor Interface
 *
 * Defines the standard interface that all video frame interceptors must implement.
 * Provides common functionality for configuration management, lifecycle, and processing.
 *
 * Features:
 * - Standardized interceptor interface
 * - Built-in configuration management
 * - Lifecycle management (initialize, process, cleanup)
 * - Error handling and fallback mechanisms
 * - Performance monitoring capabilities
 */
import type { InterceptorConfig, InterceptorStats, BaseInterceptorInterface } from "./types/global.js";
declare abstract class BaseInterceptor implements BaseInterceptorInterface {
    readonly name: string;
    readonly type: string;
    isInitialized: boolean;
    isEnabled: boolean;
    config: InterceptorConfig;
    stats: InterceptorStats;
    private processor;
    private generator;
    private transformStream;
    private originalTrack;
    private processedTrack;
    protected onConfigChange?: (oldConfig: InterceptorConfig, newConfig: InterceptorConfig) => void;
    constructor(name: string, defaultConfig?: InterceptorConfig);
    /**
     * Initialize the interceptor with a video track
     */
    initialize(videoTrack?: MediaStreamTrack): Promise<MediaStreamTrack>;
    /**
     * Process a single video frame - handles common logic and delegates to subclass
     */
    processFrame(frame: VideoFrame, controller: TransformStreamDefaultController<VideoFrame>): Promise<void>;
    /**
     * Abstract method for specific frame processing - must be implemented by subclasses
     */
    abstract processVideoFrame(frame: VideoFrame): Promise<VideoFrame>;
    /**
     * Update interceptor configuration
     */
    updateConfig(newConfig: Partial<InterceptorConfig>): void;
    /**
     * Get current configuration
     */
    getConfig(): InterceptorConfig;
    /**
     * Enable the interceptor
     */
    enable(): void;
    /**
     * Disable the interceptor
     */
    disable(): void;
    /**
     * Get performance statistics
     */
    getStats(): InterceptorStats;
    /**
     * Reset performance statistics
     */
    resetStats(): void;
    /**
     * Cleanup resources
     */
    cleanup(): Promise<void>;
    /**
     * Logging utility
     */
    log(...args: any[]): void;
    /**
     * Get interceptor metadata
     */
    getMetadata(): {
        name: string;
        type: string;
        isInitialized: boolean;
        isEnabled: boolean;
        config: InterceptorConfig;
        stats: InterceptorStats;
    };
}
export default BaseInterceptor;
//# sourceMappingURL=base-interceptor.d.ts.map