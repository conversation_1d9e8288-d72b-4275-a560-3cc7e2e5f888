/**
 * Interceptor Pipeline System
 *
 * Manages sequential processing of video frames through multiple interceptors.
 * Provides a unified interface for chaining multiple interceptors together.
 *
 * Features:
 * - Sequential interceptor processing
 * - Error handling and fallback mechanisms
 * - Performance monitoring for the entire pipeline
 * - Dynamic interceptor addition/removal
 * - Pipeline state management
 */
import type { InterceptorStats, BaseInterceptorInterface, InterceptorPipelineInterface, InterceptorConfig } from "./types/global.js";
interface PipelineStats extends InterceptorStats {
    interceptorStats: Map<string, {
        framesProcessed: number;
        totalProcessingTime: number;
        averageProcessingTime: number;
    }>;
}
declare class InterceptorPipeline implements InterceptorPipelineInterface {
    readonly interceptors: BaseInterceptorInterface[];
    isInitialized: boolean;
    isEnabled: boolean;
    stats: PipelineStats;
    private originalTrack;
    private processedTrack;
    private processor;
    private generator;
    private transformStream;
    constructor(interceptors?: BaseInterceptorInterface[]);
    /**
     * Initialize the pipeline with a video track
     */
    initialize(videoTrack: MediaStreamTrack): Promise<MediaStreamTrack>;
    /**
     * Process a single video frame through all interceptors
     */
    private processFrame;
    /**
     * Update performance stats for a specific interceptor
     */
    private updateInterceptorStats;
    /**
     * Add an interceptor to the pipeline
     */
    addInterceptor(interceptor: BaseInterceptorInterface, index?: number): void;
    /**
     * Remove an interceptor from the pipeline
     */
    removeInterceptor(name: string): boolean;
    /**
     * Get an interceptor by name or index
     */
    getInterceptor(identifier: string | number): BaseInterceptorInterface | null;
    /**
     * Get list of interceptor names in order
     */
    getInterceptorNames(): string[];
    /**
     * Enable the pipeline
     */
    enable(): void;
    /**
     * Disable the pipeline
     */
    disable(): void;
    /**
     * Update interceptor configuration by name
     */
    updateInterceptorConfig(name: string, config: Partial<InterceptorConfig>): boolean;
    /**
     * Get pipeline performance statistics
     */
    getStats(): PipelineStats & {
        interceptorCount: number;
        enabledInterceptorCount: number;
    };
    /**
     * Reset performance statistics
     */
    resetStats(): void;
    /**
     * Update interceptor configurations dynamically
     */
    updateInterceptorConfigs(interceptorConfigs: Record<string, Partial<InterceptorConfig>>): void;
    /**
     * Cleanup pipeline resources
     */
    cleanup(): Promise<void>;
    /**
     * Logging utility
     */
    private log;
}
export default InterceptorPipeline;
//# sourceMappingURL=interceptor-pipeline.d.ts.map