{"version": 3, "file": "video-crop-interceptor.js", "sourceRoot": "", "sources": ["../src/video-crop-interceptor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,OAAO,eAAe,MAAM,uBAAuB,CAAC;AAsBpD,MAAM,qBAAsB,SAAQ,eAAe;IAQjD,YAAY,OAAe,YAAY,EAAE,UAA2B,EAAE;QACpE,uDAAuD;QACvD,MAAM,aAAa,GAAoB;YACrC,KAAK,EAAE,KAAK;YACZ,SAAS,EAAE,EAAE;YACb,cAAc,EAAE,IAAI;YACpB,GAAG,OAAO;SACX,CAAC;QAEF,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAhB7B,kCAAkC;QAC1B,gBAAW,GAAG,IAAI,GAAG,EAAmC,CAAC;QACzD,eAAU,GAAW,CAAC,CAAC;QACvB,kBAAa,GAAW,CAAC,CAAC;QAehC,IAAI,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,KAAiB;QACvC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEtC,yBAAyB;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC;YACjE,IAAI,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,UAAU,SAAS,CAAC,CAAC;YAChD,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;QACnC,CAAC;QAED,IAAI,cAAc,GAAG,KAAK,CAAC;QAE3B,mDAAmD;QACnD,IACE,IAAI,CAAC,MAAM,CAAC,cAAc;YAC1B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EACzD,CAAC;YACD,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;QAED,iEAAiE;QACjE,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,KAAiB;QACrC,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAE3E,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,sEAAsE;QACtE,MAAM,cAAc,GAAe;YACjC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;YACjE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;YAClE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAClB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CACnE;YACD,MAAM,EAAE,IAAI,CAAC,QAAQ,CACnB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CACrE;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE;gBACzC,WAAW,EAAE,cAAc;gBAC3B,YAAY,EAAE,cAAc,CAAC,KAAK;gBAClC,aAAa,EAAE,cAAc,CAAC,MAAM;gBACpC,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,SAAS;aACtC,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;YAC3C,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,KAAiB;QAC/C,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,KAAK,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACxD,IAAI,CAAC;gBACH,uDAAuD;gBACvD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE;oBACvC,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,SAAS;iBACtC,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAuB;oBACnC,YAAY;oBACZ,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,SAAS,EAAE,WAAW,CAAC,GAAG,EAAE;oBAC5B,UAAU,EACR,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,IAAI;iBAClE,CAAC;gBAEF,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAE9C,yBAAyB;gBACzB,IAAI,MAAM,YAAY,OAAO,EAAE,CAAC;oBAC9B,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,GAAG,CAAC,8BAA8B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CACP,YAAoB,EACpB,QAAiC;QAEjC,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,CACN,qBAAqB,YAAY,YAAY,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CACtE,CAAC;QAEF,8BAA8B;QAC9B,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,YAAoB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,GAAG,CACN,uBAAuB,YAAY,gBAAgB,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAC5E,CAAC;QACJ,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAsC;QAClD,IAAI,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YACjD,IAAI,CAAC,YAAY,CAAC;gBAChB,UAAU,EAAE;oBACV,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC;oBACnC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC;oBACnC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,IAAI,GAAG,CAAC;oBAC7C,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,IAAI,GAAG,CAAC;iBAChD;aACF,CAAC,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAAgB;QACjC,IAAI,CAAC,YAAY,CAAC;YAChB,cAAc,EAAE,CAAC,CAAC,OAAO;SAC1B,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,SAAS;QAcP,OAAO;YACL,GAAG,IAAI,CAAC,WAAW,EAAE;YACrB,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,KAAK;YACnD,UAAU,EACR,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,IAAI;YACjE,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YACtC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,gBAAgB,EAAE,KAAK,EAAE,+CAA+C;YACxE,iBAAiB,EAAE,KAAK;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,OAAO;QACpB,IAAI,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAE7C,oBAAoB;QACpB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,sBAAsB;QACtB,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QAEtB,IAAI,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,KAAa;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;CACF;AAED,kCAAkC;AAClC,eAAe,qBAAqB,CAAC;AAErC,gDAAgD;AAChD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IACjC,MAAc,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;AAChE,CAAC"}