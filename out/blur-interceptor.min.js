import BaseInterceptor from"./base-interceptor.js";class BlurInterceptor extends BaseInterceptor{constructor(name="blur-effect",options={}){const defaultConfig={debug:false,blurRadius:0,maxBlurRadius:20,blurType:"gaussian",...options};super(name,defaultConfig);this.canvas=null;this.ctx=null;this.onConfigChange=this.onConfigUpdate.bind(this);this.log("BlurInterceptor initialized",this.config)}onConfigUpdate(oldConfig,newConfig){const oldBlurRadius=oldConfig.blurRadius||0;const newBlurRadius=newConfig.blurRadius||0;if(newBlurRadius<0){this.config.blurRadius=0;this.log("warn","Blur radius clamped to minimum: 0")}else if(newBlurRadius>this.config.maxBlurRadius){this.config.blurRadius=this.config.maxBlurRadius;this.log("warn",`Blur radius clamped to maximum: ${this.config.maxBlurRadius}`)}this.log("info",`Blur radius updated from ${oldBlurRadius} to ${this.config.blurRadius}`)}async processVideoFrame(frame){if((this.config.blurRadius||0)<=0){return frame}try{if(!this.canvas||this.canvas.width!==frame.codedWidth||this.canvas.height!==frame.codedHeight){this.initializeCanvas(frame.codedWidth,frame.codedHeight)}if(!this.ctx){throw new Error("Canvas context not available")}const blurRadius=Math.min(this.config.blurRadius||0,this.config.maxBlurRadius);this.ctx.filter=`blur(${blurRadius}px)`;this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height);this.ctx.drawImage(frame,0,0);this.ctx.filter="none";const processedFrame=new VideoFrame(this.canvas,{timestamp:frame.timestamp,duration:frame.duration??undefined});return processedFrame}catch(error){this.log("Error processing blur:",error);return frame}}initializeCanvas(width,height){this.canvas=document.createElement("canvas");this.canvas.width=width;this.canvas.height=height;this.ctx=this.canvas.getContext("2d");if(!this.ctx){throw new Error("Failed to get 2D canvas context")}this.ctx.imageSmoothingEnabled=true;this.ctx.imageSmoothingQuality="high";this.log(`Canvas initialized: ${width}x${height}`)}setBlurRadius(radius){const clampedRadius=Math.max(0,Math.min(this.config.maxBlurRadius,radius));this.updateConfig({blurRadius:clampedRadius});this.log(`Blur radius set to: ${clampedRadius}px`)}getBlurRadius(){return this.config.blurRadius||0}setBlurType(type){if(["gaussian","motion"].includes(type)){this.updateConfig({blurType:type});this.log(`Blur type set to: ${type}`)}else{this.log(`Invalid blur type: ${type}. Using 'gaussian'.`)}}setBlurEnabled(enabled){const radius=enabled?this.config.blurRadius||5:0;this.setBlurRadius(radius)}async cleanup(){this.log("Cleaning up blur interceptor...");if(this.canvas){this.canvas.width=0;this.canvas.height=0;this.canvas=null}this.ctx=null;await super.cleanup();this.log("Blur interceptor cleanup complete")}getStatus(){return{...this.getMetadata(),blurRadius:this.config.blurRadius||0,blurType:this.config.blurType,blurEnabled:(this.config.blurRadius||0)>0,canvasInitialized:!!this.canvas,canvasSize:this.canvas?`${this.canvas.width}x${this.canvas.height}`:null}}}export default BlurInterceptor;if(typeof window!=="undefined"){window.BlurInterceptor=BlurInterceptor}