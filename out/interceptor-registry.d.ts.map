{"version": 3, "file": "interceptor-registry.d.ts", "sourceRoot": "", "sources": ["../src/interceptor-registry.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,KAAK,EACV,iBAAiB,EACjB,sBAAsB,EACtB,wBAAwB,EACxB,4BAA4B,EAC7B,MAAM,mBAAmB,CAAC;AAE3B,UAAU,mBAAmB;IAC3B,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC3B,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;CACzC;AAED,cAAM,mBAAoB,YAAW,4BAA4B;IAE/D,OAAO,CAAC,kBAAkB,CAA6C;IAGvE,OAAO,CAAC,cAAc,CAAwC;IAG9D,OAAO,CAAC,kBAAkB,CAGtB;IAGJ,OAAO,CAAC,aAAa,CAA0C;;IAM/D;;OAEG;IACH,QAAQ,CACN,IAAI,EAAE,MAAM,EACZ,gBAAgB,EAAE,sBAAsB,EACxC,aAAa,GAAE,iBAAsB,GACpC,IAAI;IAeP;;OAEG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAQ9B;;OAEG;IACH,yBAAyB,IAAI,MAAM,EAAE;IAIrC;;OAEG;IACH,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAInC;;OAEG;IACH,MAAM,CACJ,IAAI,EAAE,MAAM,EACZ,MAAM,CAAC,EAAE,iBAAiB,GACzB,wBAAwB,GAAG,IAAI;IAkBlC;;OAEG;IACH,sBAAsB,CACpB,WAAW,EAAE,MAAM,EACnB,gBAAgB,GAAE,MAAM,EAAO,EAC/B,OAAO,GAAE,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAM,GAC9C,IAAI;IAyBP;;OAEG;IACH,sBAAsB,CAAC,WAAW,EAAE,MAAM,GAAG,mBAAmB;IAYhE;;OAEG;IACH,wBAAwB,CAAC,WAAW,EAAE,MAAM,GAAG,wBAAwB,EAAE;IA2DzE;;OAEG;IACH,qBAAqB,CAAC,WAAW,EAAE,MAAM,GAAG,wBAAwB,EAAE;IAetE;;OAEG;IACH,6BAA6B,CAC3B,WAAW,EAAE,MAAM,EACnB,eAAe,EAAE,MAAM,EACvB,SAAS,EAAE,OAAO,CAAC,iBAAiB,CAAC,GACpC,IAAI;IAuBP;;OAEG;IACH,yBAAyB,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAqBpD;;OAEG;IACH,QAAQ,IAAI;QACV,sBAAsB,EAAE,MAAM,CAAC;QAC/B,aAAa,EAAE,MAAM,CAAC;QACtB,uBAAuB,EAAE,MAAM,CAAC;QAChC,WAAW,EAAE,MAAM,CACjB,MAAM,EACN;YACE,gBAAgB,EAAE,MAAM,CAAC;YACzB,YAAY,EAAE,KAAK,CAAC;gBAClB,IAAI,EAAE,MAAM,CAAC;gBACb,IAAI,EAAE,MAAM,CAAC;gBACb,SAAS,EAAE,OAAO,CAAC;gBACnB,KAAK,EAAE,GAAG,CAAC;aACZ,CAAC,CAAC;SACJ,CACF,CAAC;KACH;IA0BD;;OAEG;IACH,OAAO,CAAC,GAAG;CAGZ;AAGD,QAAA,MAAM,mBAAmB,qBAA4B,CAAC;AAGtD,OAAO,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;AACpD,eAAe,mBAAmB,CAAC"}