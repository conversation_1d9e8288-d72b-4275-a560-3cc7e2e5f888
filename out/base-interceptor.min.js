class BaseInterceptor{constructor(name,defaultConfig={}){this.isInitialized=false;this.isEnabled=true;this.processor=null;this.generator=null;this.transformStream=null;this.originalTrack=null;this.processedTrack=null;if(this.constructor===BaseInterceptor){throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly")}this.name=name;this.type=this.constructor.name;this.config={debug:false,enabled:true,...defaultConfig};this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0};this.log(`${this.name} interceptor created`)}async initialize(videoTrack){if(!videoTrack||videoTrack.kind!=="video"){throw new Error(`${this.name} interceptor requires a valid video track`)}this.originalTrack=videoTrack;this.log(`Initializing ${this.name} interceptor with video track:`,videoTrack.label);try{this.processor=new MediaStreamTrackProcessor({track:videoTrack});this.generator=new MediaStreamTrackGenerator({kind:"video"});this.transformStream=new TransformStream({transform:this.processFrame.bind(this)});this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(error=>{this.log(`Pipeline error in ${this.name}:`,error);this.stats.errorsEncountered++});this.processedTrack=this.generator.track;this.isInitialized=true;this.log(`${this.name} interceptor initialized successfully`);return this.processedTrack}catch(error){this.log(`Error initializing ${this.name} interceptor:`,error);this.stats.errorsEncountered++;throw error}}async processFrame(frame,controller){const startTime=performance.now();try{this.stats.framesProcessed++;if(!this.isEnabled||!this.config.enabled){controller.enqueue(frame);return}const processedFrame=await this.processVideoFrame(frame);controller.enqueue(processedFrame);if(processedFrame!==frame){frame.close()}}catch(error){this.log(`Error processing frame in ${this.name}:`,error);this.stats.errorsEncountered++;controller.enqueue(frame)}finally{const processingTime=performance.now()-startTime;this.stats.lastProcessingTime=processingTime;this.stats.totalProcessingTime+=processingTime;this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}}updateConfig(newConfig){if(!newConfig||typeof newConfig!=="object"){this.log("warn","Invalid configuration provided to updateConfig");return}const oldConfig={...this.config};this.config={...this.config,...newConfig};this.log(`${this.name} configuration updated:`,{old:oldConfig,new:this.config});if(this.onConfigChange){this.onConfigChange(oldConfig,this.config)}}getConfig(){return{...this.config}}enable(){this.isEnabled=true;this.config.enabled=true;this.log(`${this.name} interceptor enabled`)}disable(){this.isEnabled=false;this.config.enabled=false;this.log(`${this.name} interceptor disabled`)}getStats(){return{...this.stats}}resetStats(){this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0};this.log(`${this.name} stats reset`)}async cleanup(){try{if(this.processor){this.processor=null}if(this.generator){this.generator=null}if(this.transformStream){this.transformStream=null}if(this.originalTrack){this.originalTrack=null}if(this.processedTrack){this.processedTrack=null}this.isInitialized=false;this.log(`${this.name} interceptor cleaned up`)}catch(error){this.log(`Error cleaning up ${this.name} interceptor:`,error)}}log(...args){if(this.config.debug){console.log(`[${this.name}-Interceptor]`,...args)}}getMetadata(){return{name:this.name,type:this.type,isInitialized:this.isInitialized,isEnabled:this.isEnabled,config:this.getConfig(),stats:this.getStats()}}}export default BaseInterceptor;if(typeof window!=="undefined"){window.BaseInterceptor=BaseInterceptor}