{"version": 3, "file": "base-interceptor.d.ts", "sourceRoot": "", "sources": ["../src/base-interceptor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,KAAK,EACV,iBAAiB,EACjB,gBAAgB,EAChB,wBAAwB,EACzB,MAAM,mBAAmB,CAAC;AAE3B,uBAAe,eAAgB,YAAW,wBAAwB;IAChE,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,SAAgB,IAAI,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,OAAO,CAAS;IAC/B,SAAS,EAAE,OAAO,CAAQ;IAC1B,MAAM,EAAE,iBAAiB,CAAC;IAC1B,KAAK,EAAE,gBAAgB,CAAC;IAG/B,OAAO,CAAC,SAAS,CAA0C;IAC3D,OAAO,CAAC,SAAS,CAA0C;IAC3D,OAAO,CAAC,eAAe,CAChB;IAGP,OAAO,CAAC,aAAa,CAAiC;IACtD,OAAO,CAAC,cAAc,CAAiC;IAGvD,SAAS,CAAC,cAAc,CAAC,EAAE,CACzB,SAAS,EAAE,iBAAiB,EAC5B,SAAS,EAAE,iBAAiB,KACzB,IAAI,CAAC;gBAEE,IAAI,EAAE,MAAM,EAAE,aAAa,GAAE,iBAAsB;IA6B/D;;OAEG;IACG,UAAU,CAAC,UAAU,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;IA0C1E;;OAEG;IACG,YAAY,CAChB,KAAK,EAAE,UAAU,EACjB,UAAU,EAAE,gCAAgC,CAAC,UAAU,CAAC,GACvD,OAAO,CAAC,IAAI,CAAC;IAsChB;;OAEG;IACH,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IAElE;;OAEG;IACH,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,IAAI;IAoBzD;;OAEG;IACH,SAAS,IAAI,iBAAiB;IAI9B;;OAEG;IACH,MAAM,IAAI,IAAI;IAMd;;OAEG;IACH,OAAO,IAAI,IAAI;IAMf;;OAEG;IACH,QAAQ,IAAI,gBAAgB;IAI5B;;OAEG;IACH,UAAU,IAAI,IAAI;IAWlB;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IA6B9B;;OAEG;IACH,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI;IAMzB;;OAEG;IACH,WAAW,IAAI;QACb,IAAI,EAAE,MAAM,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;QACb,aAAa,EAAE,OAAO,CAAC;QACvB,SAAS,EAAE,OAAO,CAAC;QACnB,MAAM,EAAE,iBAAiB,CAAC;QAC1B,KAAK,EAAE,gBAAgB,CAAC;KACzB;CAUF;AAGD,eAAe,eAAe,CAAC"}