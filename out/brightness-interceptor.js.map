{"version": 3, "file": "brightness-interceptor.js", "sourceRoot": "", "sources": ["../src/brightness-interceptor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AAEH,OAAO,eAAe,MAAM,uBAAuB,CAAC;AAGpD,MAAM,qBAAsB,SAAQ,eAAe;IAWjD,YACE,OAAe,mBAAmB,EAClC,UAA4B,EAAE;QAE9B,mDAAmD;QACnD,MAAM,aAAa,GAAG;YACpB,KAAK,EAAE,KAAK;YACZ,UAAU,EAAE,GAAG,EAAE,6CAA6C;YAC9D,aAAa,EAAE,GAAG;YAClB,aAAa,EAAE,GAAG;YAClB,GAAG,OAAO;SACX,CAAC;QAEF,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAvB7B,8BAA8B;QACtB,WAAM,GAA6B,IAAI,CAAC;QACxC,QAAG,GAAoC,IAAI,CAAC;QAC5C,cAAS,GAAqB,IAAI,CAAC;QAsBzC,sCAAsC;QACtC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACK,cAAc,CACpB,SAA4B,EAC5B,SAA4B;QAE5B,MAAM,aAAa,GAAI,SAAiB,CAAC,UAAU,IAAI,GAAG,CAAC;QAC3D,MAAM,aAAa,GAAI,SAAiB,CAAC,UAAU,IAAI,GAAG,CAAC;QAE3D,4BAA4B;QAC5B,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;YACnD,IAAI,CAAC,GAAG,CACN,MAAM,EACN,kCAAkC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAC9D,CAAC;QACJ,CAAC;aAAM,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;YACnD,IAAI,CAAC,GAAG,CACN,MAAM,EACN,kCAAkC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAC9D,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,GAAG,CACN,MAAM,EACN,2BAA2B,aAAa,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CACxE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,KAAiB;QACvC,wDAAwD;QACxD,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,8BAA8B;YAC9B,IACE,CAAC,IAAI,CAAC,MAAM;gBACZ,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,UAAU;gBACtC,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,WAAW,EACxC,CAAC;gBACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAEhC,iBAAiB;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CACpC,CAAC,EACD,CAAC,EACD,IAAI,CAAC,MAAO,CAAC,KAAK,EAClB,IAAI,CAAC,MAAO,CAAC,MAAM,CACpB,CAAC;YACF,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAEjC,8BAA8B;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CACzB,IAAI,CAAC,MAAM,CAAC,aAAa,EACzB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,GAAG,CAAC,CACnE,CAAC;YAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxC,mCAAmC;gBACnC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM;gBACrD,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,QAAQ;gBAC/D,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,OAAO;gBAC9D,gDAAgD;YAClD,CAAC;YAED,yCAAyC;YACzC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE5C,oCAAoC;YACpC,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAO,EAAE;gBAClD,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,SAAS;aACtC,CAAC,CAAC;YAEH,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YAChD,iCAAiC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAa,EAAE,MAAc;QACpD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,uBAAuB,KAAK,IAAI,MAAM,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAkB;QAC9B,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAChC,IAAI,CAAC,MAAM,CAAC,aAAa,EACzB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,UAAU,CAAC,CAChD,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,EAAE,UAAU,EAAE,iBAAiB,EAAE,CAAC,CAAC;QACrD,IAAI,CAAC,GAAG,CAAC,sBAAsB,iBAAiB,EAAE,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,OAAO;QACpB,IAAI,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAElD,4BAA4B;QAC5B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,sBAAsB;QACtB,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QAEtB,IAAI,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,SAAS;QAWP,OAAO;YACL,GAAG,IAAI,CAAC,WAAW,EAAE;YACrB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,GAAG;YACzC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM;YAChC,UAAU,EAAE,IAAI,CAAC,MAAM;gBACrB,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC9C,CAAC,CAAC,IAAI;SACT,CAAC;IACJ,CAAC;CACF;AAED,kCAAkC;AAClC,eAAe,qBAAqB,CAAC;AAErC,gDAAgD;AAChD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IACjC,MAAc,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;AAChE,CAAC"}