/**
 * Interceptor Registry System
 *
 * Manages registration, instantiation, and lifecycle of video frame interceptors.
 * Provides a centralized system for creating and managing multiple interceptors.
 *
 * Features:
 * - Dynamic interceptor registration and instantiation
 * - Configuration management per interceptor type
 * - Interceptor lifecycle management
 * - Sequential processing pipeline creation
 * - Error handling and fallback mechanisms
 */
import type { InterceptorConfig, InterceptorConstructor, BaseInterceptorInterface, InterceptorRegistryInterface } from "./types/global.js";
interface ClientConfiguration {
    interceptorNames: string[];
    configs: Map<string, InterceptorConfig>;
}
declare class InterceptorRegistry implements InterceptorRegistryInterface {
    private interceptorClasses;
    private defaultConfigs;
    private clientInterceptors;
    private clientConfigs;
    constructor();
    /**
     * Register an interceptor class
     */
    register(name: string, interceptorClass: InterceptorConstructor, defaultConfig?: InterceptorConfig): void;
    /**
     * Unregister an interceptor class
     */
    unregister(name: string): void;
    /**
     * Get list of registered interceptor names
     */
    getRegisteredInterceptors(): string[];
    /**
     * Check if an interceptor is registered
     */
    isRegistered(name: string): boolean;
    /**
     * Create a single interceptor instance
     */
    create(name: string, config?: InterceptorConfig): BaseInterceptorInterface | null;
    /**
     * Set interceptor configuration for a client
     */
    setClientConfiguration(webClientId: string, interceptorNames?: string[], configs?: Record<string, InterceptorConfig>): void;
    /**
     * Get client configuration
     */
    getClientConfiguration(webClientId: string): ClientConfiguration;
    /**
     * Create interceptor instances for a client
     */
    createClientInterceptors(webClientId: string): BaseInterceptorInterface[];
    /**
     * Get interceptor instances for a client
     */
    getClientInterceptors(webClientId: string): BaseInterceptorInterface[];
    /**
     * Update configuration for a specific interceptor of a client
     */
    updateClientInterceptorConfig(webClientId: string, interceptorName: string, newConfig: Partial<InterceptorConfig>): void;
    /**
     * Cleanup interceptors for a client
     */
    cleanupClientInterceptors(webClientId: string): void;
    /**
     * Get registry statistics
     */
    getStats(): {
        registeredInterceptors: number;
        activeClients: number;
        totalActiveInterceptors: number;
        clientStats: Record<string, {
            interceptorCount: number;
            interceptors: Array<{
                name: string;
                type: string;
                isEnabled: boolean;
                stats: any;
            }>;
        }>;
    };
    /**
     * Logging utility
     */
    private log;
}
declare const interceptorRegistry: InterceptorRegistry;
export { InterceptorRegistry, interceptorRegistry };
export default interceptorRegistry;
//# sourceMappingURL=interceptor-registry.d.ts.map