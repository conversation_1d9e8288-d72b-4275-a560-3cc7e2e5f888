{"version": 3, "file": "control-tab-script.js", "sourceRoot": "", "sources": ["../src/control-tab-script.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AA0BH,MAAM,iBAAiB;IA8DrB;QA7DQ,uBAAkB,GAAW,qBAAqB,CAAC;QACnD,cAAS,GAAqB,IAAI,CAAC;QACnC,gBAAW,GAAY,KAAK,CAAC;QAErC,uBAAuB;QACf,cAAS,GAAqB;YACpC,UAAU,EAAE;gBACV,EAAE,IAAI,EAAE,+BAA+B,EAAE;gBACzC,EAAE,IAAI,EAAE,8BAA8B,EAAE;aACzC;YACD,oBAAoB,EAAE,EAAE;SACzB,CAAC;QAEF,wBAAwB;QAChB,oBAAe,GAAG,IAAI,GAAG,EAA0B,CAAC;QACpD,cAAS,GAAG,IAAI,GAAG,EAAoB,CAAC;QACxC,sBAAiB,GAAG,IAAI,GAAG,EAA6B,CAAC;QACzD,6BAAwB,GAAG,IAAI,GAAG,EAAe,CAAC;QAE1D,oDAAoD;QAC5C,6BAAwB,GAA6B;YAC3D,gBAAgB,EAAE;gBAChB,iBAAiB;gBACjB,mBAAmB;gBACnB,aAAa;gBACb,YAAY;aACb;YACD,kBAAkB,EAAE;gBAClB,YAAY,EAAE;oBACZ,OAAO,EAAE,IAAI;oBACb,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE;wBACV,CAAC,EAAE,CAAC;wBACJ,CAAC,EAAE,CAAC;wBACJ,KAAK,EAAE,MAAM,CAAC,UAAU;wBACxB,MAAM,EAAE,MAAM,CAAC,WAAW;qBAC3B;iBACF;gBACD,iBAAiB,EAAE;oBACjB,OAAO,EAAE,KAAK;oBACd,eAAe,EAAE,CAAC;oBAClB,kBAAkB,EAAE,CAAC;oBACrB,uBAAuB,EAAE,CAAC;oBAC1B,eAAe,EAAE,IAAI;oBACrB,kBAAkB,EAAE,GAAG;oBACvB,aAAa,EAAE,CAAC;iBACjB;gBACD,mBAAmB,EAAE;oBACnB,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,GAAG;iBAChB;gBACD,aAAa,EAAE;oBACb,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,CAAC;iBACd;aACF;SACF,CAAC;QAEF,4BAA4B;QACpB,eAAU,GAAsB,IAAI,CAAC;QAG3C,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAED,KAAK,CAAC,IAAI;QACR,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QAEnE,sEAAsE;QACtE,IAAI,CAAC,6BAA6B,EAAE,CAAC;QAErC,yBAAyB;QACzB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAElC,8BAA8B;QAC9B,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEtC,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YACH,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,WAAW,EAAE,CAAC;gBAC7C,MAAM,IAAI,KAAK,CACb,iEAAiE,CAClE,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;YAEpC,iDAAiD;YACjD,IACE,IAAI,CAAC,UAAU;gBACf,OAAQ,IAAI,CAAC,UAAkB,CAAC,yBAAyB,KAAK,UAAU,EACxE,CAAC;gBACA,IAAI,CAAC,UAAkB,CAAC,yBAAyB,EAAE,CAAC;YACvD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,6BAA6B;QAC3B,uCAAuC;QACvC,IAAI,OAAO,MAAM,CAAC,mBAAmB,KAAK,WAAW,EAAE,CAAC;YACtD,8CAA8C;YAC9C,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,WAAW,EAAE,CAAC;gBACxD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CACjC,YAAY,EACZ,MAAM,CAAC,qBAAqB,EAC5B;oBACE,KAAK,EAAE,IAAI;oBACX,cAAc,EAAE,IAAI;iBACrB,CACF,CAAC;YACJ,CAAC;YAED,iCAAiC;YACjC,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,WAAW,EAAE,CAAC;gBACxD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CACjC,mBAAmB,EACnB,MAAM,CAAC,qBAAqB,EAC5B;oBACE,KAAK,EAAE,IAAI;oBACX,UAAU,EAAE,GAAG;iBAChB,CACF,CAAC;YACJ,CAAC;YAED,2BAA2B;YAC3B,IAAI,OAAO,MAAM,CAAC,eAAe,KAAK,WAAW,EAAE,CAAC;gBAClD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CACjC,aAAa,EACb,MAAM,CAAC,eAAe,EACtB;oBACE,KAAK,EAAE,IAAI;oBACX,UAAU,EAAE,CAAC;iBACd,CACF,CAAC;YACJ,CAAC;YAED,qCAAqC;YACrC,IAAI,OAAO,MAAM,CAAC,yBAAyB,KAAK,WAAW,EAAE,CAAC;gBAC5D,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CACjC,iBAAiB,EACjB,MAAM,CAAC,yBAAyB,EAChC;oBACE,KAAK,EAAE,IAAI;oBACX,OAAO,EAAE,KAAK;oBACd,eAAe,EAAE,CAAC;oBAClB,kBAAkB,EAAE,CAAC;oBACrB,uBAAuB,EAAE,CAAC;oBAC1B,eAAe,EAAE,IAAI;oBACrB,kBAAkB,EAAE,GAAG;oBACvB,aAAa,EAAE,CAAC;iBACjB,CACF,CAAC;YACJ,CAAC;YAED,OAAO,CAAC,GAAG,CACT,wDAAwD,EACxD,MAAM,CAAC,mBAAmB,CAAC,yBAAyB,EAAE,CACvD,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB;QAC5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC;gBACH,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAExD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE;oBAC3B,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;oBAC7D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBACxB,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC;gBAEF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE;oBACnC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtD,CAAC,CAAC;gBAEF,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC5B,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;oBAClE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBAC3B,CAAC,CAAC;gBAEF,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;oBACjC,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;oBACzD,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CACX,wDAAwD,EACxD,KAAK,CACN,CAAC;gBACF,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,OAAY;QACzC,OAAO,CAAC,GAAG,CAAC,6CAA6C,EAAE,OAAO,CAAC,CAAC;QAEpE,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,kBAAkB;gBACrB,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBACnC,MAAM;YACR,KAAK,0BAA0B;gBAC7B,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;gBAC1C,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBAC9B,MAAM;YACR,KAAK,2BAA2B;gBAC9B,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;gBAC5C,MAAM;YACR;gBACE,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,OAAY;QAC7C,IAAI,CAAC;YACH,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;YACvC,OAAO,CAAC,GAAG,CACT,mDAAmD,WAAW,EAAE,CACjE,CAAC;YAEF,wCAAwC;YACxC,MAAM,cAAc,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE7D,wCAAwC;YACxC,MAAM,WAAW,GAAG,cAAc,CAAC,iBAAiB,CAAC,SAAS,EAAE;gBAC9D,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;YAEH,yBAAyB;YACzB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE;gBACpC,cAAc;gBACd,mBAAmB,EAAE,IAAI;gBACzB,WAAW;gBACX,SAAS,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;aAC/B,CAAC,CAAC;YAEH,yBAAyB;YACzB,MAAM,cAAc,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAEjD,mCAAmC;YACnC,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,YAAY,EAAE,CAAC;YACnD,MAAM,cAAc,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAEjD,iCAAiC;YACjC,IAAI,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,oBAAoB;gBAC1B,WAAW;gBACX,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,8CAA8C,WAAW,EAAE,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B,CAAC,OAAY;QACpD,IAAI,CAAC;YACH,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;YAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAE7D,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,cAAc,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;gBAC/D,OAAO,CAAC,GAAG,CACT,uDAAuD,WAAW,EAAE,CACrE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,OAAY;QACxC,IAAI,CAAC;YACH,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;YAE3C,IACE,IAAI,CAAC,UAAU;gBACf,OAAQ,IAAI,CAAC,UAAkB,CAAC,eAAe,KAAK,UAAU,EAC9D,CAAC;gBACD,MAAO,IAAI,CAAC,UAAkB,CAAC,eAAe,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBACvE,OAAO,CAAC,GAAG,CACT,+CAA+C,WAAW,EAAE,CAC7D,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CACV,mEAAmE,CACpE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,6BAA6B,CAAC,OAAY;QAChD,IAAI,CAAC;YACH,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;YACpD,OAAO,CAAC,GAAG,CACT,2DAA2D,WAAW,EAAE,CACzE,CAAC;YAEF,8BAA8B;YAC9B,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YAEnE,kDAAkD;YAClD,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAC7D,IAAI,cAAc,IAAI,cAAc,CAAC,mBAAmB,EAAE,CAAC;gBACzD,8CAA8C;gBAC9C,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;oBAChE,cAAc,CAAC,mBAAmB,CAAC,uBAAuB,CACxD,IAAI,EACJ,MAAoC,CACrC,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,CACT,0DAA0D,WAAW,EAAE,CACxE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CACX,oDAAoD,EACpD,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAAY;QACtB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CACV,yEAAyE,CAC1E,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAElE,6BAA6B;QAC7B,KAAK,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACxD,IAAI,CAAC;gBACH,IAAI,KAAK,CAAC,mBAAmB,EAAE,CAAC;oBAC9B,MAAM,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;gBAC5C,CAAC;gBACD,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CACX,gDAAgD,WAAW,GAAG,EAC9D,KAAK,CACN,CAAC;YACJ,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,KAAK,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzD,IAAI,CAAC;gBACH,UAAU,CAAC,KAAK,EAAE,CAAC;YACrB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CACX,uDAAuD,KAAK,GAAG,EAC/D,KAAK,CACN,CAAC;YACJ,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;QAED,sBAAsB;QACtB,IACE,IAAI,CAAC,UAAU;YACf,OAAQ,IAAI,CAAC,UAAkB,CAAC,OAAO,KAAK,UAAU,EACtD,CAAC;YACD,MAAO,IAAI,CAAC,UAAkB,CAAC,OAAO,EAAE,CAAC;QAC3C,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IACvE,CAAC;CACF;AAED,4BAA4B;AAC5B,CAAC;IACC,YAAY,CAAC;IACb,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IAElE,8BAA8B;IAC9B,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CACT,kEAAkE,CACnE,CAAC;QACF,OAAO;IACT,CAAC;IACD,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;IAEjC,iCAAiC;IACjC,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;IAElD,wCAAwC;IACvC,MAAc,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAEtD,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;AAC7E,CAAC,CAAC,EAAE,CAAC;AAEL,kCAAkC;AAClC,eAAe,iBAAiB,CAAC"}