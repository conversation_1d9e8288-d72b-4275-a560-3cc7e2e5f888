import BaseInterceptor from"./base-interceptor.js";class VideoFrameInterceptor extends BaseInterceptor{constructor(name="video-crop",options={}){const defaultConfig={debug:false,frameRate:30,enableCropping:true,...options};super(name,defaultConfig);this.subscribers=new Map;this.frameCount=0;this.lastFrameTime=0;this.log("VideoFrameInterceptor initialized",this.config)}async processVideoFrame(frame){this.frameCount++;const currentTime=performance.now();if(this.config.debug&&currentTime-this.lastFrameTime>1e3){this.log(`Processed ${this.frameCount} frames`);this.lastFrameTime=currentTime}let processedFrame=frame;if(this.config.enableCropping&&(this.config.cropRegion||this.config.defaultCropRegion)){processedFrame=this.applyCropping(frame)}if(this.subscribers.size>0){await this.notifySubscribers(processedFrame)}return processedFrame}applyCropping(frame){const{codedWidth:codedWidth,codedHeight:codedHeight}=frame;const cropRegion=this.config.cropRegion||this.config.defaultCropRegion;if(!cropRegion){return frame}const safeCropRegion={x:this.makeEven(Math.max(0,Math.min(cropRegion.x,codedWidth))),y:this.makeEven(Math.max(0,Math.min(cropRegion.y,codedHeight))),width:this.makeEven(Math.max(2,Math.min(cropRegion.width,codedWidth-cropRegion.x))),height:this.makeEven(Math.max(2,Math.min(cropRegion.height,codedHeight-cropRegion.y)))};try{const croppedFrame=new VideoFrame(frame,{visibleRect:safeCropRegion,displayWidth:safeCropRegion.width,displayHeight:safeCropRegion.height,timestamp:frame.timestamp,duration:frame.duration??undefined});this.log("Frame cropped:",safeCropRegion);return croppedFrame}catch(error){this.log("Cropping failed, using original frame:",error);return frame}}async notifySubscribers(frame){const promises=[];for(const[subscriberId,callback]of this.subscribers){try{const frameClone=new VideoFrame(frame,{timestamp:frame.timestamp,duration:frame.duration??undefined});const metadata={subscriberId:subscriberId,frameCount:this.frameCount,timestamp:performance.now(),cropRegion:this.config.cropRegion||this.config.defaultCropRegion||null};const result=callback(frameClone,metadata);if(result instanceof Promise){promises.push(result)}}catch(error){this.log(`Error notifying subscriber ${subscriberId}:`,error)}}if(promises.length>0){await Promise.allSettled(promises)}}subscribe(subscriberId,callback){if(typeof callback!=="function"){throw new Error("Callback must be a function")}this.subscribers.set(subscriberId,callback);this.log(`Subscriber added: ${subscriberId} (total: ${this.subscribers.size})`);return()=>this.unsubscribe(subscriberId)}unsubscribe(subscriberId){const removed=this.subscribers.delete(subscriberId);if(removed){this.log(`Subscriber removed: ${subscriberId} (remaining: ${this.subscribers.size})`)}return removed}setCropRegion(cropRegion){if(cropRegion&&typeof cropRegion==="object"){this.updateConfig({cropRegion:{x:this.makeEven(cropRegion.x||0),y:this.makeEven(cropRegion.y||0),width:this.makeEven(cropRegion.width||100),height:this.makeEven(cropRegion.height||100)}});this.log("Crop region updated:",this.config.cropRegion)}else{this.updateConfig({cropRegion:null});this.log("Crop region cleared")}}setCroppingEnabled(enabled){this.updateConfig({enableCropping:!!enabled});this.log(`Cropping ${this.config.enableCropping?"enabled":"disabled"}`)}getStatus(){return{...this.getMetadata(),enableCropping:this.config.enableCropping||false,cropRegion:this.config.cropRegion||this.config.defaultCropRegion||null,subscriberCount:this.subscribers.size,frameCount:this.frameCount,hasOriginalTrack:false,hasProcessedTrack:false}}async cleanup(){this.log("Cleaning up video interceptor...");this.subscribers.clear();await super.cleanup();this.log("Video interceptor cleanup complete")}makeEven(value){return Math.floor(value/2)*2}}export default VideoFrameInterceptor;if(typeof window!=="undefined"){window.VideoFrameInterceptor=VideoFrameInterceptor}