/**
 * Brightness Filter Interceptor
 *
 * A video frame interceptor that adjusts the brightness of video frames.
 * Demonstrates the multi-interceptor system with a simple brightness adjustment.
 *
 * Features:
 * - Real-time brightness adjustment
 * - Configurable brightness levels
 * - Extends BaseInterceptor for standardized interface
 * - Canvas-based frame processing
 */
import BaseInterceptor from "./base-interceptor.js";
import type { BrightnessConfig } from "./types/global.js";
declare class BrightnessInterceptor extends BaseInterceptor {
    private canvas;
    private ctx;
    private imageData;
    config: BrightnessConfig & {
        minBrightness: number;
        maxBrightness: number;
    };
    constructor(name?: string, options?: BrightnessConfig);
    /**
     * Handle configuration updates
     */
    private onConfigUpdate;
    /**
     * Process a video frame - implements BaseInterceptor interface
     */
    processVideoFrame(frame: VideoFrame): Promise<VideoFrame>;
    /**
     * Initialize canvas for frame processing
     */
    private initializeCanvas;
    /**
     * Set brightness level
     */
    setBrightness(brightness: number): void;
    /**
     * Get current brightness level
     */
    getBrightness(): number;
    /**
     * Override cleanup to handle canvas resources
     */
    cleanup(): Promise<void>;
    /**
     * Get interceptor-specific status
     */
    getStatus(): {
        name: string;
        type: string;
        isInitialized: boolean;
        isEnabled: boolean;
        config: BrightnessConfig;
        stats: any;
        brightness: number;
        canvasInitialized: boolean;
        canvasSize: string | null;
    };
}
export default BrightnessInterceptor;
//# sourceMappingURL=brightness-interceptor.d.ts.map