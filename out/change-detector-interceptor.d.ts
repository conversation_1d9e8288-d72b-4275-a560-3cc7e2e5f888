/**
 * Change Detector Interceptor
 *
 * A video frame interceptor that detects click events and
 * pauses the video stream when screen changes are detected, resuming only
 * after the screen has stabilized.
 *
 * Features:
 * - Click event detection
 * - Screen stability detection using pixelmatch
 * - Stream pause/resume functionality
 * - Configurable thresholds and stability duration
 * - Integration with established interceptor architecture
 */
import BaseInterceptor from "./base-interceptor.js";
import type { ChangeDetectorConfig } from "./types/global.js";
interface ControlTabManager {
    sendMessage(message: any): void;
}
declare class ChangeDetectorInterceptor extends BaseInterceptor {
    private canvas;
    private ctx;
    private isStreamPaused;
    private isMonitoring;
    private lastFrameData;
    private consecutiveStableCount;
    private maxWaitTimeout;
    private pendingFrames;
    private lastStableFrame;
    private controlTabManager;
    private triggeringWebClientId;
    private pauseStartTime;
    config: ChangeDetectorConfig & {
        comparisonInterval: number;
    };
    constructor(name?: string, options?: ChangeDetectorConfig);
    /**
     * Set the control tab manager reference for WebSocket messaging
     */
    setControlTabManager(controlTabManager: ControlTabManager): void;
    /**
     * Set the web client ID that triggered the change detection
     */
    setTriggeringWebClient(webClientId: string): void;
    /**
     * Send WebSocket notification to the triggering web client
     */
    private sendWebSocketNotification;
    /**
     * Process a video frame - implements BaseInterceptor interface
     */
    processVideoFrame(frame: VideoFrame): Promise<VideoFrame>;
    /**
     * Initialize canvas for frame processing
     */
    private initializeCanvas;
    /**
     * Compare two frames and return percentage difference
     */
    private compareFrames;
    /**
     * Pause the stream
     */
    private pauseStream;
    /**
     * Handle stable frame detection
     */
    private handleStableFrame;
    /**
     * Reset stability counter
     */
    private resetStability;
    /**
     * Resume the stream
     */
    private resumeStream;
    /**
     * Start monitoring for changes
     */
    startMonitoring(): void;
    /**
     * Stop monitoring for changes
     */
    stopMonitoring(): void;
    /**
     * Override cleanup to handle change detector resources
     */
    cleanup(): Promise<void>;
}
export default ChangeDetectorInterceptor;
//# sourceMappingURL=change-detector-interceptor.d.ts.map