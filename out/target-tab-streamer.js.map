{"version": 3, "file": "target-tab-streamer.js", "sourceRoot": "", "sources": ["../src/target-tab-streamer.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAQH,MAAM,iBAAiB;IAQrB,YACE,kBAA0B,EAC1B,KAAa,EACb,cAAgC;QAT1B,OAAE,GAAqB,IAAI,CAAC;QAE5B,gBAAW,GAAY,KAAK,CAAC;QAC7B,kBAAa,GAAuB,IAAI,CAAC;QACzC,6BAAwB,GAA6B,IAAI,CAAC;QAOhE,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,MAAM,IAAI,cAAc,KAAK,IAAI,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,cAAuB;QAChC,IAAI,CAAC;YACH,uCAAuC;YACvC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAExD,8BAA8B;YAC9B,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAEtC,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACjC,CAAC;YAED,OAAO,CAAC,GAAG,CACT,6DAA6D,CAC9D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CACX,sDAAsD,EACtD,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC;gBACH,IAAI,CAAC,EAAE,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAEjD,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG,EAAE;oBACpB,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;oBAC5D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBAExB,yBAAyB;oBACzB,IAAI,CAAC,WAAW,CAAC;wBACf,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,IAAI,CAAC,KAAK;qBAClB,CAAC,CAAC;oBAEH,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC;gBAEF,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE;oBAC5B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtD,CAAC,CAAC;gBAEF,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,GAAG,EAAE;oBACrB,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;oBACjE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBAC3B,CAAC,CAAC;gBAEF,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;oBAC1B,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;oBACxD,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,OAAY;QAC9B,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YACrD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,OAAY;QAC/C,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAExE,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,cAAc;gBACjB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC/B,MAAM;YACR,KAAK,aAAa;gBAChB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC9B,MAAM;YACR,KAAK,OAAO;gBACV,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAChC,MAAM;YACR,KAAK,eAAe;gBAClB,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACvC,MAAM;YACR;gBACE,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YAEzD,+CAA+C;YAC/C,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,eAAe,CAAC;gBAC1D,KAAK,EAAE;oBACL,WAAW,EAAE,QAAQ;oBACrB,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;oBACtB,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;oBACvB,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;iBAClB;gBACR,KAAK,EAAE,IAAI;gBACX,gBAAgB,EAAE,IAAI;aAChB,CAAC,CAAC;YAEV,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;YAE5B,yCAAyC;YACzC,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAExC,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;YAElE,0BAA0B;YAC1B,IAAI,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,gBAAgB;gBACtB,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACvE,IAAI,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YAEjD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;gBAChE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC5B,CAAC;YAED,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAClC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBACtC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;YACvC,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAE7C,IAAI,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,gBAAgB;gBACtB,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACtC,IAAI,CAAC,wBAAwB,GAAG,IAAI,iBAAiB,CAAC;YACpD,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,8BAA8B,EAAE,CAAC;SACvD,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC/C,IAAI,CAAC,wBAAyB,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,aAAc,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,wBAAwB,CAAC,cAAc,GAAG,CAAC,KAAK,EAAE,EAAE;YACvD,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACpB,IAAI,CAAC,WAAW,CAAC;oBACf,IAAI,EAAE,eAAe;oBACrB,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;QAEF,kCAAkC;QAClC,IAAI,CAAC,wBAAwB,CAAC,uBAAuB,GAAG,GAAG,EAAE;YAC3D,OAAO,CAAC,GAAG,CACT,8CAA8C,EAC9C,IAAI,CAAC,wBAAyB,CAAC,eAAe,CAC/C,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,OAAY;QACpC,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAC1C,CAAC;YAED,MAAM,IAAI,CAAC,wBAAyB,CAAC,oBAAoB,CACvD,IAAI,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,CACzC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAyB,CAAC,YAAY,EAAE,CAAC;YACnE,MAAM,IAAI,CAAC,wBAAyB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAEjE,IAAI,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAY;QAC3C,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,wBAAwB,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACvD,MAAM,IAAI,CAAC,wBAAwB,CAAC,eAAe,CACjD,IAAI,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CACvC,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAClC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;CACF;AAED,6CAA6C;AAC7C,CAAC,GAAG,EAAE;IACJ,YAAY,CAAC;IAEb,8BAA8B;IAC9B,IAAK,MAAc,CAAC,iBAAiB,EAAE,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAClE,OAAO;IACT,CAAC;IAED,0BAA0B;IACzB,MAAc,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAEtD,gDAAgD;IAChD,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC9D,MAAM,kBAAkB,GAAG,yBAAyB,CAAC;IACrD,MAAM,KAAK,GAAG,WAAW,CAAC;IAC1B,MAAM,cAAc,GAAG,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,OAAO,CAAC;IAElE,IAAI,kBAAkB,IAAI,KAAK,EAAE,CAAC;QAChC,IAAI,iBAAiB,CAAC,kBAAkB,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;IACnE,CAAC;AACH,CAAC,CAAC,EAAE,CAAC;AAEL,eAAe,iBAAiB,CAAC"}