import BaseInterceptor from"./base-interceptor.js";class ChangeDetectorInterceptor extends BaseInterceptor{constructor(name="change-detector",options={}){const defaultConfig={debug:false,enabled:true,changeThreshold:5,stabilityThreshold:1,consecutiveStableFrames:3,maxWaitDuration:5e3,pixelSampling:2,comparisonInterval:100,...options};super(name,defaultConfig);this.canvas=null;this.ctx=null;this.isStreamPaused=false;this.isMonitoring=false;this.lastFrameData=null;this.consecutiveStableCount=0;this.maxWaitTimeout=null;this.pendingFrames=[];this.lastStableFrame=null;this.controlTabManager=null;this.triggeringWebClientId=null;this.pauseStartTime=null;this.log("ChangeDetectorInterceptor initialized",this.config)}setControlTabManager(controlTabManager){this.controlTabManager=controlTabManager;this.log("Control tab manager reference set for WebSocket messaging")}setTriggeringWebClient(webClientId){this.triggeringWebClientId=webClientId;this.log(`Triggering web client set: ${webClientId}`)}sendWebSocketNotification(type,data={}){if(!this.controlTabManager||!this.triggeringWebClientId){this.log("Cannot send WebSocket notification - missing control tab manager or web client ID");return}const message={type:type,webClientId:this.triggeringWebClientId,timestamp:Date.now(),...data};this.log(`Sending WebSocket notification: ${type} to client ${this.triggeringWebClientId}`);this.controlTabManager.sendMessage(message)}async processVideoFrame(frame){try{if(!this.isMonitoring){return frame}if(!this.canvas||this.canvas.width!==frame.codedWidth||this.canvas.height!==frame.codedHeight){this.initializeCanvas(frame.codedWidth,frame.codedHeight)}if(!this.ctx){throw new Error("Canvas context not available")}this.ctx.drawImage(frame,0,0);const imageData=this.ctx.getImageData(0,0,this.canvas.width,this.canvas.height);const currentFrameData=imageData.data;if(this.lastFrameData){const changePercentage=this.compareFrames(currentFrameData,this.lastFrameData);if(this.config.debug){this.log(`Frame comparison: ${changePercentage.toFixed(2)}% change`)}if(changePercentage>(this.config.changeThreshold||5)&&!this.isStreamPaused){this.log(`📊 Significant change detected: ${changePercentage.toFixed(2)}% > ${this.config.changeThreshold}% - immediately pausing`);this.pauseStream();if(this.lastStableFrame){frame.close();return this.lastStableFrame}}else if(this.isStreamPaused&&changePercentage<=(this.config.stabilityThreshold||1)){this.handleStableFrame()}else if(this.isStreamPaused&&changePercentage>(this.config.stabilityThreshold||1)){this.resetStability()}}this.lastFrameData=new Uint8ClampedArray(currentFrameData);if(!this.isStreamPaused){if(this.lastStableFrame){this.lastStableFrame.close()}this.lastStableFrame=new VideoFrame(frame,{timestamp:frame.timestamp,duration:frame.duration??undefined})}return this.isStreamPaused&&this.lastStableFrame?this.lastStableFrame:frame}catch(error){this.log("Error in change detector:",error);return frame}}initializeCanvas(width,height){this.canvas=document.createElement("canvas");this.canvas.width=width;this.canvas.height=height;this.ctx=this.canvas.getContext("2d");if(!this.ctx){throw new Error("Failed to get 2D canvas context")}this.log(`Canvas initialized: ${width}x${height}`)}compareFrames(currentData,previousData){let diffPixels=0;const totalPixels=currentData.length/4;const sampling=this.config.pixelSampling||2;for(let i=0;i<currentData.length;i+=4*sampling){const rDiff=Math.abs(currentData[i]-previousData[i]);const gDiff=Math.abs(currentData[i+1]-previousData[i+1]);const bDiff=Math.abs(currentData[i+2]-previousData[i+2]);if(rDiff>30||gDiff>30||bDiff>30){diffPixels++}}return diffPixels/(totalPixels/sampling)*100}pauseStream(){this.isStreamPaused=true;this.pauseStartTime=Date.now();this.consecutiveStableCount=0;if(this.maxWaitTimeout){clearTimeout(this.maxWaitTimeout)}this.maxWaitTimeout=setTimeout(()=>{this.log("Maximum wait duration reached, resuming stream");this.resumeStream()},this.config.maxWaitDuration||5e3);this.sendWebSocketNotification("stream-paused",{reason:"change-detected",pauseStartTime:this.pauseStartTime})}handleStableFrame(){this.consecutiveStableCount++;this.log(`Stable frame ${this.consecutiveStableCount}/${this.config.consecutiveStableFrames}`);if(this.consecutiveStableCount>=(this.config.consecutiveStableFrames||3)){this.resumeStream()}}resetStability(){this.consecutiveStableCount=0}resumeStream(){const pauseDuration=this.pauseStartTime?Date.now()-this.pauseStartTime:0;this.isStreamPaused=false;this.consecutiveStableCount=0;this.pauseStartTime=null;if(this.maxWaitTimeout){clearTimeout(this.maxWaitTimeout);this.maxWaitTimeout=null}this.log(`Stream resumed after ${pauseDuration}ms`);this.sendWebSocketNotification("stream-resumed",{pauseDuration:pauseDuration,resumeTime:Date.now()})}startMonitoring(){this.isMonitoring=true;this.log("Change detection monitoring started")}stopMonitoring(){this.isMonitoring=false;this.isStreamPaused=false;this.consecutiveStableCount=0;if(this.maxWaitTimeout){clearTimeout(this.maxWaitTimeout);this.maxWaitTimeout=null}this.log("Change detection monitoring stopped")}async cleanup(){this.log("Cleaning up change detector interceptor...");this.stopMonitoring();if(this.canvas){this.canvas.width=0;this.canvas.height=0;this.canvas=null}this.ctx=null;this.lastFrameData=null;if(this.lastStableFrame){this.lastStableFrame.close();this.lastStableFrame=null}for(const frame of this.pendingFrames){frame.close()}this.pendingFrames=[];await super.cleanup();this.log("Change detector interceptor cleanup complete")}}export default ChangeDetectorInterceptor;if(typeof window!=="undefined"){window.ChangeDetectorInterceptor=ChangeDetectorInterceptor}