{"version": 3, "file": "change-detector-interceptor.js", "sourceRoot": "", "sources": ["../src/change-detector-interceptor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,OAAO,eAAe,MAAM,uBAAuB,CAAC;AAUpD,MAAM,yBAA0B,SAAQ,eAAe;IAyBrD,YACE,OAAe,iBAAiB,EAChC,UAAgC,EAAE;QAElC,wBAAwB;QACxB,MAAM,aAAa,GAAG;YACpB,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,IAAI;YACb,2BAA2B;YAC3B,eAAe,EAAE,CAAC,EAAE,qCAAqC;YACzD,kBAAkB,EAAE,CAAC,EAAE,uCAAuC;YAC9D,uBAAuB,EAAE,CAAC,EAAE,+CAA+C;YAC3E,eAAe,EAAE,IAAI,EAAE,qCAAqC;YAC5D,uBAAuB;YACvB,aAAa,EAAE,CAAC,EAAE,sCAAsC;YACxD,kBAAkB,EAAE,GAAG,EAAE,gCAAgC;YACzD,GAAG,OAAO;SACX,CAAC;QAEF,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QA3C7B,6CAA6C;QACrC,WAAM,GAA6B,IAAI,CAAC;QACxC,QAAG,GAAoC,IAAI,CAAC;QAEpD,wBAAwB;QAChB,mBAAc,GAAY,KAAK,CAAC;QAChC,iBAAY,GAAY,KAAK,CAAC;QAC9B,kBAAa,GAA6B,IAAI,CAAC;QAC/C,2BAAsB,GAAW,CAAC,CAAC;QACnC,mBAAc,GAA0B,IAAI,CAAC;QAErD,iBAAiB;QACT,kBAAa,GAAiB,EAAE,CAAC;QACjC,oBAAe,GAAsB,IAAI,CAAC;QAElD,sBAAsB;QACd,sBAAiB,GAA6B,IAAI,CAAC;QACnD,0BAAqB,GAAkB,IAAI,CAAC;QAC5C,mBAAc,GAAkB,IAAI,CAAC;QA2B3C,IAAI,CAAC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,iBAAoC;QACvD,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,WAAmB;QACxC,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC;QACzC,IAAI,CAAC,GAAG,CAAC,8BAA8B,WAAW,EAAE,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,yBAAyB,CAC/B,IAAY,EACZ,OAA4B,EAAE;QAE9B,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC3D,IAAI,CAAC,GAAG,CACN,mFAAmF,CACpF,CAAC;YACF,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,IAAI,CAAC,qBAAqB;YACvC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,GAAG,IAAI;SACR,CAAC;QAEF,IAAI,CAAC,GAAG,CACN,mCAAmC,IAAI,cAAc,IAAI,CAAC,qBAAqB,EAAE,CAClF,CAAC;QACF,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,KAAiB;QACvC,IAAI,CAAC;YACH,kCAAkC;YAClC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,8BAA8B;YAC9B,IACE,CAAC,IAAI,CAAC,MAAM;gBACZ,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,UAAU;gBACtC,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,WAAW,EACxC,CAAC;gBACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,0CAA0C;YAC1C,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAChC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CACrC,CAAC,EACD,CAAC,EACD,IAAI,CAAC,MAAO,CAAC,KAAK,EAClB,IAAI,CAAC,MAAO,CAAC,MAAM,CACpB,CAAC;YACF,MAAM,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC;YAExC,2CAA2C;YAC3C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CACzC,gBAAgB,EAChB,IAAI,CAAC,aAAa,CACnB,CAAC;gBAEF,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;oBACtB,IAAI,CAAC,GAAG,CAAC,qBAAqB,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBACvE,CAAC;gBAED,iFAAiF;gBACjF,IACE,gBAAgB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,CAAC,CAAC;oBACrD,CAAC,IAAI,CAAC,cAAc,EACpB,CAAC;oBACD,IAAI,CAAC,GAAG,CACN,mCAAmC,gBAAgB,CAAC,OAAO,CACzD,CAAC,CACF,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,yBAAyB,CAC7D,CAAC;oBACF,IAAI,CAAC,WAAW,EAAE,CAAC;oBAEnB,4DAA4D;oBAC5D,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;wBACzB,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,0BAA0B;wBACzC,OAAO,IAAI,CAAC,eAAe,CAAC;oBAC9B,CAAC;gBACH,CAAC;gBACD,iCAAiC;qBAC5B,IACH,IAAI,CAAC,cAAc;oBACnB,gBAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAC,EACzD,CAAC;oBACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,CAAC;gBACD,gEAAgE;qBAC3D,IACH,IAAI,CAAC,cAAc;oBACnB,gBAAgB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAC,EACxD,CAAC;oBACD,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,CAAC;YACH,CAAC;YAED,+CAA+C;YAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAE7D,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACzB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;gBAC/B,CAAC;gBACD,IAAI,CAAC,eAAe,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE;oBAC3C,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,SAAS;iBACtC,CAAC,CAAC;YACL,CAAC;YAED,6CAA6C;YAC7C,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe;gBAChD,CAAC,CAAC,IAAI,CAAC,eAAe;gBACtB,CAAC,CAAC,KAAK,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAa,EAAE,MAAc;QACpD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,uBAAuB,KAAK,IAAI,MAAM,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,aAAa,CACnB,WAA8B,EAC9B,YAA+B;QAE/B,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,2BAA2B;QACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC;QAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC;YAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAEjE,uEAAuE;YACvE,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBAC3C,UAAU,EAAE,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,CAAC,UAAU,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;QAEhC,2BAA2B;QAC3B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;YAC3D,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,yBAAyB,CAAC,eAAe,EAAE;YAC9C,MAAM,EAAE,iBAAiB;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,GAAG,CACN,gBAAgB,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CACrF,CAAC;QAEF,IACE,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB,IAAI,CAAC,CAAC,EACzE,CAAC;YACD,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc;YACvC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc;YAClC,CAAC,CAAC,CAAC,CAAC;QAEN,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,wBAAwB,aAAa,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,EAAE;YAC/C,aAAa;YACb,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,OAAO;QACpB,IAAI,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAEvD,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,4BAA4B;QAC5B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,4BAA4B;QAC5B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC9B,CAAC;QAED,0BAA0B;QAC1B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvC,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAExB,sBAAsB;QACtB,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QAEtB,IAAI,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAC3D,CAAC;CACF;AAED,kCAAkC;AAClC,eAAe,yBAAyB,CAAC;AAEzC,gDAAgD;AAChD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IACjC,MAAc,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;AACxE,CAAC"}