{"version": 3, "file": "change-detector-interceptor.d.ts", "sourceRoot": "", "sources": ["../src/change-detector-interceptor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,OAAO,eAAe,MAAM,uBAAuB,CAAC;AACpD,OAAO,KAAK,EACV,oBAAoB,EAErB,MAAM,mBAAmB,CAAC;AAE3B,UAAU,iBAAiB;IACzB,WAAW,CAAC,OAAO,EAAE,GAAG,GAAG,IAAI,CAAC;CACjC;AAED,cAAM,yBAA0B,SAAQ,eAAe;IAErD,OAAO,CAAC,MAAM,CAAkC;IAChD,OAAO,CAAC,GAAG,CAAyC;IAGpD,OAAO,CAAC,cAAc,CAAkB;IACxC,OAAO,CAAC,YAAY,CAAkB;IACtC,OAAO,CAAC,aAAa,CAAkC;IACvD,OAAO,CAAC,sBAAsB,CAAa;IAC3C,OAAO,CAAC,cAAc,CAA+B;IAGrD,OAAO,CAAC,aAAa,CAAoB;IACzC,OAAO,CAAC,eAAe,CAA2B;IAGlD,OAAO,CAAC,iBAAiB,CAAkC;IAC3D,OAAO,CAAC,qBAAqB,CAAuB;IACpD,OAAO,CAAC,cAAc,CAAuB;IAErC,MAAM,EAAE,oBAAoB,GAAG;QACrC,kBAAkB,EAAE,MAAM,CAAC;KAC5B,CAAC;gBAGA,IAAI,GAAE,MAA0B,EAChC,OAAO,GAAE,oBAAyB;IAsBpC;;OAEG;IACH,oBAAoB,CAAC,iBAAiB,EAAE,iBAAiB,GAAG,IAAI;IAKhE;;OAEG;IACH,sBAAsB,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAKjD;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAwBjC;;OAEG;IACG,iBAAiB,CAAC,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IAmG/D;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAaxB;;OAEG;IACH,OAAO,CAAC,aAAa;IAsBrB;;OAEG;IACH,OAAO,CAAC,WAAW;IAqBnB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAazB;;OAEG;IACH,OAAO,CAAC,cAAc;IAItB;;OAEG;IACH,OAAO,CAAC,YAAY;IAqBpB;;OAEG;IACH,eAAe,IAAI,IAAI;IAKvB;;OAEG;IACH,cAAc,IAAI,IAAI;IAatB;;OAEG;IACY,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;CAgCxC;AAGD,eAAe,yBAAyB,CAAC"}