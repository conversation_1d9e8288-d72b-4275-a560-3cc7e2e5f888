/**
 * Target Tab Streamer Script
 *
 * This script is injected into target tabs to automatically capture their content
 * and stream it via WebRTC. Uses preferCurrentTab: true for automatic capture.
 */
declare class TargetTabStreamer {
    private signalingServerUrl;
    private ws;
    private tabId;
    private isConnected;
    private captureStream;
    private controlTabPeerConnection;
    constructor(signalingServerUrl: string, tabId: string, autoInitialize: string | boolean);
    init(autoInitialize: boolean): Promise<void>;
    connectToSignalingServer(): Promise<void>;
    private sendMessage;
    private handleSignalingMessage;
    private handleStartStream;
    private handleStopStream;
    private createControlTabConnection;
    private handleOffer;
    private handleIceCandidate;
    cleanup(): void;
}
export default TargetTabStreamer;
//# sourceMappingURL=target-tab-streamer.d.ts.map