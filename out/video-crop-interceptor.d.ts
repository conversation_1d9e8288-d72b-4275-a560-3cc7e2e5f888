/**
 * Video Frame Interceptor
 *
 * A modular interceptor that sits between video track capture and WebRTC peer connection.
 * Provides frame cropping and subscription capabilities for real-time frame analysis.
 *
 * Features:
 * - Frame cropping with configurable crop regions
 * - Event-driven subscription API for frame listeners
 * - Toggle-able interceptor functionality
 * - Maintains existing streaming functionality
 * - Support for multiple frame subscribers
 * - Extends BaseInterceptor for standardized interface
 */
import BaseInterceptor from "./base-interceptor.js";
import type { VideoCropConfig } from "./types/global.js";
interface CropRegion {
    x: number;
    y: number;
    width: number;
    height: number;
}
interface SubscriberMetadata {
    subscriberId: string;
    frameCount: number;
    timestamp: number;
    cropRegion: CropRegion | null;
}
type FrameSubscriberCallback = (frame: VideoFrame, metadata: SubscriberMetadata) => void | Promise<void>;
declare class VideoFrameInterceptor extends BaseInterceptor {
    private subscribers;
    private frameCount;
    private lastFrameTime;
    config: VideoCropConfig;
    constructor(name?: string, options?: VideoCropConfig);
    /**
     * Process a video frame - implements BaseInterceptor interface
     */
    processVideoFrame(frame: VideoFrame): Promise<VideoFrame>;
    /**
     * Apply cropping to a video frame
     */
    private applyCropping;
    /**
     * Notify all subscribers with the processed frame
     */
    private notifySubscribers;
    /**
     * Subscribe to processed frames
     */
    subscribe(subscriberId: string, callback: FrameSubscriberCallback): () => boolean;
    /**
     * Unsubscribe from processed frames
     */
    unsubscribe(subscriberId: string): boolean;
    /**
     * Update crop region
     */
    setCropRegion(cropRegion: Partial<CropRegion> | null): void;
    /**
     * Enable or disable cropping functionality
     */
    setCroppingEnabled(enabled: boolean): void;
    /**
     * Get current interceptor status
     */
    getStatus(): {
        name: string;
        type: string;
        isInitialized: boolean;
        isEnabled: boolean;
        config: VideoCropConfig;
        stats: any;
        enableCropping: boolean;
        cropRegion: CropRegion | null;
        subscriberCount: number;
        frameCount: number;
        hasOriginalTrack: boolean;
        hasProcessedTrack: boolean;
    };
    /**
     * Override cleanup to handle video-specific resources
     */
    cleanup(): Promise<void>;
    /**
     * Utility function to ensure even numbers for YUV 4:2:0 alignment
     */
    private makeEven;
}
export default VideoFrameInterceptor;
//# sourceMappingURL=video-crop-interceptor.d.ts.map