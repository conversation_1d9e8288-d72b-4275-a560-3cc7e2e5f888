{"version": 3, "file": "base-interceptor.js", "sourceRoot": "", "sources": ["../src/base-interceptor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAQH,MAAe,eAAe;IAwB5B,YAAY,IAAY,EAAE,gBAAmC,EAAE;QArBxD,kBAAa,GAAY,KAAK,CAAC;QAC/B,cAAS,GAAY,IAAI,CAAC;QAIjC,8BAA8B;QACtB,cAAS,GAAqC,IAAI,CAAC;QACnD,cAAS,GAAqC,IAAI,CAAC;QACnD,oBAAe,GACrB,IAAI,CAAC;QAEP,mBAAmB;QACX,kBAAa,GAA4B,IAAI,CAAC;QAC9C,mBAAc,GAA4B,IAAI,CAAC;QASrD,IAAI,IAAI,CAAC,WAAW,KAAK,eAAe,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CACb,0EAA0E,CAC3E,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAElC,+CAA+C;QAC/C,IAAI,CAAC,MAAM,GAAG;YACZ,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,IAAI;YACb,GAAG,aAAa;SACjB,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,KAAK,GAAG;YACX,eAAe,EAAE,CAAC;YAClB,iBAAiB,EAAE,CAAC;YACpB,qBAAqB,EAAE,CAAC;YACxB,kBAAkB,EAAE,CAAC;YACrB,mBAAmB,EAAE,CAAC;SACvB,CAAC;QAEF,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,sBAAsB,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,UAA6B;QAC5C,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,2CAA2C,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;QAChC,IAAI,CAAC,GAAG,CACN,gBAAgB,IAAI,CAAC,IAAI,gCAAgC,EACzD,UAAU,CAAC,KAAK,CACjB,CAAC;QAEF,IAAI,CAAC;YACH,iCAAiC;YACjC,IAAI,CAAC,SAAS,GAAG,IAAI,yBAAyB,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;YACtE,IAAI,CAAC,SAAS,GAAG,IAAI,yBAAyB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;YAElE,yDAAyD;YACzD,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC;gBACzC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;aACxC,CAAC,CAAC;YAEH,uBAAuB;YACvB,IAAI,CAAC,SAAS,CAAC,QAAQ;iBACpB,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;iBACjC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;iBAC/B,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBACnD,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YACjC,CAAC,CAAC,CAAC;YAEL,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;YAC3C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAE1B,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,uCAAuC,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,sBAAsB,IAAI,CAAC,IAAI,eAAe,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC/B,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAChB,KAAiB,EACjB,UAAwD;QAExD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAE7B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC5C,0CAA0C;gBAC1C,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC1B,OAAO;YACT,CAAC;YAED,mDAAmD;YACnD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE3D,8BAA8B;YAC9B,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAEnC,mDAAmD;YACnD,IAAI,cAAc,KAAK,KAAK,EAAE,CAAC;gBAC7B,KAAK,CAAC,KAAK,EAAE,CAAC;YAChB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3D,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAE/B,wCAAwC;YACxC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;gBAAS,CAAC;YACT,2BAA2B;YAC3B,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACrD,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,cAAc,CAAC;YAC/C,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,cAAc,CAAC;YACjD,IAAI,CAAC,KAAK,CAAC,qBAAqB;gBAC9B,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;QAChE,CAAC;IACH,CAAC;IAOD;;OAEG;IACH,YAAY,CAAC,SAAqC;QAChD,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YAChD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,gDAAgD,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;QAE/C,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,yBAAyB,EAAE;YAC9C,GAAG,EAAE,SAAS;YACd,GAAG,EAAE,IAAI,CAAC,MAAM;SACjB,CAAC,CAAC;QAEH,mDAAmD;QACnD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,sBAAsB,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,uBAAuB,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,KAAK,GAAG;YACX,eAAe,EAAE,CAAC;YAClB,iBAAiB,EAAE,CAAC;YACpB,qBAAqB,EAAE,CAAC;YACxB,kBAAkB,EAAE,CAAC;YACrB,mBAAmB,EAAE,CAAC;SACvB,CAAC;QACF,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,cAAc,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACxB,CAAC;YAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACxB,CAAC;YAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC9B,CAAC;YAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC5B,CAAC;YAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC7B,CAAC;YAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,yBAAyB,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,IAAI,eAAe,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,GAAG,CAAC,GAAG,IAAW;QAChB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,eAAe,EAAE,GAAG,IAAI,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW;QAQT,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;YACxB,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;SACvB,CAAC;IACJ,CAAC;CACF;AAED,kCAAkC;AAClC,eAAe,eAAe,CAAC;AAE/B,gDAAgD;AAChD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IACjC,MAAc,CAAC,eAAe,GAAG,eAAe,CAAC;AACpD,CAAC"}