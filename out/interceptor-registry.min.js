class InterceptorRegistry{constructor(){this.interceptorClasses=new Map;this.defaultConfigs=new Map;this.clientInterceptors=new Map;this.clientConfigs=new Map;this.log("InterceptorRegistry initialized")}register(name,interceptorClass,defaultConfig={}){if(!name||typeof name!=="string"){throw new Error("Interceptor name must be a non-empty string")}if(!interceptorClass||typeof interceptorClass!=="function"){throw new Error("Interceptor class must be a constructor function")}this.interceptorClasses.set(name,interceptorClass);this.defaultConfigs.set(name,defaultConfig);this.log(`Registered interceptor: ${name}`)}unregister(name){if(this.interceptorClasses.has(name)){this.interceptorClasses.delete(name);this.defaultConfigs.delete(name);this.log(`Unregistered interceptor: ${name}`)}}getRegisteredInterceptors(){return Array.from(this.interceptorClasses.keys())}isRegistered(name){return this.interceptorClasses.has(name)}create(name,config){const InterceptorClass=this.interceptorClasses.get(name);if(!InterceptorClass){this.log(`Warning: Interceptor class not found for ${name}`);return null}const defaultConfig=this.defaultConfigs.get(name)||{};const finalConfig={...defaultConfig,...config};try{return new InterceptorClass(name,finalConfig)}catch(error){this.log(`Error creating interceptor ${name}:`,error);return null}}setClientConfiguration(webClientId,interceptorNames=[],configs={}){for(const name of interceptorNames){if(!this.interceptorClasses.has(name)){throw new Error(`Unknown interceptor: ${name}`)}}const clientConfig={interceptorNames:[...interceptorNames],configs:new Map};for(const name of interceptorNames){const defaultConfig=this.defaultConfigs.get(name)||{};const userConfig=configs[name]||{};clientConfig.configs.set(name,{...defaultConfig,...userConfig})}this.clientConfigs.set(webClientId,clientConfig);this.log(`Set configuration for client ${webClientId}:`,interceptorNames)}getClientConfiguration(webClientId){const config=this.clientConfigs.get(webClientId);if(!config){return{interceptorNames:[],configs:new Map}}return{interceptorNames:[...config.interceptorNames],configs:new Map(config.configs)}}createClientInterceptors(webClientId){if(this.clientInterceptors.has(webClientId)){this.log(`Interceptors already exist for client ${webClientId}, returning existing instances`);const existingInterceptors=this.clientInterceptors.get(webClientId);return Array.from(existingInterceptors.values())}const clientConfig=this.getClientConfiguration(webClientId);const interceptors=[];const interceptorMap=new Map;try{for(const name of clientConfig.interceptorNames){const InterceptorClass=this.interceptorClasses.get(name);const config=clientConfig.configs.get(name)||{};if(!InterceptorClass){this.log(`Warning: Interceptor class not found for ${name}, skipping`);continue}const interceptor=new InterceptorClass(name,config);interceptors.push(interceptor);interceptorMap.set(name,interceptor);this.log(`Created ${name} interceptor for client ${webClientId}`)}this.clientInterceptors.set(webClientId,interceptorMap);return interceptors}catch(error){for(const interceptor of interceptors){try{interceptor.cleanup()}catch(cleanupError){this.log(`Error cleaning up interceptor during creation failure:`,cleanupError)}}throw new Error(`Failed to create interceptors for client ${webClientId}: ${error instanceof Error?error.message:String(error)}`)}}getClientInterceptors(webClientId){const interceptorMap=this.clientInterceptors.get(webClientId);if(!interceptorMap){return[]}const clientConfig=this.getClientConfiguration(webClientId);return clientConfig.interceptorNames.map(name=>interceptorMap.get(name)).filter(interceptor=>interceptor!==undefined)}updateClientInterceptorConfig(webClientId,interceptorName,newConfig){const clientConfig=this.clientConfigs.get(webClientId);if(!clientConfig){throw new Error(`No configuration found for client: ${webClientId}`)}const currentConfig=clientConfig.configs.get(interceptorName)||{};clientConfig.configs.set(interceptorName,{...currentConfig,...newConfig});const interceptorMap=this.clientInterceptors.get(webClientId);if(interceptorMap&&interceptorMap.has(interceptorName)){const interceptor=interceptorMap.get(interceptorName);interceptor.updateConfig(newConfig)}this.log(`Updated ${interceptorName} config for client ${webClientId}`)}cleanupClientInterceptors(webClientId){const interceptorMap=this.clientInterceptors.get(webClientId);if(interceptorMap){for(const[name,interceptor]of interceptorMap){try{interceptor.cleanup();this.log(`Cleaned up ${name} interceptor for client ${webClientId}`)}catch(error){this.log(`Error cleaning up ${name} interceptor:`,error)}}this.clientInterceptors.delete(webClientId)}this.clientConfigs.delete(webClientId);this.log(`Cleaned up all interceptors for client ${webClientId}`)}getStats(){const stats={registeredInterceptors:this.interceptorClasses.size,activeClients:this.clientInterceptors.size,totalActiveInterceptors:0,clientStats:{}};for(const[webClientId,interceptorMap]of this.clientInterceptors){const clientInterceptors=Array.from(interceptorMap.values());stats.totalActiveInterceptors+=clientInterceptors.length;stats.clientStats[webClientId]={interceptorCount:clientInterceptors.length,interceptors:clientInterceptors.map(i=>({name:i.name,type:i.type,isEnabled:i.isEnabled,stats:i.getStats()}))}}return stats}log(...args){console.log("[InterceptorRegistry]",...args)}}const interceptorRegistry=new InterceptorRegistry;export{InterceptorRegistry,interceptorRegistry};export default interceptorRegistry;if(typeof window!=="undefined"){window.InterceptorRegistry=InterceptorRegistry;window.interceptorRegistry=interceptorRegistry}