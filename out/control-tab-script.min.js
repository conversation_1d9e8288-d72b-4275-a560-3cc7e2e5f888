class ControlTabManager{constructor(){this.signalingServerUrl="ws://localhost:8080";this.websocket=null;this.isConnected=false;this.rtcConfig={iceServers:[{urls:"stun:stun.cloudflare.com:3478"},{urls:"stun:stun.l.google.com:19302"}],iceCandidatePoolSize:10};this.webClientGroups=new Map;this.tabGroups=new Map;this.targetConnections=new Map;this.clientInterceptorConfigs=new Map;this.defaultInterceptorConfig={interceptorNames:["change-detector","brightness-filter","blur-effect","video-crop"],interceptorConfigs:{"video-crop":{enabled:true,enableCropping:true,cropRegion:{x:0,y:0,width:window.innerWidth,height:window.innerHeight}},"change-detector":{enabled:false,changeThreshold:5,stabilityThreshold:1,consecutiveStableFrames:3,maxWaitDuration:5e3,comparisonInterval:100,pixelSampling:2},"brightness-filter":{enabled:true,brightness:1.2},"blur-effect":{enabled:true,blurRadius:3}}};this.cdpManager=null;this.init()}async init(){console.log("[POC-Streaming] Initializing control tab manager...");this.initializeInterceptorRegistry();await this.initializeCDPManager();await this.connectToSignalingServer();console.log("[POC-Streaming] Control tab manager initialized successfully")}async initializeCDPManager(){try{if(typeof window.CDPManager==="undefined"){throw new Error("CDPManager not available - ensure CDP manager is injected first")}this.cdpManager=window.CDPManager;if(this.cdpManager&&typeof this.cdpManager.initializeDefaultHandlers==="function"){this.cdpManager.initializeDefaultHandlers()}console.log("[POC-Streaming] CDP Manager initialized successfully")}catch(error){console.error("[POC-Streaming] Failed to initialize CDP Manager:",error);throw error}}initializeInterceptorRegistry(){if(typeof window.interceptorRegistry!=="undefined"){if(typeof window.VideoFrameInterceptor!=="undefined"){window.interceptorRegistry.register("video-crop",window.VideoFrameInterceptor,{debug:true,enableCropping:true})}if(typeof window.BrightnessInterceptor!=="undefined"){window.interceptorRegistry.register("brightness-filter",window.BrightnessInterceptor,{debug:true,brightness:1})}if(typeof window.BlurInterceptor!=="undefined"){window.interceptorRegistry.register("blur-effect",window.BlurInterceptor,{debug:true,blurRadius:0})}if(typeof window.ChangeDetectorInterceptor!=="undefined"){window.interceptorRegistry.register("change-detector",window.ChangeDetectorInterceptor,{debug:true,enabled:false,changeThreshold:5,stabilityThreshold:1,consecutiveStableFrames:3,maxWaitDuration:5e3,comparisonInterval:100,pixelSampling:2})}console.log("[POC-Streaming] Interceptor registry initialized with:",window.interceptorRegistry.getRegisteredInterceptors())}else{console.warn("[POC-Streaming] Interceptor registry not available")}}async connectToSignalingServer(){return new Promise((resolve,reject)=>{try{this.websocket=new WebSocket(this.signalingServerUrl);this.websocket.onopen=()=>{console.log("[POC-Streaming] Connected to signaling server");this.isConnected=true;resolve()};this.websocket.onmessage=event=>{this.handleSignalingMessage(JSON.parse(event.data))};this.websocket.onclose=()=>{console.log("[POC-Streaming] Disconnected from signaling server");this.isConnected=false};this.websocket.onerror=error=>{console.error("[POC-Streaming] WebSocket error:",error);reject(error)}}catch(error){console.error("[POC-Streaming] Failed to connect to signaling server:",error);reject(error)}})}handleSignalingMessage(message){console.log("[POC-Streaming] Received signaling message:",message);switch(message.type){case"web-client-offer":this.handleWebClientOffer(message);break;case"web-client-ice-candidate":this.handleWebClientIceCandidate(message);break;case"user-event":this.handleUserEvent(message);break;case"interceptor-config-update":this.handleInterceptorConfigUpdate(message);break;default:console.warn("[POC-Streaming] Unknown message type:",message.type)}}async handleWebClientOffer(message){try{const{webClientId:webClientId,offer:offer}=message;console.log(`[POC-Streaming] Handling offer from web client: ${webClientId}`);const peerConnection=new RTCPeerConnection(this.rtcConfig);const dataChannel=peerConnection.createDataChannel("control",{ordered:true});this.webClientGroups.set(webClientId,{peerConnection:peerConnection,interceptorPipeline:null,dataChannel:dataChannel,webClient:{id:webClientId}});await peerConnection.setRemoteDescription(offer);const answer=await peerConnection.createAnswer();await peerConnection.setLocalDescription(answer);this.sendMessage({type:"control-tab-answer",webClientId:webClientId,answer:answer});console.log(`[POC-Streaming] Answer sent to web client: ${webClientId}`)}catch(error){console.error("[POC-Streaming] Error handling web client offer:",error)}}async handleWebClientIceCandidate(message){try{const{webClientId:webClientId,candidate:candidate}=message;const webClientGroup=this.webClientGroups.get(webClientId);if(webClientGroup){await webClientGroup.peerConnection.addIceCandidate(candidate);console.log(`[POC-Streaming] ICE candidate added for web client: ${webClientId}`)}}catch(error){console.error("[POC-Streaming] Error handling ICE candidate:",error)}}async handleUserEvent(message){try{const{userEvent:userEvent,targetTabId:targetTabId}=message;if(this.cdpManager&&typeof this.cdpManager.handleUserEvent==="function"){await this.cdpManager.handleUserEvent(userEvent,targetTabId);console.log(`[POC-Streaming] User event handled for tab: ${targetTabId}`)}else{console.warn("[POC-Streaming] CDP Manager not available for user event handling")}}catch(error){console.error("[POC-Streaming] Error handling user event:",error)}}handleInterceptorConfigUpdate(message){try{const{webClientId:webClientId,interceptorConfigs:interceptorConfigs}=message;console.log(`[POC-Streaming] Updating interceptor config for client: ${webClientId}`);this.clientInterceptorConfigs.set(webClientId,interceptorConfigs);const webClientGroup=this.webClientGroups.get(webClientId);if(webClientGroup&&webClientGroup.interceptorPipeline){for(const[name,config]of Object.entries(interceptorConfigs)){webClientGroup.interceptorPipeline.updateInterceptorConfig(name,config)}}console.log(`[POC-Streaming] Interceptor config updated for client: ${webClientId}`)}catch(error){console.error("[POC-Streaming] Error updating interceptor config:",error)}}sendMessage(message){if(this.websocket&&this.isConnected){this.websocket.send(JSON.stringify(message))}else{console.warn("[POC-Streaming] Cannot send message - not connected to signaling server")}}async cleanup(){console.log("[POC-Streaming] Cleaning up control tab manager...");for(const[webClientId,group]of this.webClientGroups){try{if(group.interceptorPipeline){await group.interceptorPipeline.cleanup()}group.peerConnection.close()}catch(error){console.error(`[POC-Streaming] Error cleaning up web client ${webClientId}:`,error)}}for(const[tabId,connection]of this.targetConnections){try{connection.close()}catch(error){console.error(`[POC-Streaming] Error cleaning up target connection ${tabId}:`,error)}}if(this.websocket){this.websocket.close()}if(this.cdpManager&&typeof this.cdpManager.cleanup==="function"){await this.cdpManager.cleanup()}console.log("[POC-Streaming] Control tab manager cleanup completed")}}(function(){"use strict";console.log("[POC-Streaming] Control tab script initializing...");if(window.controlTabInjected){console.log("[POC-Streaming] Control tab script already injected, skipping...");return}window.controlTabInjected=true;const controlTabManager=new ControlTabManager;window.controlTabManager=controlTabManager;console.log("[POC-Streaming] Control tab script initialized successfully")})();export default ControlTabManager;