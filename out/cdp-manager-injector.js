/**
 * CDP Manager Injector Script
 *
 * This script injects the CDPManager into the control tab
 * It should be injected before the control-tab-script.js
 */
class CDP {
    constructor(targetInfo) {
        this.ws = null;
        this.messageId = 0;
        this.pendingMessages = new Map();
        this.targetInfo = targetInfo;
    }
    async connect() {
        if (this.ws)
            return;
        this.ws = new WebSocket(this.targetInfo.webSocketDebuggerUrl);
        return new Promise((resolve, reject) => {
            if (!this.ws)
                return reject(new Error("WebSocket not initialized"));
            this.ws.onopen = () => {
                console.log(`Connected to target: ${this.targetInfo.title}`);
                resolve();
            };
            this.ws.onerror = reject;
            this.ws.onmessage = (event) => {
                const message = JSON.parse(event.data);
                if (message.id && this.pendingMessages.has(message.id)) {
                    const { resolve, reject } = this.pendingMessages.get(message.id);
                    this.pendingMessages.delete(message.id);
                    if (message.error) {
                        reject(new Error(message.error.message));
                    }
                    else {
                        resolve(message.result);
                    }
                }
            };
        });
    }
    async send(method, params = {}, sessionId = null) {
        if (!this.ws) {
            await this.connect();
        }
        const id = ++this.messageId;
        const message = { id, method, params };
        if (sessionId) {
            message.sessionId = sessionId;
        }
        return new Promise((resolve, reject) => {
            this.pendingMessages.set(id, { resolve, reject });
            this.ws.send(JSON.stringify(message));
        });
    }
    // Runtime domain
    get Runtime() {
        return {
            enable: (params = {}, sessionId = null) => this.send("Runtime.enable", params, sessionId),
            evaluate: (params, sessionId = null) => this.send("Runtime.evaluate", params, sessionId),
        };
    }
    // Target domain
    get Target() {
        return {
            getTargets: (params = {}, sessionId = null) => this.send("Target.getTargets", params, sessionId),
            createTarget: (params, sessionId = null) => this.send("Target.createTarget", params, sessionId),
            attachToTarget: (params, sessionId = null) => this.send("Target.attachToTarget", params, sessionId),
            closeTarget: (params, sessionId = null) => this.send("Target.closeTarget", params, sessionId),
            activateTarget: (params, sessionId = null) => this.send("Target.activateTarget", params, sessionId),
        };
    }
    // Input domain
    get Input() {
        return {
            dispatchKeyEvent: (params, sessionId = null) => this.send("Input.dispatchKeyEvent", params, sessionId),
            dispatchMouseEvent: (params, sessionId = null) => this.send("Input.dispatchMouseEvent", params, sessionId),
        };
    }
    async close() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
}
/**
 * CDP Manager - High-level API for CDP operations
 */
class CDPManager {
    constructor(options = {}) {
        this.connections = new Map();
        this.eventHandlers = new Map();
        this.options = {
            debugPort: options.debugPort || 9222,
            debug: options.debug || false,
        };
        this.log("[CDP-Manager] Initialized");
    }
    /**
     * Log messages with CDP-Manager prefix
     */
    log(message, ...args) {
        if (this.options.debug) {
            console.log(`[CDP-Manager] ${message}`, ...args);
        }
    }
    /**
     * Add a new CDP connection to a target tab
     */
    async addConnection(targetTabId, targetInfo = null) {
        try {
            if (this.connections.has(targetTabId)) {
                return this.connections.get(targetTabId);
            }
            if (!targetInfo) {
                targetInfo = await this.getTargetInfo(targetTabId);
            }
            if (!targetInfo) {
                throw new Error(`Target tab ${targetTabId} not found`);
            }
            const client = new CDP(targetInfo);
            await client.connect();
            const attachResult = await client.Target.attachToTarget({
                targetId: targetTabId,
                flatten: true,
            });
            const sessionId = attachResult.sessionId;
            const connection = {
                client,
                sessionId,
                targetInfo,
                createdAt: Date.now(),
            };
            this.connections.set(targetTabId, connection);
            this.log(`CDP connection established for tab: ${targetTabId}`);
            return connection;
        }
        catch (error) {
            this.log(`Failed to add connection to tab ${targetTabId}:`, error);
            throw error;
        }
    }
    /**
     * Remove a CDP connection
     */
    async removeConnection(targetTabId) {
        try {
            const connection = this.connections.get(targetTabId);
            if (!connection) {
                return;
            }
            await connection.client.close();
            this.connections.delete(targetTabId);
            this.log(`CDP connection removed for tab: ${targetTabId}`);
        }
        catch (error) {
            this.log(`Failed to remove connection from tab ${targetTabId}:`, error);
            throw error;
        }
    }
    /**
     * Get connection info for a target tab
     */
    getConnection(targetTabId) {
        return this.connections.get(targetTabId) || null;
    }
    /**
     * Get all active connections
     */
    getAllConnections() {
        return new Map(this.connections);
    }
    /**
     * Execute a command on a target tab
     */
    async executeCommand(targetTabId, method, params = {}) {
        try {
            const connection = this.connections.get(targetTabId);
            if (!connection) {
                throw new Error(`No connection found for tab: ${targetTabId}`);
            }
            // Parse method to determine domain and method
            const [domain, methodName] = method.split(".");
            const domainObj = connection.client[domain];
            if (!domainObj) {
                throw new Error(`Unsupported domain: ${domain}`);
            }
            const result = await domainObj[methodName](params, connection.sessionId);
            return result;
        }
        catch (error) {
            this.log(`Failed to execute ${method} on tab ${targetTabId}:`, error);
            throw error;
        }
    }
    /**
     * Get target tab information
     */
    async getTargetTabInfo(targetTabId) {
        try {
            const result = await this.executeCommand(targetTabId, "Runtime.evaluate", {
                expression: `({
          width: window.innerWidth,
          height: window.innerHeight,
          url: window.location.href,
          title: document.title
        })`,
                returnByValue: true,
                awaitPromise: true,
            });
            return result.result.value;
        }
        catch (error) {
            this.log(`Failed to get tab info for ${targetTabId}:`, error);
            // Return default values as fallback
            return { width: 1920, height: 1080, url: "unknown", title: "Unknown" };
        }
    }
    /**
     * Execute JavaScript on a target tab
     */
    async executeScript(targetTabId, script) {
        try {
            const result = await this.executeCommand(targetTabId, "Runtime.evaluate", {
                expression: script,
                returnByValue: true,
            });
            return result.result.value;
        }
        catch (error) {
            this.log(`Failed to execute script on tab ${targetTabId}:`, error);
            return null;
        }
    }
    /**
     * Register an event handler for user events
     */
    registerEventHandler(eventType, handler) {
        this.eventHandlers.set(eventType, handler);
    }
    /**
     * Unregister an event handler
     */
    unregisterEventHandler(eventType) {
        this.eventHandlers.delete(eventType);
    }
    /**
     * Handle a user event by dispatching it to the appropriate target tab
     */
    async handleUserEvent(userEvent, targetTabId) {
        try {
            const handler = this.eventHandlers.get(userEvent.eventType);
            if (!handler) {
                this.log(`No handler registered for event type: ${userEvent.eventType}`);
                return;
            }
            await handler(userEvent, targetTabId);
        }
        catch (error) {
            this.log(`Failed to handle user event on tab ${targetTabId}:`, error);
            throw error;
        }
    }
    /**
     * Handle click events
     */
    async handleClickEvent(userEvent, targetTabId) {
        try {
            const tabInfo = await this.getTargetTabInfo(targetTabId);
            if (!tabInfo) {
                throw new Error("Could not get target tab info");
            }
            const targetX = userEvent.x * tabInfo.width;
            const targetY = userEvent.y * tabInfo.height;
            await this.executeCommand(targetTabId, "Input.dispatchMouseEvent", {
                type: "mousePressed",
                x: Math.round(targetX),
                y: Math.round(targetY),
                button: "left",
                clickCount: 1,
                buttons: 1,
            });
            await new Promise((resolve) => setTimeout(resolve, 50));
            await this.executeCommand(targetTabId, "Input.dispatchMouseEvent", {
                type: "mouseReleased",
                x: Math.round(targetX),
                y: Math.round(targetY),
                button: "left",
                clickCount: 1,
                buttons: 0,
            });
        }
        catch (error) {
            this.log(`Failed to handle click event on tab ${targetTabId}:`, error);
            throw error;
        }
    }
    /**
     * Handle scroll events
     */
    async handleScrollEvent(userEvent, targetTabId) {
        try {
            const tabInfo = await this.getTargetTabInfo(targetTabId);
            if (!tabInfo) {
                throw new Error("Could not get target tab info");
            }
            const targetX = userEvent.x * tabInfo.width;
            const targetY = userEvent.y * tabInfo.height;
            await this.executeCommand(targetTabId, "Input.dispatchMouseEvent", {
                type: "mouseWheel",
                x: Math.round(targetX),
                y: Math.round(targetY),
                deltaX: userEvent.deltaX || 0,
                deltaY: userEvent.deltaY || 0,
            });
        }
        catch (error) {
            this.log(`Failed to handle scroll event on tab ${targetTabId}:`, error);
            throw error;
        }
    }
    /**
     * Handle key events
     */
    async handleKeyEvent(userEvent, targetTabId) {
        try {
            await this.executeCommand(targetTabId, "Input.dispatchKeyEvent", {
                type: userEvent.keyType || "keyDown",
                key: userEvent.key,
                text: userEvent.text,
                code: userEvent.code,
                keyCode: userEvent.keyCode,
            });
        }
        catch (error) {
            this.log(`Failed to handle key event on tab ${targetTabId}:`, error);
            throw error;
        }
    }
    /**
     * Get target info from CDP debug port
     */
    async getTargetInfo(targetTabId) {
        try {
            const response = await fetch(`http://localhost:${this.options.debugPort}/json`);
            const targets = await response.json();
            return targets.find((tab) => tab.id === targetTabId) || null;
        }
        catch (error) {
            this.log(`Failed to get target info for ${targetTabId}:`, error);
            return null;
        }
    }
    /**
     * Initialize default event handlers
     */
    initializeDefaultHandlers() {
        this.registerEventHandler("click", this.handleClickEvent.bind(this));
        this.registerEventHandler("scroll", this.handleScrollEvent.bind(this));
        this.registerEventHandler("keydown", this.handleKeyEvent.bind(this));
        this.registerEventHandler("keyup", this.handleKeyEvent.bind(this));
        this.registerEventHandler("keypress", this.handleKeyEvent.bind(this));
    }
    /**
     * Clean up all connections
     */
    async cleanup() {
        const cleanupPromises = Array.from(this.connections.keys()).map((targetTabId) => this.removeConnection(targetTabId));
        await Promise.all(cleanupPromises);
        this.eventHandlers.clear();
        this.log("CDP Manager cleanup completed");
    }
    /**
     * Get connection statistics
     */
    getStats() {
        return {
            totalConnections: this.connections.size,
            eventHandlers: this.eventHandlers.size,
            connections: Array.from(this.connections.entries()).map(([tabId, conn]) => ({
                tabId,
                sessionId: conn.sessionId,
                createdAt: conn.createdAt,
                targetInfo: conn.targetInfo,
            })),
        };
    }
}
// Export for use in other modules
export { CDPManager, CDP };
export default CDPManager;
// Create global instance for injection scripts
const cdpManager = new CDPManager({
    debug: true,
});
// Make available globally for injection scripts
if (typeof window !== "undefined") {
    window.CDPManager = cdpManager;
    window.CDP = CDP;
}
//# sourceMappingURL=cdp-manager-injector.js.map