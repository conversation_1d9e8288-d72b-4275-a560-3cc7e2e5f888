/**
 * Control Tab Script for POC Streaming
 *
 * This script is injected into the control tab to manage WebRTC connections
 * and coordinate streaming between target tabs and web clients
 */
class ControlTabManager {
    constructor() {
        this.signalingServerUrl = "ws://localhost:8080";
        this.websocket = null;
        this.isConnected = false;
        // WebRTC configuration
        this.rtcConfig = {
            iceServers: [
                { urls: "stun:stun.cloudflare.com:3478" },
                { urls: "stun:stun.l.google.com:19302" },
            ],
            iceCandidatePoolSize: 10,
        };
        // Connection management
        this.webClientGroups = new Map();
        this.tabGroups = new Map();
        this.targetConnections = new Map();
        this.clientInterceptorConfigs = new Map();
        // Default interceptor configuration for new clients
        this.defaultInterceptorConfig = {
            interceptorNames: [
                "change-detector",
                "brightness-filter",
                "blur-effect",
                "video-crop",
            ],
            interceptorConfigs: {
                "video-crop": {
                    enabled: true,
                    enableCropping: true,
                    cropRegion: {
                        x: 0,
                        y: 0,
                        width: window.innerWidth,
                        height: window.innerHeight,
                    },
                },
                "change-detector": {
                    enabled: false,
                    changeThreshold: 5,
                    stabilityThreshold: 1,
                    consecutiveStableFrames: 3,
                    maxWaitDuration: 5000,
                    comparisonInterval: 100,
                    pixelSampling: 2,
                },
                "brightness-filter": {
                    enabled: true,
                    brightness: 1.2,
                },
                "blur-effect": {
                    enabled: true,
                    blurRadius: 3,
                },
            },
        };
        // CDP connection management
        this.cdpManager = null;
        this.init();
    }
    async init() {
        console.log("[POC-Streaming] Initializing control tab manager...");
        // Initialize interceptor registry and register available interceptors
        this.initializeInterceptorRegistry();
        // Initialize CDP Manager
        await this.initializeCDPManager();
        // Connect to signaling server
        await this.connectToSignalingServer();
        console.log("[POC-Streaming] Control tab manager initialized successfully");
    }
    /**
     * Initialize CDP Manager
     */
    async initializeCDPManager() {
        try {
            if (typeof window.CDPManager === "undefined") {
                throw new Error("CDPManager not available - ensure CDP manager is injected first");
            }
            this.cdpManager = window.CDPManager;
            // Initialize default event handlers if available
            if (this.cdpManager &&
                typeof this.cdpManager.initializeDefaultHandlers === "function") {
                this.cdpManager.initializeDefaultHandlers();
            }
            console.log("[POC-Streaming] CDP Manager initialized successfully");
        }
        catch (error) {
            console.error("[POC-Streaming] Failed to initialize CDP Manager:", error);
            throw error;
        }
    }
    /**
     * Initialize the interceptor registry with available interceptors
     */
    initializeInterceptorRegistry() {
        // Register available interceptor types
        if (typeof window.interceptorRegistry !== "undefined") {
            // Register VideoFrameInterceptor (video-crop)
            if (typeof window.VideoFrameInterceptor !== "undefined") {
                window.interceptorRegistry.register("video-crop", window.VideoFrameInterceptor, {
                    debug: true,
                    enableCropping: true,
                });
            }
            // Register BrightnessInterceptor
            if (typeof window.BrightnessInterceptor !== "undefined") {
                window.interceptorRegistry.register("brightness-filter", window.BrightnessInterceptor, {
                    debug: true,
                    brightness: 1.0,
                });
            }
            // Register BlurInterceptor
            if (typeof window.BlurInterceptor !== "undefined") {
                window.interceptorRegistry.register("blur-effect", window.BlurInterceptor, {
                    debug: true,
                    blurRadius: 0,
                });
            }
            // Register ChangeDetectorInterceptor
            if (typeof window.ChangeDetectorInterceptor !== "undefined") {
                window.interceptorRegistry.register("change-detector", window.ChangeDetectorInterceptor, {
                    debug: true,
                    enabled: false,
                    changeThreshold: 5,
                    stabilityThreshold: 1,
                    consecutiveStableFrames: 3,
                    maxWaitDuration: 5000,
                    comparisonInterval: 100,
                    pixelSampling: 2,
                });
            }
            console.log("[POC-Streaming] Interceptor registry initialized with:", window.interceptorRegistry.getRegisteredInterceptors());
        }
        else {
            console.warn("[POC-Streaming] Interceptor registry not available");
        }
    }
    /**
     * Connect to signaling server
     */
    async connectToSignalingServer() {
        return new Promise((resolve, reject) => {
            try {
                this.websocket = new WebSocket(this.signalingServerUrl);
                this.websocket.onopen = () => {
                    console.log("[POC-Streaming] Connected to signaling server");
                    this.isConnected = true;
                    resolve();
                };
                this.websocket.onmessage = (event) => {
                    this.handleSignalingMessage(JSON.parse(event.data));
                };
                this.websocket.onclose = () => {
                    console.log("[POC-Streaming] Disconnected from signaling server");
                    this.isConnected = false;
                };
                this.websocket.onerror = (error) => {
                    console.error("[POC-Streaming] WebSocket error:", error);
                    reject(error);
                };
            }
            catch (error) {
                console.error("[POC-Streaming] Failed to connect to signaling server:", error);
                reject(error);
            }
        });
    }
    /**
     * Handle signaling messages
     */
    handleSignalingMessage(message) {
        console.log("[POC-Streaming] Received signaling message:", message);
        switch (message.type) {
            case "web-client-offer":
                this.handleWebClientOffer(message);
                break;
            case "web-client-ice-candidate":
                this.handleWebClientIceCandidate(message);
                break;
            case "user-event":
                this.handleUserEvent(message);
                break;
            case "interceptor-config-update":
                this.handleInterceptorConfigUpdate(message);
                break;
            default:
                console.warn("[POC-Streaming] Unknown message type:", message.type);
        }
    }
    /**
     * Handle web client offer
     */
    async handleWebClientOffer(message) {
        try {
            const { webClientId, offer } = message;
            console.log(`[POC-Streaming] Handling offer from web client: ${webClientId}`);
            // Create peer connection for web client
            const peerConnection = new RTCPeerConnection(this.rtcConfig);
            // Set up data channel for communication
            const dataChannel = peerConnection.createDataChannel("control", {
                ordered: true,
            });
            // Store web client group
            this.webClientGroups.set(webClientId, {
                peerConnection,
                interceptorPipeline: null,
                dataChannel,
                webClient: { id: webClientId },
            });
            // Set remote description
            await peerConnection.setRemoteDescription(offer);
            // Create and set local description
            const answer = await peerConnection.createAnswer();
            await peerConnection.setLocalDescription(answer);
            // Send answer back to web client
            this.sendMessage({
                type: "control-tab-answer",
                webClientId,
                answer,
            });
            console.log(`[POC-Streaming] Answer sent to web client: ${webClientId}`);
        }
        catch (error) {
            console.error("[POC-Streaming] Error handling web client offer:", error);
        }
    }
    /**
     * Handle web client ICE candidate
     */
    async handleWebClientIceCandidate(message) {
        try {
            const { webClientId, candidate } = message;
            const webClientGroup = this.webClientGroups.get(webClientId);
            if (webClientGroup) {
                await webClientGroup.peerConnection.addIceCandidate(candidate);
                console.log(`[POC-Streaming] ICE candidate added for web client: ${webClientId}`);
            }
        }
        catch (error) {
            console.error("[POC-Streaming] Error handling ICE candidate:", error);
        }
    }
    /**
     * Handle user events
     */
    async handleUserEvent(message) {
        try {
            const { userEvent, targetTabId } = message;
            if (this.cdpManager &&
                typeof this.cdpManager.handleUserEvent === "function") {
                await this.cdpManager.handleUserEvent(userEvent, targetTabId);
                console.log(`[POC-Streaming] User event handled for tab: ${targetTabId}`);
            }
            else {
                console.warn("[POC-Streaming] CDP Manager not available for user event handling");
            }
        }
        catch (error) {
            console.error("[POC-Streaming] Error handling user event:", error);
        }
    }
    /**
     * Handle interceptor configuration updates
     */
    handleInterceptorConfigUpdate(message) {
        try {
            const { webClientId, interceptorConfigs } = message;
            console.log(`[POC-Streaming] Updating interceptor config for client: ${webClientId}`);
            // Update stored configuration
            this.clientInterceptorConfigs.set(webClientId, interceptorConfigs);
            // Update active interceptor pipeline if it exists
            const webClientGroup = this.webClientGroups.get(webClientId);
            if (webClientGroup && webClientGroup.interceptorPipeline) {
                // Update each interceptor config individually
                for (const [name, config] of Object.entries(interceptorConfigs)) {
                    webClientGroup.interceptorPipeline.updateInterceptorConfig(name, config);
                }
            }
            console.log(`[POC-Streaming] Interceptor config updated for client: ${webClientId}`);
        }
        catch (error) {
            console.error("[POC-Streaming] Error updating interceptor config:", error);
        }
    }
    /**
     * Send message to signaling server
     */
    sendMessage(message) {
        if (this.websocket && this.isConnected) {
            this.websocket.send(JSON.stringify(message));
        }
        else {
            console.warn("[POC-Streaming] Cannot send message - not connected to signaling server");
        }
    }
    /**
     * Cleanup resources
     */
    async cleanup() {
        console.log("[POC-Streaming] Cleaning up control tab manager...");
        // Close all peer connections
        for (const [webClientId, group] of this.webClientGroups) {
            try {
                if (group.interceptorPipeline) {
                    await group.interceptorPipeline.cleanup();
                }
                group.peerConnection.close();
            }
            catch (error) {
                console.error(`[POC-Streaming] Error cleaning up web client ${webClientId}:`, error);
            }
        }
        // Close target connections
        for (const [tabId, connection] of this.targetConnections) {
            try {
                connection.close();
            }
            catch (error) {
                console.error(`[POC-Streaming] Error cleaning up target connection ${tabId}:`, error);
            }
        }
        // Close WebSocket
        if (this.websocket) {
            this.websocket.close();
        }
        // Cleanup CDP Manager
        if (this.cdpManager &&
            typeof this.cdpManager.cleanup === "function") {
            await this.cdpManager.cleanup();
        }
        console.log("[POC-Streaming] Control tab manager cleanup completed");
    }
}
// IIFE for injection script
(function () {
    "use strict";
    console.log("[POC-Streaming] Control tab script initializing...");
    // Prevent multiple injections
    if (window.controlTabInjected) {
        console.log("[POC-Streaming] Control tab script already injected, skipping...");
        return;
    }
    window.controlTabInjected = true;
    // Initialize control tab manager
    const controlTabManager = new ControlTabManager();
    // Make available globally for debugging
    window.controlTabManager = controlTabManager;
    console.log("[POC-Streaming] Control tab script initialized successfully");
})();
// Export for use in other modules
export default ControlTabManager;
//# sourceMappingURL=control-tab-script.js.map