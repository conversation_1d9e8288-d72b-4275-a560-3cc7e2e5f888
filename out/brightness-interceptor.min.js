import BaseInterceptor from"./base-interceptor.js";class BrightnessInterceptor extends BaseInterceptor{constructor(name="brightness-filter",options={}){const defaultConfig={debug:false,brightness:1,minBrightness:.1,maxBrightness:3,...options};super(name,defaultConfig);this.canvas=null;this.ctx=null;this.imageData=null;this.onConfigChange=this.onConfigUpdate.bind(this);this.log("BrightnessInterceptor initialized",this.config)}onConfigUpdate(oldConfig,newConfig){const oldBrightness=oldConfig.brightness||1;const newBrightness=newConfig.brightness||1;if(newBrightness<this.config.minBrightness){this.config.brightness=this.config.minBrightness;this.log("warn",`Brightness clamped to minimum: ${this.config.minBrightness}`)}else if(newBrightness>this.config.maxBrightness){this.config.brightness=this.config.maxBrightness;this.log("warn",`Brightness clamped to maximum: ${this.config.maxBrightness}`)}this.log("info",`Brightness updated from ${oldBrightness} to ${this.config.brightness}`)}async processVideoFrame(frame){if(Math.abs((this.config.brightness||1)-1)<.01){return frame}try{if(!this.canvas||this.canvas.width!==frame.codedWidth||this.canvas.height!==frame.codedHeight){this.initializeCanvas(frame.codedWidth,frame.codedHeight)}if(!this.ctx){throw new Error("Canvas context not available")}this.ctx.drawImage(frame,0,0);this.imageData=this.ctx.getImageData(0,0,this.canvas.width,this.canvas.height);const data=this.imageData.data;const brightness=Math.max(this.config.minBrightness,Math.min(this.config.maxBrightness,this.config.brightness||1));for(let i=0;i<data.length;i+=4){data[i]=Math.min(255,data[i]*brightness);data[i+1]=Math.min(255,data[i+1]*brightness);data[i+2]=Math.min(255,data[i+2]*brightness)}this.ctx.putImageData(this.imageData,0,0);const processedFrame=new VideoFrame(this.canvas,{timestamp:frame.timestamp,duration:frame.duration??undefined});return processedFrame}catch(error){this.log("Error processing brightness:",error);return frame}}initializeCanvas(width,height){this.canvas=document.createElement("canvas");this.canvas.width=width;this.canvas.height=height;this.ctx=this.canvas.getContext("2d");if(!this.ctx){throw new Error("Failed to get 2D canvas context")}this.log(`Canvas initialized: ${width}x${height}`)}setBrightness(brightness){const clampedBrightness=Math.max(this.config.minBrightness,Math.min(this.config.maxBrightness,brightness));this.updateConfig({brightness:clampedBrightness});this.log(`Brightness set to: ${clampedBrightness}`)}getBrightness(){return this.config.brightness||1}async cleanup(){this.log("Cleaning up brightness interceptor...");if(this.canvas){this.canvas.width=0;this.canvas.height=0;this.canvas=null}this.ctx=null;this.imageData=null;await super.cleanup();this.log("Brightness interceptor cleanup complete")}getStatus(){return{...this.getMetadata(),brightness:this.config.brightness||1,canvasInitialized:!!this.canvas,canvasSize:this.canvas?`${this.canvas.width}x${this.canvas.height}`:null}}}export default BrightnessInterceptor;if(typeof window!=="undefined"){window.BrightnessInterceptor=BrightnessInterceptor}