{"version": 3, "file": "cdp-manager-injector.d.ts", "sourceRoot": "", "sources": ["../src/cdp-manager-injector.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,UAAU,UAAU;IAClB,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,GAAG,EAAE,MAAM,CAAC;IACZ,oBAAoB,EAAE,MAAM,CAAC;CAC9B;AAED,UAAU,SAAS;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,UAAU,OAAO;IACf,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,MAAM,CAAC;CACf;AAWD,UAAU,UAAU;IAClB,MAAM,EAAE,GAAG,CAAC;IACZ,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,UAAU,CAAC;IACvB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,cAAM,GAAG;IACP,OAAO,CAAC,UAAU,CAAa;IAC/B,OAAO,CAAC,EAAE,CAA0B;IACpC,OAAO,CAAC,SAAS,CAAa;IAC9B,OAAO,CAAC,eAAe,CAGnB;gBAEQ,UAAU,EAAE,UAAU;IAI5B,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAgCxB,IAAI,CACR,MAAM,EAAE,MAAM,EACd,MAAM,GAAE,GAAQ,EAChB,SAAS,GAAE,MAAM,GAAG,IAAW,GAC9B,OAAO,CAAC,GAAG,CAAC;IAmBf,IAAI,OAAO;0BAEU,GAAG,cAAkB,MAAM,GAAG,IAAI;2BAEhC,GAAG,cAAa,MAAM,GAAG,IAAI;MAGnD;IAGD,IAAI,MAAM;8BAEe,GAAG,cAAkB,MAAM,GAAG,IAAI;+BAEhC,GAAG,cAAa,MAAM,GAAG,IAAI;iCAE3B,GAAG,cAAa,MAAM,GAAG,IAAI;8BAEhC,GAAG,cAAa,MAAM,GAAG,IAAI;iCAE1B,GAAG,cAAa,MAAM,GAAG,IAAI;MAGzD;IAGD,IAAI,KAAK;mCAEsB,GAAG,cAAa,MAAM,GAAG,IAAI;qCAE3B,GAAG,cAAa,MAAM,GAAG,IAAI;MAG7D;IAEK,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;CAM7B;AAED;;GAEG;AACH,cAAM,UAAU;IACd,OAAO,CAAC,OAAO,CAGb;IACF,OAAO,CAAC,WAAW,CAAiC;IACpD,OAAO,CAAC,aAAa,CAGjB;gBAEQ,OAAO,GAAE;QAAE,SAAS,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,CAAC,EAAE,OAAO,CAAA;KAAO;IASjE;;OAEG;IACH,OAAO,CAAC,GAAG;IAMX;;OAEG;IACG,aAAa,CACjB,WAAW,EAAE,MAAM,EACnB,UAAU,GAAE,UAAU,GAAG,IAAW,GACnC,OAAO,CAAC,UAAU,CAAC;IAwCtB;;OAEG;IACG,gBAAgB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAiB1D;;OAEG;IACH,aAAa,CAAC,WAAW,EAAE,MAAM,GAAG,UAAU,GAAG,IAAI;IAIrD;;OAEG;IACH,iBAAiB,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;IAI5C;;OAEG;IACG,cAAc,CAClB,WAAW,EAAE,MAAM,EACnB,MAAM,EAAE,MAAM,EACd,MAAM,GAAE,GAAQ,GACf,OAAO,CAAC,GAAG,CAAC;IAuBf;;OAEG;IACG,gBAAgB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAyB7D;;OAEG;IACG,aAAa,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAkBtE;;OAEG;IACH,oBAAoB,CAClB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,GACpE,IAAI;IAIP;;OAEG;IACH,sBAAsB,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAI/C;;OAEG;IACG,eAAe,CACnB,SAAS,EAAE,SAAS,EACpB,WAAW,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IAiBhB;;OAEG;IACG,gBAAgB,CACpB,SAAS,EAAE,SAAS,EACpB,WAAW,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IAmChB;;OAEG;IACG,iBAAiB,CACrB,SAAS,EAAE,SAAS,EACpB,WAAW,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IAuBhB;;OAEG;IACG,cAAc,CAClB,SAAS,EAAE,SAAS,EACpB,WAAW,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IAehB;;OAEG;IACG,aAAa,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAapE;;OAEG;IACH,yBAAyB,IAAI,IAAI;IAQjC;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAW9B;;OAEG;IACH,QAAQ,IAAI;QACV,gBAAgB,EAAE,MAAM,CAAC;QACzB,aAAa,EAAE,MAAM,CAAC;QACtB,WAAW,EAAE,KAAK,CAAC;YACjB,KAAK,EAAE,MAAM,CAAC;YACd,SAAS,EAAE,MAAM,CAAC;YAClB,SAAS,EAAE,MAAM,CAAC;YAClB,UAAU,EAAE,UAAU,CAAC;SACxB,CAAC,CAAC;KACJ;CAcF;AAGD,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;AAC3B,eAAe,UAAU,CAAC"}