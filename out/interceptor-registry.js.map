{"version": 3, "file": "interceptor-registry.js", "sourceRoot": "", "sources": ["../src/interceptor-registry.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAcH,MAAM,mBAAmB;IAgBvB;QAfA,kCAAkC;QAC1B,uBAAkB,GAAG,IAAI,GAAG,EAAkC,CAAC;QAEvE,mDAAmD;QAC3C,mBAAc,GAAG,IAAI,GAAG,EAA6B,CAAC;QAE9D,0CAA0C;QAClC,uBAAkB,GAAG,IAAI,GAAG,EAGjC,CAAC;QAEJ,oCAAoC;QAC5B,kBAAa,GAAG,IAAI,GAAG,EAA+B,CAAC;QAG7D,IAAI,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,QAAQ,CACN,IAAY,EACZ,gBAAwC,EACxC,gBAAmC,EAAE;QAErC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,gBAAgB,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAE7C,IAAI,CAAC,GAAG,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAY;QACrB,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,6BAA6B,IAAI,EAAE,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,IAAY;QACvB,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,MAAM,CACJ,IAAY,EACZ,MAA0B;QAE1B,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,IAAI,CAAC,GAAG,CAAC,4CAA4C,IAAI,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1D,MAAM,WAAW,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC;QAEpD,IAAI,CAAC;YACH,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,8BAA8B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAsB,CACpB,WAAmB,EACnB,mBAA6B,EAAE,EAC/B,UAA6C,EAAE;QAE/C,6BAA6B;QAC7B,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,MAAM,YAAY,GAAwB;YACxC,gBAAgB,EAAE,CAAC,GAAG,gBAAgB,CAAC;YACvC,OAAO,EAAE,IAAI,GAAG,EAAE;SACnB,CAAC;QAEF,0CAA0C;QAC1C,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAC1D,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACvC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,GAAG,aAAa,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,GAAG,CAAC,gCAAgC,WAAW,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,WAAmB;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACnD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,EAAE,gBAAgB,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC;QACtD,CAAC;QAED,OAAO;YACL,gBAAgB,EAAE,CAAC,GAAG,MAAM,CAAC,gBAAgB,CAAC;YAC9C,OAAO,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC;SACjC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,WAAmB;QAC1C,sDAAsD;QACtD,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,GAAG,CACN,yCAAyC,WAAW,gCAAgC,CACrF,CAAC;YACF,MAAM,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;YACvE,OAAO,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;QAC9D,MAAM,YAAY,GAA+B,EAAE,CAAC;QACpD,MAAM,cAAc,GAAG,IAAI,GAAG,EAAoC,CAAC;QAEnE,IAAI,CAAC;YACH,KAAK,MAAM,IAAI,IAAI,YAAY,CAAC,gBAAgB,EAAE,CAAC;gBACjD,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC3D,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBAEpD,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,IAAI,CAAC,GAAG,CACN,4CAA4C,IAAI,YAAY,CAC7D,CAAC;oBACF,SAAS;gBACX,CAAC;gBAED,8BAA8B;gBAC9B,MAAM,WAAW,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACvD,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC/B,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBAEtC,IAAI,CAAC,GAAG,CAAC,WAAW,IAAI,2BAA2B,WAAW,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,4BAA4B;YAC5B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAEzD,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,4CAA4C;YAC5C,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;gBACvC,IAAI,CAAC;oBACH,WAAW,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC;gBAAC,OAAO,YAAY,EAAE,CAAC;oBACtB,IAAI,CAAC,GAAG,CACN,wDAAwD,EACxD,YAAY,CACb,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,MAAM,IAAI,KAAK,CACb,4CAA4C,WAAW,KACrD,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CACvD,EAAE,CACH,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,WAAmB;QACvC,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAChE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;QAC9D,OAAO,YAAY,CAAC,gBAAgB;aACjC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACvC,MAAM,CACL,CAAC,WAAW,EAA2C,EAAE,CACvD,WAAW,KAAK,SAAS,CAC5B,CAAC;IACN,CAAC;IAED;;OAEG;IACH,6BAA6B,CAC3B,WAAmB,EACnB,eAAuB,EACvB,SAAqC;QAErC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,sCAAsC,WAAW,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,8BAA8B;QAC9B,MAAM,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QACtE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE;YACxC,GAAG,aAAa;YAChB,GAAG,SAAS;SACb,CAAC,CAAC;QAEH,kDAAkD;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAChE,IAAI,cAAc,IAAI,cAAc,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;YAC1D,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAAC,eAAe,CAAE,CAAC;YACzD,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,eAAe,sBAAsB,WAAW,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,yBAAyB,CAAC,WAAmB;QAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAChE,IAAI,cAAc,EAAE,CAAC;YACnB,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,cAAc,EAAE,CAAC;gBACjD,IAAI,CAAC;oBACH,WAAW,CAAC,OAAO,EAAE,CAAC;oBACtB,IAAI,CAAC,GAAG,CAAC,cAAc,IAAI,2BAA2B,WAAW,EAAE,CAAC,CAAC;gBACvE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,GAAG,CAAC,qBAAqB,IAAI,eAAe,EAAE,KAAK,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC;QAED,8BAA8B;QAC9B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAEvC,IAAI,CAAC,GAAG,CAAC,0CAA0C,WAAW,EAAE,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,QAAQ;QAiBN,MAAM,KAAK,GAAG;YACZ,sBAAsB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI;YACpD,aAAa,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI;YAC3C,uBAAuB,EAAE,CAAC;YAC1B,WAAW,EAAE,EAAyB;SACvC,CAAC;QAEF,KAAK,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACpE,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/D,KAAK,CAAC,uBAAuB,IAAI,kBAAkB,CAAC,MAAM,CAAC;YAE3D,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG;gBAC/B,gBAAgB,EAAE,kBAAkB,CAAC,MAAM;gBAC3C,YAAY,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC3C,IAAI,EAAE,CAAC,CAAC,IAAI;oBACZ,IAAI,EAAE,CAAC,CAAC,IAAI;oBACZ,SAAS,EAAE,CAAC,CAAC,SAAS;oBACtB,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE;iBACpB,CAAC,CAAC;aACJ,CAAC;QACJ,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,GAAG,IAAW;QACxB,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,GAAG,IAAI,CAAC,CAAC;IAChD,CAAC;CACF;AAED,4BAA4B;AAC5B,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;AAEtD,kCAAkC;AAClC,OAAO,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;AACpD,eAAe,mBAAmB,CAAC;AAEnC,gDAAgD;AAChD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IACjC,MAAc,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IACzD,MAAc,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AAC5D,CAAC"}