class InterceptorPipeline{constructor(interceptors=[]){this.isInitialized=false;this.isEnabled=true;this.originalTrack=null;this.processedTrack=null;this.processor=null;this.generator=null;this.transformStream=null;this.interceptors=[...interceptors];this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0,interceptorStats:new Map};this.log("InterceptorPipeline created with",this.interceptors.length,"interceptors")}async initialize(videoTrack){if(!videoTrack||videoTrack.kind!=="video"){throw new Error("InterceptorPipeline requires a valid video track")}this.originalTrack=videoTrack;this.log("Initializing pipeline with video track:",videoTrack.label);try{this.processor=new MediaStreamTrackProcessor({track:videoTrack});this.generator=new MediaStreamTrackGenerator({kind:"video"});this.transformStream=new TransformStream({transform:this.processFrame.bind(this)});this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(error=>{this.log("Pipeline error:",error);this.stats.errorsEncountered++});this.processedTrack=this.generator.track;this.isInitialized=true;this.log("Pipeline initialized successfully");return this.processedTrack}catch(error){this.log("Error initializing pipeline:",error);this.stats.errorsEncountered++;throw error}}async processFrame(frame,controller){const startTime=performance.now();try{this.stats.framesProcessed++;if(!this.isEnabled||this.interceptors.length===0){controller.enqueue(frame);return}if(frame==null){return}let currentFrame=frame;const framesToCleanup=[];for(let i=0;i<this.interceptors.length;i++){const interceptor=this.interceptors[i];if(!interceptor||!interceptor.config.enabled){continue}console.log("Processing frame through interceptor:",interceptor.name);try{const interceptorStartTime=performance.now();const processedFrame=await interceptor.processVideoFrame(currentFrame);const interceptorTime=performance.now()-interceptorStartTime;this.updateInterceptorStats(interceptor.name,interceptorTime);if(processedFrame!==currentFrame&&currentFrame!==frame){framesToCleanup.push(currentFrame)}currentFrame=processedFrame}catch(error){this.log(`Error in interceptor ${interceptor.name}:`,error);this.stats.errorsEncountered++}}controller.enqueue(currentFrame);for(const frameToCleanup of framesToCleanup){try{frameToCleanup.close()}catch(cleanupError){this.log("Error cleaning up intermediate frame:",cleanupError)}}if(currentFrame!==frame){frame.close()}}catch(error){this.log("Error processing frame through pipeline:",error);this.stats.errorsEncountered++;controller.enqueue(frame)}finally{const processingTime=performance.now()-startTime;this.stats.lastProcessingTime=processingTime;this.stats.totalProcessingTime+=processingTime;this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}}updateInterceptorStats(interceptorName,processingTime){if(!this.stats.interceptorStats.has(interceptorName)){this.stats.interceptorStats.set(interceptorName,{framesProcessed:0,totalProcessingTime:0,averageProcessingTime:0})}const stats=this.stats.interceptorStats.get(interceptorName);stats.framesProcessed++;stats.totalProcessingTime+=processingTime;stats.averageProcessingTime=stats.totalProcessingTime/stats.framesProcessed}addInterceptor(interceptor,index=-1){if(!interceptor||typeof interceptor.processVideoFrame!=="function"){throw new Error("Invalid interceptor: must implement processVideoFrame method")}if(index<0||index>=this.interceptors.length){this.interceptors.push(interceptor);this.log(`Added interceptor ${interceptor.name} at end of pipeline`)}else{this.interceptors.splice(index,0,interceptor);this.log(`Added interceptor ${interceptor.name} at index ${index}`)}}removeInterceptor(name){const index=this.interceptors.findIndex(i=>i.name===name);if(index>=0&&index<this.interceptors.length){const removed=this.interceptors.splice(index,1)[0];this.log(`Removed interceptor ${removed.name} from pipeline`);return true}this.log(`Interceptor not found: ${name}`);return false}getInterceptor(identifier){let index=-1;if(typeof identifier==="string"){index=this.interceptors.findIndex(i=>i.name===identifier)}else if(typeof identifier==="number"){index=identifier}if(index>=0&&index<this.interceptors.length){return this.interceptors[index]}return null}getInterceptorNames(){return this.interceptors.map(i=>i.name)}enable(){this.isEnabled=true;this.log("Pipeline enabled")}disable(){this.isEnabled=false;this.log("Pipeline disabled")}updateInterceptorConfig(name,config){const interceptor=this.getInterceptor(name);if(interceptor){interceptor.updateConfig(config);return true}return false}getStats(){return{...this.stats,interceptorStats:new Map(this.stats.interceptorStats),interceptorCount:this.interceptors.length,enabledInterceptorCount:this.interceptors.filter(i=>i.isEnabled).length}}resetStats(){this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0,interceptorStats:new Map};this.log("Pipeline stats reset")}updateInterceptorConfigs(interceptorConfigs){if(!interceptorConfigs||typeof interceptorConfigs!=="object"){this.log("warn","Invalid interceptor configurations provided");return}this.log("info","Updating interceptor configurations:",interceptorConfigs);for(const interceptor of this.interceptors){const newConfig=interceptorConfigs[interceptor.name];if(newConfig){try{interceptor.updateConfig(newConfig);this.log("info",`Updated config for interceptor: ${interceptor.name}`)}catch(error){this.log("error",`Failed to update config for interceptor ${interceptor.name}:`,error)}}}}async cleanup(){try{this.log("Cleaning up pipeline...");for(const interceptor of this.interceptors){try{if(interceptor.cleanup){await interceptor.cleanup()}}catch(error){this.log(`Error cleaning up interceptor ${interceptor.name}:`,error)}}this.interceptors.length=0;if(this.processor){this.processor=null}if(this.generator){this.generator=null}if(this.transformStream){this.transformStream=null}if(this.originalTrack){this.originalTrack=null}if(this.processedTrack){this.processedTrack=null}this.isInitialized=false;this.log("Pipeline cleanup complete")}catch(error){this.log("Error during pipeline cleanup:",error)}}log(...args){console.log("[InterceptorPipeline]",...args)}}export default InterceptorPipeline;if(typeof window!=="undefined"){window.InterceptorPipeline=InterceptorPipeline}