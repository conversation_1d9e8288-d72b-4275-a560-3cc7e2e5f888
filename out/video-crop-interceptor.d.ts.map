{"version": 3, "file": "video-crop-interceptor.d.ts", "sourceRoot": "", "sources": ["../src/video-crop-interceptor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,OAAO,eAAe,MAAM,uBAAuB,CAAC;AACpD,OAAO,KAAK,EAAE,eAAe,EAAmB,MAAM,mBAAmB,CAAC;AAE1E,UAAU,UAAU;IAClB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,UAAU,kBAAkB;IAC1B,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;CAC/B;AAED,KAAK,uBAAuB,GAAG,CAC7B,KAAK,EAAE,UAAU,EACjB,QAAQ,EAAE,kBAAkB,KACzB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1B,cAAM,qBAAsB,SAAQ,eAAe;IAEjD,OAAO,CAAC,WAAW,CAA8C;IACjE,OAAO,CAAC,UAAU,CAAa;IAC/B,OAAO,CAAC,aAAa,CAAa;IAE1B,MAAM,EAAE,eAAe,CAAC;gBAEpB,IAAI,GAAE,MAAqB,EAAE,OAAO,GAAE,eAAoB;IActE;;OAEG;IACG,iBAAiB,CAAC,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IA4B/D;;OAEG;IACH,OAAO,CAAC,aAAa;IAqCrB;;OAEG;YACW,iBAAiB;IAoC/B;;OAEG;IACH,SAAS,CACP,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,uBAAuB,GAChC,MAAM,OAAO;IAchB;;OAEG;IACH,WAAW,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO;IAU1C;;OAEG;IACH,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI;IAiB3D;;OAEG;IACH,kBAAkB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAO1C;;OAEG;IACH,SAAS,IAAI;QACX,IAAI,EAAE,MAAM,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;QACb,aAAa,EAAE,OAAO,CAAC;QACvB,SAAS,EAAE,OAAO,CAAC;QACnB,MAAM,EAAE,eAAe,CAAC;QACxB,KAAK,EAAE,GAAG,CAAC;QACX,cAAc,EAAE,OAAO,CAAC;QACxB,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;QAC9B,eAAe,EAAE,MAAM,CAAC;QACxB,UAAU,EAAE,MAAM,CAAC;QACnB,gBAAgB,EAAE,OAAO,CAAC;QAC1B,iBAAiB,EAAE,OAAO,CAAC;KAC5B;IAaD;;OAEG;IACY,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAYvC;;OAEG;IACH,OAAO,CAAC,QAAQ;CAGjB;AAGD,eAAe,qBAAqB,CAAC"}