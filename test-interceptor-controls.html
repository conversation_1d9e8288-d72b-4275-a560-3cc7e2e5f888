<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interceptor Controls</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .control-group {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .status {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎛️ Interceptor Controls Test</h1>
        <p>This page tests the web client interceptor controls without needing a full streaming setup.</p>
        
        <div class="control-group">
            <h3>Connection</h3>
            <button onclick="connect()">Connect to Signaling Server</button>
            <button onclick="disconnect()">Disconnect</button>
            <div class="status" id="connectionStatus">Disconnected</div>
        </div>

        <div class="control-group">
            <h3>Interceptor Controls</h3>
            <button onclick="toggleCropping()">Toggle Cropping</button>
            <button onclick="applyCropRegion()">Apply Crop Region</button>
            <br><br>
            <label>Crop Region (x,y,width,height): </label>
            <input type="text" id="cropRegionInput" value="100,100,400,300" placeholder="100,100,400,300">
            <br><br>
            <button onclick="requestStatus()">Request Status</button>
            <div class="status" id="interceptorStatus">No status</div>
        </div>

        <div class="control-group">
            <h3>Activity Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div class="log" id="log"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let clientId = null;
        const testTabId = "test-tab-123";

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateConnectionStatus(status) {
            document.getElementById('connectionStatus').textContent = status;
        }

        function updateInterceptorStatus(status) {
            document.getElementById('interceptorStatus').textContent = JSON.stringify(status, null, 2);
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log("Already connected");
                return;
            }

            log("Connecting to signaling server...");
            ws = new WebSocket('ws://localhost:8080');

            ws.onopen = () => {
                log("Connected to signaling server");
                updateConnectionStatus("Connected");
                
                // Register as web client
                sendMessage({
                    type: "register-web-client",
                    metadata: {
                        userAgent: navigator.userAgent,
                        timestamp: Date.now()
                    }
                });
            };

            ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (error) {
                    log(`Failed to parse message: ${error.message}`);
                }
            };

            ws.onclose = () => {
                log("Disconnected from signaling server");
                updateConnectionStatus("Disconnected");
                clientId = null;
            };

            ws.onerror = (error) => {
                log(`WebSocket error: ${error.message || 'Unknown error'}`);
                updateConnectionStatus("Error");
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                log(`Sent: ${message.type}`);
            } else {
                log("Cannot send message - not connected");
            }
        }

        function handleMessage(message) {
            log(`Received: ${message.type}`);

            switch (message.type) {
                case "welcome":
                    clientId = message.clientId;
                    log(`Registered with client ID: ${clientId}`);
                    break;
                case "interceptor-status":
                    log(`Interceptor status: enabled=${message.enabled}, cropping=${message.croppingEnabled}`);
                    updateInterceptorStatus(message);
                    break;
                case "error":
                    log(`Error: ${message.message}`);
                    break;
                default:
                    log(`Unknown message type: ${message.type}`);
            }
        }

        function toggleCropping() {
            if (!clientId) {
                log("Not connected");
                return;
            }

            sendMessage({
                type: "toggle-interceptor-cropping",
                tabId: testTabId,
                enabled: true // Toggle to enabled for testing
            });
        }

        function applyCropRegion() {
            if (!clientId) {
                log("Not connected");
                return;
            }

            const cropInput = document.getElementById('cropRegionInput').value.trim();
            if (!cropInput) {
                log("Please enter a crop region");
                return;
            }

            const parts = cropInput.split(',').map(part => parseInt(part.trim()));
            if (parts.length !== 4 || parts.some(isNaN)) {
                log("Invalid crop region format");
                return;
            }

            const [x, y, width, height] = parts;
            sendMessage({
                type: "set-interceptor-crop-region",
                tabId: testTabId,
                cropRegion: { x, y, width, height }
            });
        }

        function requestStatus() {
            if (!clientId) {
                log("Not connected");
                return;
            }

            sendMessage({
                type: "request-interceptor-status",
                tabId: testTabId
            });
        }

        // Auto-connect on page load
        window.addEventListener('load', () => {
            log("Page loaded - ready to test interceptor controls");
        });
    </script>
</body>
</html>
