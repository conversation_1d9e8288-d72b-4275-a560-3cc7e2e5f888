# Tab Streaming System - Core Streaming Mechanics

## System Overview

This tab streaming system enables real-time streaming of browser tab content using a sophisticated WebRTC proxy architecture. The system implements a **single-stream design** where only one tab can be actively streamed at a time, with the control tab acting as a central WebRTC proxy between target tabs and multiple web clients.

### 🎬 Video Frame Interceptor

The system now includes a powerful **Video Frame Interceptor** that processes incoming WebRTC streams in the control tab, providing:

- **Frame Cropping**: Apply configurable crop regions to video frames with YUV 4:2:0 alignment
- **Frame Subscription API**: Allow multiple components to subscribe to processed frames for analysis
- **Real-time Processing**: Maintains streaming performance while processing frames
- **Control Tab Processing**: All frame processing happens in the control tab, not target tabs
- **Toggle Controls**: Enable/disable interceptor and cropping independently through the control tab UI

**Quick Start with Interceptor:**

```javascript
// The interceptor is automatically integrated into the control tab
// Control it through the control tab interface or programmatically:

// Direct control through control tab manager
const controlTab = window.pocControlTabManager;
controlTab.toggleInterceptor(tabId);
controlTab.setCropRegion(tabId);

// Subscribe to frames for analysis
if (window.frameInterceptor) {
  window.frameInterceptor.subscribe("my-analyzer", (frame, metadata) => {
    // Analyze the frame
    console.log("Frame received:", metadata);
    frame.close(); // Always close when done
  });
}
```

## Core Streaming Architecture

### Primary Components

The streaming infrastructure consists of five core components:

1. **Control Tab** - Central WebRTC proxy and CDP session manager
2. **Target Tabs** - Content sources with injected streaming capabilities
3. **Web Clients** - Stream viewers with persistent connections to control tab
4. **Signaling Server** - WebSocket-based coordination hub for WebRTC signaling
5. **Video Frame Interceptor** - Real-time frame processing and analysis pipeline

### Single-Stream Design Philosophy

The system is architected around a **single active stream constraint**:

- Only one target tab can stream content at any given time
- Web clients maintain persistent connections to the control tab
- Stream switching happens at the control tab ↔ target tab level
- Web client connections remain stable during stream transitions

## 1. Web Client to Control Tab Communication Flow

### Initial Connection Establishment

When a web client connects to the system, it establishes a **persistent WebRTC connection** to the control tab that remains active throughout the session:

```
Web Client → Signaling Server → Control Tab
1. register-web-client
2. web-client-registered (triggers peer connection creation)
3. WebRTC offer/answer exchange
4. Persistent connection established
```

### Stream Request and Delivery Flow

**Phase 1: Stream Request**

```
User clicks tab → Web Client → Signaling Server → Target Tab
                                    ↓
                              request-stream(tabId)
```

**Phase 2: Target Tab Activation**

```
Target Tab receives start-stream → Captures content → Creates WebRTC offer → Control Tab
```

**Phase 3: Stream Distribution**

```
Control Tab receives stream → Adds tracks to existing web client connections → Stream flows to all clients
```

### Key Communication Characteristics

- **Persistent Connections**: Web clients maintain stable WebRTC connections to control tab
- **No Re-negotiation**: Stream switching doesn't require new WebRTC connections to clients
- **Efficient Distribution**: Single stream from target tab is multiplexed to all connected clients
- **Real-time Coordination**: All signaling happens through the centralized signaling server

## 2. Script Injection Process and Effects

### Injection Mechanism

Scripts are injected into tabs using Chrome DevTools Protocol (CDP):

```javascript
// Immediate injection for current page
await targetTab.cdp.Runtime.evaluate({
  expression: script,
  awaitPromise: false,
  returnByValue: false,
});

// Persistent injection for page reloads
await targetTab.cdp.Page.addScriptToEvaluateOnNewDocument({
  source: script,
});
```

### Target Tab Script Capabilities

Once injected, the target tab script provides:

**1. Media Capture Capability**

```javascript
const stream = await navigator.mediaDevices.getDisplayMedia({
  video: { mediaSource: "tab" },
  audio: true,
  preferCurrentTab: true, // Automatically selects current tab
});
```

**2. WebRTC Connection Management**

- Creates RTCPeerConnection to control tab
- Handles offer/answer exchange via signaling server
- Manages ICE candidate exchange for connectivity

**3. Signaling Server Communication**

```javascript
// Establishes WebSocket connection
this.ws = new WebSocket(signalingServerUrl);

// Registers as target tab
this.sendMessage({
  type: "register-target-tab",
  tabId: this.tabId,
  url: window.location.href,
  title: document.title,
});
```

### Local State Management

Each injected target tab maintains:

- **Connection State**: WebSocket and WebRTC connection status
- **Stream State**: Active media stream and track information
- **Tab Metadata**: URL, title, and identification information
- **Peer Connection**: RTCPeerConnection instance to control tab

### Navigation Persistence

The injection system ensures continuity across page navigation:

- Scripts persist through page reloads via `addScriptToEvaluateOnNewDocument`
- System monitors tab navigation and re-injects if needed
- State is re-established automatically after navigation

## 3. Control Tab's Central Role

### Single Stream Management

The control tab enforces the single-stream constraint:

```javascript
// Only one active stream at a time
this.currentStream = null; // { tabId, webClientId }
this.availableTabs = new Map(); // tabId -> { title, url, clientId }

// When new stream starts, previous stream is automatically stopped
if (this.currentStream) {
  this.cleanupStream(this.currentStream.tabId);
}
```

### State Tracking and Management

**Connection State**

```javascript
this.peerConnections = new Map(); // webClientId -> RTCPeerConnection (to web client)
this.targetConnections = new Map(); // tabId -> RTCPeerConnection (to target tab)
this.webClients = new Map(); // webClientId -> { clientInfo, currentTabId }
this.dataChannels = new Map(); // webClientId -> dataChannel
```

**Stream Coordination**

- Tracks which target tab is currently streaming
- Manages distribution to all connected web clients
- Handles stream switching and cleanup

### WebRTC Proxy Operations

**Stream Reception from Target Tab**

```javascript
targetPeerConnection.ontrack = (event) => {
  const [stream] = event.streams;
  // Broadcast stream to ALL connected web clients
  this.broadcastStreamToAllClients(stream, targetTabId);
};
```

**Stream Distribution to Web Clients**

```javascript
// Add tracks to existing peer connections (no re-negotiation needed)
stream.getTracks().forEach((track) => {
  const sender = peerConnection
    .getSenders()
    .find((s) => s.track && s.track.kind === track.kind);

  if (sender) {
    sender.replaceTrack(track); // Replace existing track
  } else {
    peerConnection.addTrack(track, stream); // Add new track
  }
});
```

### CDP Session Management for Event Replay

The control tab maintains CDP sessions to target tabs for user interaction replay:

**CDP Session Establishment**

```javascript
// Establish persistent CDP session to target tab
const cdpClient = new CDP(targetTab);
await cdpClient.connect();
const attachResult = await cdpClient.Target.attachToTarget({
  targetId: targetTabId,
  flatten: true,
});
this.cdpSessions.set(targetTabId, attachResult.sessionId);
```

**Event Replay Coordination**

```javascript
// Receive user event from web client via data channel
dataChannel.onmessage = (event) => {
  const userEvent = JSON.parse(event.data);
  this.replayEventOnTargetTab(userEvent, currentTargetTabId);
};

// Replay event using CDP
await cdpClient.Input.dispatchMouseEvent(
  {
    type: "mousePressed",
    x: Math.round(targetX),
    y: Math.round(targetY),
    button: "left",
  },
  sessionId
);
```

### Coordination Between Components

The control tab serves as the central coordinator:

- **Target Tab Registration**: Tracks available tabs and their capabilities
- **Web Client Management**: Maintains connections to all viewing clients
- **Stream Lifecycle**: Manages complete stream lifecycle from initiation to cleanup
- **Event Distribution**: Routes user interactions to appropriate target tabs
- **State Synchronization**: Keeps all components synchronized with current system state

## Core Streaming Sequence Diagram

The following diagram illustrates the essential streaming flow with focus on state management and persistent connections:

## 4. Signaling Server State Management

### Client Connection Tracking

The signaling server maintains comprehensive state about all connected clients:

```javascript
this.clients = new Map(); // clientId -> { ws, type, metadata }
this.targetTabs = new Map(); // tabId -> clientId
this.controlTab = null; // clientId of control tab
this.webClients = new Set(); // Set of web client IDs
```

### Single-Stream State Management

**Current Stream Tracking**

```javascript
this.currentStream = null; // { tabId, webClientId }
this.availableTabs = new Map(); // tabId -> { title, url, clientId }
```

**Why Single-Stream Design**:

- **Simplified State Management**: Only one active stream reduces complexity
- **Resource Optimization**: Prevents resource conflicts between multiple streams
- **User Experience**: Clear, focused streaming experience
- **Performance**: Optimal performance with dedicated resources

### Client Type Management

**Control Tab Registration**

```javascript
case "register-control-tab":
  this.controlTab = clientId;
  this.clients.set(clientId, { ws, type: "control-tab", metadata });
```

**Target Tab Registration**

```javascript
case "register-target-tab":
  this.targetTabs.set(message.tabId, clientId);
  this.availableTabs.set(message.tabId, {
    title: message.title,
    url: message.url,
    clientId: clientId
  });
```

**Web Client Registration**

```javascript
case "register-web-client":
  this.webClients.add(clientId);
  // Notify control tab to create peer connection
  this.sendToControlTab({
    type: "web-client-registered",
    webClientId: clientId,
    metadata: message.metadata
  });
```

### Stream Request Coordination

When a web client requests a stream:

```javascript
case "request-stream":
  // Stop current stream if exists
  if (this.currentStream) {
    this.sendToTargetTab(this.currentStream.tabId, {
      type: "stop-stream"
    });
  }

  // Start new stream
  this.currentStream = { tabId: message.tabId, webClientId: clientId };
  this.sendToTargetTab(message.tabId, {
    type: "start-stream"
  });
```

## 5. Single Stream Architecture Benefits

### Stream Switching Efficiency

**No Web Client Re-connection Required**:

- Web clients maintain persistent WebRTC connections to control tab
- Stream switching only affects control tab ↔ target tab connections
- Faster switching due to pre-established client connections. The reestablishment of the target tab connection is expected to be quick.

**Connection Reestablishment Pattern**:

```
Previous Target Tab ←→ Control Tab ←→ Web Clients (persistent)
                           ↓
New Target Tab ←→ Control Tab ←→ Web Clients (same connections)
```

### Architecture Simplification

**State Management Benefits**:

- Single point of truth for active stream
- Simplified resource allocation
- Clear ownership of streaming resources
- Reduced coordination complexity

**Performance Advantages**:

- Dedicated bandwidth for single stream
- Optimal quality without resource competition
- Predictable performance characteristics
- Simplified error handling and recovery

### Stream Transition Process

**Seamless Switching**:

1. User selects new target tab
2. Control tab stops current stream
3. New target tab starts streaming
4. Control tab receives new stream
5. Existing web client connections receive new stream tracks
6. No interruption to web client connections

## 6. Inter-Tab Communication via WebRTC

### Data Channel Communication

The system enables direct communication between tabs through the control tab proxy:

**Control Tab as Message Router**

```javascript
// Web client sends message to target tab
webClientDataChannel.send(
  JSON.stringify({
    type: "tab-message",
    targetTabId: "tab-123",
    payload: { action: "scroll", direction: "down" },
  })
);

// Control tab routes message to target tab
this.sendToTargetTab(targetTabId, message.payload);
```

### Extensibility Opportunities

**Bidirectional Communication**:

- Target tabs can send status updates to web clients
- Real-time synchronization of tab state
- Custom application-specific messaging

**Multi-Tab Coordination**:

- Tabs can coordinate through control tab
- Shared state management across tabs
- Cross-tab event propagation

**Enhanced Interaction**:

- Rich interaction beyond simple clicks
- Form data synchronization
- Real-time collaborative features

## Core Streaming Sequence Diagram

```mermaid
sequenceDiagram
    participant WC as 💻 Web Client
    participant SS as 📡 Signaling Server
    participant CT as 🎮 Control Tab
    participant TT1 as 📄 Target Tab 1
    participant TT2 as 📄 Target Tab 2

    Note over WC,TT2: System Initialization & Persistent Connections

    WC->>SS: register-web-client
    SS->>CT: web-client-registered
    CT->>CT: Create persistent peer connection for web client
    CT->>SS: webrtc-offer-to-web-client
    SS->>WC: webrtc-offer
    WC->>SS: webrtc-answer-from-web-client
    SS->>CT: webrtc-answer-from-web-client
    Note over WC,CT: ✅ Persistent WebRTC connection established

    TT1->>SS: register-target-tab (YouTube)
    TT2->>SS: register-target-tab (GitHub)
    SS->>SS: Store available tabs state
    SS->>WC: available-tabs-updated

    Note over WC,TT2: Stream Request & Single-Stream Management

    WC->>SS: request-stream(TT1.tabId)
    SS->>SS: Set currentStream = {tabId: TT1, webClientId: WC}
    SS->>TT1: start-stream

    TT1->>TT1: getDisplayMedia() - Capture tab content
    TT1->>TT1: Create RTCPeerConnection to Control Tab
    TT1->>SS: webrtc-offer-from-target
    SS->>CT: webrtc-offer-from-target

    CT->>CT: Create peer connection to TT1
    CT->>CT: Store targetConnections[TT1] = peerConnection
    CT->>SS: webrtc-answer-to-target
    SS->>TT1: webrtc-answer-to-target

    Note over TT1,CT: ✅ WebRTC connection established
    TT1-->>CT: 🎥 Media stream flows

    CT->>CT: Receive stream from TT1
    CT->>CT: Add tracks to existing WC peer connection (no re-negotiation)
    CT-->>WC: 🎥 Stream delivered via existing connection

    Note over WC,TT2: Stream Switching (Single-Stream Constraint)

    WC->>SS: request-stream(TT2.tabId)
    SS->>SS: Stop current stream: currentStream.tabId = TT1
    SS->>TT1: stop-stream
    TT1->>TT1: Stop media capture & close connection

    SS->>SS: Update currentStream = {tabId: TT2, webClientId: WC}
    SS->>TT2: start-stream

    TT2->>TT2: getDisplayMedia() - Capture tab content
    TT2->>SS: webrtc-offer-from-target
    SS->>CT: webrtc-offer-from-target

    CT->>CT: Close previous connection to TT1
    CT->>CT: Create new peer connection to TT2
    CT->>CT: Store targetConnections[TT2] = peerConnection
    CT->>SS: webrtc-answer-to-target
    SS->>TT2: webrtc-answer-to-target

    Note over TT2,CT: ✅ New WebRTC connection established
    TT2-->>CT: 🎥 New media stream flows

    CT->>CT: Replace tracks in existing WC connection
    CT-->>WC: 🎥 New stream via SAME persistent connection

    Note over WC,TT2: User Interaction & CDP Session Management

    WC->>WC: User clicks on video
    WC->>CT: User event via data channel
    CT->>CT: Get current target tab (TT2) from state
    CT->>CT: Establish CDP session to TT2 if not exists
    CT->>TT2: Input.dispatchMouseEvent via CDP
    TT2->>TT2: Handle click event

    Note over WC,TT2: State Management Summary

    Note over SS: Signaling Server State:<br/>• clients: Map of all connections<br/>• currentStream: {tabId, webClientId}<br/>• availableTabs: Map of registered tabs
    Note over CT: Control Tab State:<br/>• peerConnections: Map to web clients (persistent)<br/>• targetConnections: Map to target tabs<br/>• cdpSessions: Map of CDP sessions<br/>• activeStreams: Current streaming state
    Note over WC: Web Client State:<br/>• Persistent WebRTC connection to CT<br/>• No reconnection needed for stream switching<br/>• Receives new streams via track replacement
```

## 7. Video Frame Interceptor Integration

### Architecture Overview

The Video Frame Interceptor integrates seamlessly into the existing streaming pipeline, processing incoming WebRTC streams in the control tab:

```
Target Tab: getDisplayMedia() → WebRTC PeerConnection → Control Tab: VideoFrameInterceptor → Web Clients
                                                                            ↓
                                                                    Frame Subscribers (Analysis Components)
```

### Integration Points

**Control Tab Integration**

```javascript
// Automatic integration in control-tab-script.js
initializeFrameInterceptor() {
  if (typeof VideoFrameInterceptor !== 'undefined') {
    this.frameInterceptor = new VideoFrameInterceptor({
      debug: true,
      enableCropping: this.enableCropping,
      defaultCropRegion: this.cropRegion
    });

    // Expose globally for external access
    window.frameInterceptor = this.frameInterceptor;
  }
}

// Process incoming streams from target tabs
processStreamThroughInterceptor(stream, targetTabId) {
  if (!this.frameInterceptor || !this.interceptorEnabled) {
    return stream; // Return original stream if interceptor disabled
  }

  const videoTrack = stream.getVideoTracks()[0];
  const processedTrack = this.frameInterceptor.initialize(videoTrack);
  const audioTracks = stream.getAudioTracks();
  return new MediaStream([processedTrack, ...audioTracks]);
}
```

**Control Tab Interface**

- Real-time interceptor status display
- Toggle controls for interceptor and cropping
- Crop region configuration interface
- Frame processing statistics

**Simplified Architecture**

- No signaling server coordination needed for interceptor
- All interceptor control happens locally in the control tab
- Target tabs only send raw streams via WebRTC

### Frame Processing Pipeline

**Transform Stream Architecture**

```javascript
MediaStreamTrackProcessor → TransformStream → MediaStreamTrackGenerator
                               ↓
                        Frame Analysis & Cropping
                               ↓
                        Subscriber Notifications
```

**Frame Processing Flow**

1. Original frame received from MediaStreamTrackProcessor
2. Apply cropping if enabled and crop region is set
3. Notify all subscribers with processed frame clones
4. Forward processed frame to MediaStreamTrackGenerator
5. Clean up original frame resources

### Subscription API

**Frame Subscription**

```javascript
// Subscribe to processed frames
const unsubscribe = interceptor.subscribe(
  "screen-analyzer",
  (frame, metadata) => {
    // Analyze frame
    const analysis = analyzeFrame(frame);

    // Detect changes
    if (analysis.significantChange) {
      console.log("Significant change detected:", analysis);
    }

    // Always close frame when done
    frame.close();
  }
);

// Unsubscribe when done
unsubscribe();
```

**Metadata Structure**

```javascript
const metadata = {
  subscriberId: "screen-analyzer",
  frameCount: 1234,
  timestamp: performance.now(),
  cropRegion: { x: 100, y: 100, width: 400, height: 300 },
};
```

### Configuration and Control

**Interceptor Configuration**

```javascript
// Via control tab manager
const controlTab = window.pocControlTabManager;
controlTab.toggleInterceptor(tabId);
controlTab.toggleCropping(tabId);
controlTab.setCropRegion(tabId); // Uses input field value

// Direct API access
if (window.frameInterceptor) {
  window.frameInterceptor.setCropRegion({
    x: 100,
    y: 100,
    width: 400,
    height: 300,
  });
  window.frameInterceptor.setEnabled(true);
  window.frameInterceptor.setCroppingEnabled(true);
}
```

**Status Monitoring**

```javascript
const status = interceptor.getStatus();
// Returns:
// {
//   isEnabled: true,
//   enableCropping: true,
//   cropRegion: { x: 100, y: 100, width: 400, height: 300 },
//   subscriberCount: 2,
//   frameCount: 1234,
//   hasOriginalTrack: true,
//   hasProcessedTrack: true
// }
```

### Use Cases and Examples

**Screen Change Detection**

```javascript
const screenComparison = new ScreenComparison({
  debug: true,
  diffThreshold: 0.1, // 10% change threshold
  analysisInterval: 500, // Analyze every 500ms
});

screenComparison.start(window.frameInterceptor);

// Listen for changes
window.addEventListener("screenchange", (event) => {
  console.log("Screen changed:", event.detail.difference);
});
```

**Content-Aware Cropping**

```javascript
interceptor.subscribe("content-detector", (frame, metadata) => {
  const importantRegion = detectImportantContent(frame);

  if (importantRegion) {
    // Dynamically adjust crop region
    interceptor.setCropRegion(importantRegion);
  }

  frame.close();
});
```

**Performance Monitoring**

```javascript
interceptor.subscribe("performance-monitor", (frame, metadata) => {
  // Track frame processing performance
  const processingTime = performance.now() - metadata.timestamp;

  if (processingTime > 16.67) {
    // > 60fps threshold
    console.warn("Frame processing too slow:", processingTime + "ms");
  }

  frame.close();
});
```

### Files and Documentation

- **Core Implementation**: `injection-scripts/video-frame-interceptor.js`
- **Integration**: `injection-scripts/target-tab-streamer.js`
- **Control Interface**: `injection-scripts/control-tab-script.js`
- **Example Usage**: `examples/change-detector.js`
- **Demo Application**: `examples/interceptor-demo.html`
- **Configuration**: `config/interceptor-config.js`
- **Full Documentation**: `docs/video-frame-interceptor.md`

### Browser Compatibility

- **Chrome 94+**: Full support (MediaStreamTrackProcessor/Generator)
- **Edge 94+**: Full support
- **Firefox**: Not supported (missing MediaStreamTrackProcessor)
- **Safari**: Not supported (missing MediaStreamTrackProcessor)

The interceptor gracefully degrades when not supported, falling back to the original streaming pipeline without frame processing capabilities.
