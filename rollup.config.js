import typescript from "@rollup/plugin-typescript";
import { nodeResolve } from "@rollup/plugin-node-resolve";
import commonjs from "@rollup/plugin-commonjs";
import terser from "@rollup/plugin-terser";
import fs from "fs";
import path from "path";

// Get all TypeScript files from src directory
const srcDir = "src";
const files = fs
  .readdirSync(srcDir)
  .filter((file) => file.endsWith(".ts") && !file.endsWith(".d.ts"))
  .map((file) => path.basename(file, ".ts"));

// Create configuration for each file
const configs = files.map((fileName) => {
  const config = {
    input: `src/${fileName}.ts`,
    output: [
      {
        file: `injection-scripts/${fileName}.js`,
        format: "iife",
        name: fileName
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(""),
        sourcemap: true,
      },
      {
        file: `injection-scripts/${fileName}.min.js`,
        format: "iife",
        name: fileName
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(""),
        plugins: [terser()],
        sourcemap: true,
      },
    ],
    plugins: [
      nodeResolve({
        browser: true,
        preferBuiltins: false,
      }),
      commonjs(),
      typescript({
        tsconfig: "./tsconfig.json",
        declaration: false,
        declarationMap: false,
        outDir: null,
      }),
    ],
    external: [],
    onwarn: (warning, warn) => {
      // Suppress certain warnings
      if (warning.code === "THIS_IS_UNDEFINED") return;
      if (warning.code === "CIRCULAR_DEPENDENCY") return;
      warn(warning);
    },
  };

  return config;
});

export default configs;
