/**
 * Control Tab Script for POC Streaming
 *
 * This script is injected into the control tab to manage WebRTC connections
 * and coordinate streaming between target tabs and web clients
 */

import type {
  InterceptorConfig,
  BaseInterceptorInterface,
  InterceptorPipelineInterface,
  CDPManager,
} from "./types/global.js";

interface WebClientGroup {
  peerConnection: RTCPeerConnection;
  interceptorPipeline: InterceptorPipelineInterface | null;
  dataChannel: RTCDataChannel | null;
  webClient: any;
}

interface TabGroup {
  tabInfo: any;
  stream: MediaStream | null;
}

interface DefaultInterceptorConfig {
  interceptorNames: string[];
  interceptorConfigs: Record<string, InterceptorConfig>;
}

class ControlTabManager {
  private signalingServerUrl: string = "ws://localhost:8080";
  private websocket: WebSocket | null = null;
  private isConnected: boolean = false;

  // WebRTC configuration
  private rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: "stun:stun.cloudflare.com:3478" },
      { urls: "stun:stun.l.google.com:19302" },
    ],
    iceCandidatePoolSize: 10,
  };

  // Connection management
  private webClientGroups = new Map<string, WebClientGroup>();
  private tabGroups = new Map<string, TabGroup>();
  private targetConnections = new Map<string, RTCPeerConnection>();
  private clientInterceptorConfigs = new Map<string, any>();

  // Default interceptor configuration for new clients
  private defaultInterceptorConfig: DefaultInterceptorConfig = {
    interceptorNames: [
      "change-detector",
      "brightness-filter",
      "blur-effect",
      "video-crop",
    ],
    interceptorConfigs: {
      "video-crop": {
        enabled: true,
        enableCropping: true,
        cropRegion: {
          x: 0,
          y: 0,
          width: window.innerWidth,
          height: window.innerHeight,
        },
      },
      "change-detector": {
        enabled: false,
        changeThreshold: 5,
        stabilityThreshold: 1,
        consecutiveStableFrames: 3,
        maxWaitDuration: 5000,
        comparisonInterval: 100,
        pixelSampling: 2,
      },
      "brightness-filter": {
        enabled: true,
        brightness: 1.2,
      },
      "blur-effect": {
        enabled: true,
        blurRadius: 3,
      },
    },
  };

  // CDP connection management
  private cdpManager: CDPManager | null = null;

  constructor() {
    this.init();
  }

  async init(): Promise<void> {
    console.log("[POC-Streaming] Initializing control tab manager...");

    // Initialize interceptor registry and register available interceptors
    this.initializeInterceptorRegistry();

    // Initialize CDP Manager
    await this.initializeCDPManager();

    // Connect to signaling server
    await this.connectToSignalingServer();

    console.log("[POC-Streaming] Control tab manager initialized successfully");
  }

  /**
   * Initialize CDP Manager
   */
  async initializeCDPManager(): Promise<void> {
    try {
      if (typeof window.CDPManager === "undefined") {
        throw new Error(
          "CDPManager not available - ensure CDP manager is injected first"
        );
      }

      this.cdpManager = window.CDPManager;

      // Initialize default event handlers if available
      if (
        this.cdpManager &&
        typeof (this.cdpManager as any).initializeDefaultHandlers === "function"
      ) {
        (this.cdpManager as any).initializeDefaultHandlers();
      }

      console.log("[POC-Streaming] CDP Manager initialized successfully");
    } catch (error) {
      console.error("[POC-Streaming] Failed to initialize CDP Manager:", error);
      throw error;
    }
  }

  /**
   * Initialize the interceptor registry with available interceptors
   */
  initializeInterceptorRegistry(): void {
    // Register available interceptor types
    if (typeof window.interceptorRegistry !== "undefined") {
      // Register VideoFrameInterceptor (video-crop)
      if (typeof window.VideoFrameInterceptor !== "undefined") {
        window.interceptorRegistry.register(
          "video-crop",
          window.VideoFrameInterceptor,
          {
            debug: true,
            enableCropping: true,
          }
        );
      }

      // Register BrightnessInterceptor
      if (typeof window.BrightnessInterceptor !== "undefined") {
        window.interceptorRegistry.register(
          "brightness-filter",
          window.BrightnessInterceptor,
          {
            debug: true,
            brightness: 1.0,
          }
        );
      }

      // Register BlurInterceptor
      if (typeof window.BlurInterceptor !== "undefined") {
        window.interceptorRegistry.register(
          "blur-effect",
          window.BlurInterceptor,
          {
            debug: true,
            blurRadius: 0,
          }
        );
      }

      // Register ChangeDetectorInterceptor
      if (typeof window.ChangeDetectorInterceptor !== "undefined") {
        window.interceptorRegistry.register(
          "change-detector",
          window.ChangeDetectorInterceptor,
          {
            debug: true,
            enabled: false,
            changeThreshold: 5,
            stabilityThreshold: 1,
            consecutiveStableFrames: 3,
            maxWaitDuration: 5000,
            comparisonInterval: 100,
            pixelSampling: 2,
          }
        );
      }

      console.log(
        "[POC-Streaming] Interceptor registry initialized with:",
        window.interceptorRegistry.getRegisteredInterceptors()
      );
    } else {
      console.warn("[POC-Streaming] Interceptor registry not available");
    }
  }

  /**
   * Connect to signaling server
   */
  async connectToSignalingServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(this.signalingServerUrl);

        this.websocket.onopen = () => {
          console.log("[POC-Streaming] Connected to signaling server");
          this.isConnected = true;
          resolve();
        };

        this.websocket.onmessage = (event) => {
          this.handleSignalingMessage(JSON.parse(event.data));
        };

        this.websocket.onclose = () => {
          console.log("[POC-Streaming] Disconnected from signaling server");
          this.isConnected = false;
        };

        this.websocket.onerror = (error) => {
          console.error("[POC-Streaming] WebSocket error:", error);
          reject(error);
        };
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to connect to signaling server:",
          error
        );
        reject(error);
      }
    });
  }

  /**
   * Handle signaling messages
   */
  private handleSignalingMessage(message: any): void {
    console.log("[POC-Streaming] Received signaling message:", message);

    switch (message.type) {
      case "web-client-offer":
        this.handleWebClientOffer(message);
        break;
      case "web-client-ice-candidate":
        this.handleWebClientIceCandidate(message);
        break;
      case "user-event":
        this.handleUserEvent(message);
        break;
      case "interceptor-config-update":
        this.handleInterceptorConfigUpdate(message);
        break;
      default:
        console.warn("[POC-Streaming] Unknown message type:", message.type);
    }
  }

  /**
   * Handle web client offer
   */
  private async handleWebClientOffer(message: any): Promise<void> {
    try {
      const { webClientId, offer } = message;
      console.log(
        `[POC-Streaming] Handling offer from web client: ${webClientId}`
      );

      // Create peer connection for web client
      const peerConnection = new RTCPeerConnection(this.rtcConfig);

      // Set up data channel for communication
      const dataChannel = peerConnection.createDataChannel("control", {
        ordered: true,
      });

      // Store web client group
      this.webClientGroups.set(webClientId, {
        peerConnection,
        interceptorPipeline: null,
        dataChannel,
        webClient: { id: webClientId },
      });

      // Set remote description
      await peerConnection.setRemoteDescription(offer);

      // Create and set local description
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // Send answer back to web client
      this.sendMessage({
        type: "control-tab-answer",
        webClientId,
        answer,
      });

      console.log(`[POC-Streaming] Answer sent to web client: ${webClientId}`);
    } catch (error) {
      console.error("[POC-Streaming] Error handling web client offer:", error);
    }
  }

  /**
   * Handle web client ICE candidate
   */
  private async handleWebClientIceCandidate(message: any): Promise<void> {
    try {
      const { webClientId, candidate } = message;
      const webClientGroup = this.webClientGroups.get(webClientId);

      if (webClientGroup) {
        await webClientGroup.peerConnection.addIceCandidate(candidate);
        console.log(
          `[POC-Streaming] ICE candidate added for web client: ${webClientId}`
        );
      }
    } catch (error) {
      console.error("[POC-Streaming] Error handling ICE candidate:", error);
    }
  }

  /**
   * Handle user events
   */
  private async handleUserEvent(message: any): Promise<void> {
    try {
      const { userEvent, targetTabId } = message;

      if (
        this.cdpManager &&
        typeof (this.cdpManager as any).handleUserEvent === "function"
      ) {
        await (this.cdpManager as any).handleUserEvent(userEvent, targetTabId);
        console.log(
          `[POC-Streaming] User event handled for tab: ${targetTabId}`
        );
      } else {
        console.warn(
          "[POC-Streaming] CDP Manager not available for user event handling"
        );
      }
    } catch (error) {
      console.error("[POC-Streaming] Error handling user event:", error);
    }
  }

  /**
   * Handle interceptor configuration updates
   */
  private handleInterceptorConfigUpdate(message: any): void {
    try {
      const { webClientId, interceptorConfigs } = message;
      console.log(
        `[POC-Streaming] Updating interceptor config for client: ${webClientId}`
      );

      // Update stored configuration
      this.clientInterceptorConfigs.set(webClientId, interceptorConfigs);

      // Update active interceptor pipeline if it exists
      const webClientGroup = this.webClientGroups.get(webClientId);
      if (webClientGroup && webClientGroup.interceptorPipeline) {
        // Update each interceptor config individually
        for (const [name, config] of Object.entries(interceptorConfigs)) {
          webClientGroup.interceptorPipeline.updateInterceptorConfig(
            name,
            config as Partial<InterceptorConfig>
          );
        }
      }

      console.log(
        `[POC-Streaming] Interceptor config updated for client: ${webClientId}`
      );
    } catch (error) {
      console.error(
        "[POC-Streaming] Error updating interceptor config:",
        error
      );
    }
  }

  /**
   * Send message to signaling server
   */
  sendMessage(message: any): void {
    if (this.websocket && this.isConnected) {
      this.websocket.send(JSON.stringify(message));
    } else {
      console.warn(
        "[POC-Streaming] Cannot send message - not connected to signaling server"
      );
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    console.log("[POC-Streaming] Cleaning up control tab manager...");

    // Close all peer connections
    for (const [webClientId, group] of this.webClientGroups) {
      try {
        if (group.interceptorPipeline) {
          await group.interceptorPipeline.cleanup();
        }
        group.peerConnection.close();
      } catch (error) {
        console.error(
          `[POC-Streaming] Error cleaning up web client ${webClientId}:`,
          error
        );
      }
    }

    // Close target connections
    for (const [tabId, connection] of this.targetConnections) {
      try {
        connection.close();
      } catch (error) {
        console.error(
          `[POC-Streaming] Error cleaning up target connection ${tabId}:`,
          error
        );
      }
    }

    // Close WebSocket
    if (this.websocket) {
      this.websocket.close();
    }

    // Cleanup CDP Manager
    if (
      this.cdpManager &&
      typeof (this.cdpManager as any).cleanup === "function"
    ) {
      await (this.cdpManager as any).cleanup();
    }

    console.log("[POC-Streaming] Control tab manager cleanup completed");
  }
}

// IIFE for injection script
(function () {
  "use strict";
  console.log("[POC-Streaming] Control tab script initializing...");

  // Prevent multiple injections
  if (window.controlTabInjected) {
    console.log(
      "[POC-Streaming] Control tab script already injected, skipping..."
    );
    return;
  }
  window.controlTabInjected = true;

  // Initialize control tab manager
  const controlTabManager = new ControlTabManager();

  // Make available globally for debugging
  (window as any).controlTabManager = controlTabManager;

  console.log("[POC-Streaming] Control tab script initialized successfully");
})();

// Export for use in other modules
export default ControlTabManager;
