/**
 * Interceptor Pipeline System
 *
 * Manages sequential processing of video frames through multiple interceptors.
 * Provides a unified interface for chaining multiple interceptors together.
 *
 * Features:
 * - Sequential interceptor processing
 * - Error handling and fallback mechanisms
 * - Performance monitoring for the entire pipeline
 * - Dynamic interceptor addition/removal
 * - Pipeline state management
 */

import type {
  InterceptorStats,
  BaseInterceptorInterface,
  InterceptorPipelineInterface,
  InterceptorConfig,
} from "./types/global.js";

interface PipelineStats extends InterceptorStats {
  interceptorStats: Map<
    string,
    {
      framesProcessed: number;
      totalProcessingTime: number;
      averageProcessingTime: number;
    }
  >;
}

class InterceptorPipeline implements InterceptorPipelineInterface {
  public readonly interceptors: BaseInterceptorInterface[];
  public isInitialized: boolean = false;
  public isEnabled: boolean = true;
  public stats: PipelineStats;

  // Pipeline state
  private originalTrack: MediaStreamTrack | null = null;
  private processedTrack: MediaStreamTrack | null = null;

  // Transform stream components
  private processor: MediaStreamTrackProcessor | null = null;
  private generator: MediaStreamTrackGenerator | null = null;
  private transformStream: TransformStream<VideoFrame, VideoFrame> | null =
    null;

  constructor(interceptors: BaseInterceptorInterface[] = []) {
    this.interceptors = [...interceptors];

    // Performance tracking
    this.stats = {
      framesProcessed: 0,
      errorsEncountered: 0,
      averageProcessingTime: 0,
      lastProcessingTime: 0,
      totalProcessingTime: 0,
      interceptorStats: new Map(),
    };

    this.log(
      "InterceptorPipeline created with",
      this.interceptors.length,
      "interceptors"
    );
  }

  /**
   * Initialize the pipeline with a video track
   */
  async initialize(videoTrack: MediaStreamTrack): Promise<MediaStreamTrack> {
    if (!videoTrack || videoTrack.kind !== "video") {
      throw new Error("InterceptorPipeline requires a valid video track");
    }

    this.originalTrack = videoTrack;
    this.log("Initializing pipeline with video track:", videoTrack.label);

    try {
      // Create processor and generator
      this.processor = new MediaStreamTrackProcessor({ track: videoTrack });
      this.generator = new MediaStreamTrackGenerator({ kind: "video" });

      // Create transform stream with bound processing function
      this.transformStream = new TransformStream({
        transform: this.processFrame.bind(this),
      });

      // Connect the pipeline
      this.processor.readable
        .pipeThrough(this.transformStream)
        .pipeTo(this.generator.writable)
        .catch((error) => {
          this.log("Pipeline error:", error);
          this.stats.errorsEncountered++;
        });

      this.processedTrack = this.generator.track;
      this.isInitialized = true;

      this.log("Pipeline initialized successfully");
      return this.processedTrack;
    } catch (error) {
      this.log("Error initializing pipeline:", error);
      this.stats.errorsEncountered++;
      throw error;
    }
  }

  /**
   * Process a single video frame through all interceptors
   */
  private async processFrame(
    frame: VideoFrame,
    controller: TransformStreamDefaultController<VideoFrame>
  ): Promise<void> {
    const startTime = performance.now();

    try {
      this.stats.framesProcessed++;

      if (!this.isEnabled || this.interceptors.length === 0) {
        // Pass through original frame if disabled or no interceptors
        controller.enqueue(frame);
        return;
      }

      if (frame == null) {
        // If input frame is null, skip processing
        return;
      }

      let currentFrame = frame;
      const framesToCleanup: VideoFrame[] = [];

      // Process frame through each interceptor sequentially
      for (let i = 0; i < this.interceptors.length; i++) {
        const interceptor = this.interceptors[i];
        if (!interceptor || !interceptor.config.enabled) {
          continue; // Skip disabled interceptors
        }

        console.log("Processing frame through interceptor:", interceptor.name);
        try {
          const interceptorStartTime = performance.now();

          // Process frame through current interceptor
          const processedFrame = await interceptor.processVideoFrame(
            currentFrame
          );

          // Track interceptor performance
          const interceptorTime = performance.now() - interceptorStartTime;
          this.updateInterceptorStats(interceptor.name, interceptorTime);

          // If a new frame was created, mark the previous one for cleanup
          if (processedFrame !== currentFrame && currentFrame !== frame) {
            framesToCleanup.push(currentFrame);
          }

          currentFrame = processedFrame as VideoFrame;
        } catch (error) {
          this.log(`Error in interceptor ${interceptor.name}:`, error);
          this.stats.errorsEncountered++;

          // Continue with current frame on interceptor error
          // This provides resilience - one failing interceptor doesn't break the pipeline
        }
      }

      // Enqueue the final processed frame
      controller.enqueue(currentFrame);

      // Clean up intermediate frames
      for (const frameToCleanup of framesToCleanup) {
        try {
          frameToCleanup.close();
        } catch (cleanupError) {
          this.log("Error cleaning up intermediate frame:", cleanupError);
        }
      }

      // Clean up original frame if a new one was created
      if (currentFrame !== frame) {
        frame.close();
      }
    } catch (error) {
      this.log("Error processing frame through pipeline:", error);
      this.stats.errorsEncountered++;

      // Fallback: pass through original frame
      controller.enqueue(frame);
    } finally {
      // Update performance stats
      const processingTime = performance.now() - startTime;
      this.stats.lastProcessingTime = processingTime;
      this.stats.totalProcessingTime += processingTime;
      this.stats.averageProcessingTime =
        this.stats.totalProcessingTime / this.stats.framesProcessed;
    }
  }

  /**
   * Update performance stats for a specific interceptor
   */
  private updateInterceptorStats(
    interceptorName: string,
    processingTime: number
  ): void {
    if (!this.stats.interceptorStats.has(interceptorName)) {
      this.stats.interceptorStats.set(interceptorName, {
        framesProcessed: 0,
        totalProcessingTime: 0,
        averageProcessingTime: 0,
      });
    }

    const stats = this.stats.interceptorStats.get(interceptorName)!;
    stats.framesProcessed++;
    stats.totalProcessingTime += processingTime;
    stats.averageProcessingTime =
      stats.totalProcessingTime / stats.framesProcessed;
  }

  /**
   * Add an interceptor to the pipeline
   */
  addInterceptor(
    interceptor: BaseInterceptorInterface,
    index: number = -1
  ): void {
    if (!interceptor || typeof interceptor.processVideoFrame !== "function") {
      throw new Error(
        "Invalid interceptor: must implement processVideoFrame method"
      );
    }

    if (index < 0 || index >= this.interceptors.length) {
      this.interceptors.push(interceptor);
      this.log(`Added interceptor ${interceptor.name} at end of pipeline`);
    } else {
      this.interceptors.splice(index, 0, interceptor);
      this.log(`Added interceptor ${interceptor.name} at index ${index}`);
    }
  }

  /**
   * Remove an interceptor from the pipeline
   */
  removeInterceptor(name: string): boolean {
    const index = this.interceptors.findIndex((i) => i.name === name);

    if (index >= 0 && index < this.interceptors.length) {
      const removed = this.interceptors.splice(index, 1)[0];
      this.log(`Removed interceptor ${removed.name} from pipeline`);
      return true;
    }

    this.log(`Interceptor not found: ${name}`);
    return false;
  }

  /**
   * Get an interceptor by name or index
   */
  getInterceptor(identifier: string | number): BaseInterceptorInterface | null {
    let index = -1;

    if (typeof identifier === "string") {
      index = this.interceptors.findIndex((i) => i.name === identifier);
    } else if (typeof identifier === "number") {
      index = identifier;
    }

    if (index >= 0 && index < this.interceptors.length) {
      return this.interceptors[index];
    }

    return null;
  }

  /**
   * Get list of interceptor names in order
   */
  getInterceptorNames(): string[] {
    return this.interceptors.map((i) => i.name);
  }

  /**
   * Enable the pipeline
   */
  enable(): void {
    this.isEnabled = true;
    this.log("Pipeline enabled");
  }

  /**
   * Disable the pipeline
   */
  disable(): void {
    this.isEnabled = false;
    this.log("Pipeline disabled");
  }

  /**
   * Update interceptor configuration by name
   */
  updateInterceptorConfig(
    name: string,
    config: Partial<InterceptorConfig>
  ): boolean {
    const interceptor = this.getInterceptor(name);
    if (interceptor) {
      interceptor.updateConfig(config);
      return true;
    }
    return false;
  }

  /**
   * Get pipeline performance statistics
   */
  getStats(): PipelineStats & {
    interceptorCount: number;
    enabledInterceptorCount: number;
  } {
    return {
      ...this.stats,
      interceptorStats: new Map(this.stats.interceptorStats),
      interceptorCount: this.interceptors.length,
      enabledInterceptorCount: this.interceptors.filter((i) => i.isEnabled)
        .length,
    };
  }

  /**
   * Reset performance statistics
   */
  resetStats(): void {
    this.stats = {
      framesProcessed: 0,
      errorsEncountered: 0,
      averageProcessingTime: 0,
      lastProcessingTime: 0,
      totalProcessingTime: 0,
      interceptorStats: new Map(),
    };
    this.log("Pipeline stats reset");
  }

  /**
   * Update interceptor configurations dynamically
   */
  updateInterceptorConfigs(
    interceptorConfigs: Record<string, Partial<InterceptorConfig>>
  ): void {
    if (!interceptorConfigs || typeof interceptorConfigs !== "object") {
      this.log("warn", "Invalid interceptor configurations provided");
      return;
    }

    this.log(
      "info",
      "Updating interceptor configurations:",
      interceptorConfigs
    );

    for (const interceptor of this.interceptors) {
      const newConfig = interceptorConfigs[interceptor.name];
      if (newConfig) {
        try {
          interceptor.updateConfig(newConfig);
          this.log(
            "info",
            `Updated config for interceptor: ${interceptor.name}`
          );
        } catch (error) {
          this.log(
            "error",
            `Failed to update config for interceptor ${interceptor.name}:`,
            error
          );
        }
      }
    }
  }

  /**
   * Cleanup pipeline resources
   */
  async cleanup(): Promise<void> {
    try {
      this.log("Cleaning up pipeline...");

      // Cleanup all interceptors
      for (const interceptor of this.interceptors) {
        try {
          if (interceptor.cleanup) {
            await interceptor.cleanup();
          }
        } catch (error) {
          this.log(`Error cleaning up interceptor ${interceptor.name}:`, error);
        }
      }

      // Clear interceptors array
      this.interceptors.length = 0;

      // Cleanup transform stream components
      if (this.processor) {
        this.processor = null;
      }

      if (this.generator) {
        this.generator = null;
      }

      if (this.transformStream) {
        this.transformStream = null;
      }

      if (this.originalTrack) {
        this.originalTrack = null;
      }

      if (this.processedTrack) {
        this.processedTrack = null;
      }

      this.isInitialized = false;
      this.log("Pipeline cleanup complete");
    } catch (error) {
      this.log("Error during pipeline cleanup:", error);
    }
  }

  /**
   * Logging utility
   */
  private log(...args: any[]): void {
    console.log("[InterceptorPipeline]", ...args);
  }
}

// Export for use in other modules
export default InterceptorPipeline;

// Make available globally for injection scripts
if (typeof window !== "undefined") {
  (window as any).InterceptorPipeline = InterceptorPipeline;
}
