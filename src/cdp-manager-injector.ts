/**
 * CDP Manager Injector Script
 *
 * This script injects the CDPManager into the control tab
 * It should be injected before the control-tab-script.js
 */

interface TargetInfo {
  id: string;
  title: string;
  type: string;
  url: string;
  webSocketDebuggerUrl: string;
}

interface UserEvent {
  eventType: string;
  x: number;
  y: number;
  deltaX?: number;
  deltaY?: number;
  key?: string;
  text?: string;
  code?: string;
  keyCode?: number;
  keyType?: string;
}

interface TabInfo {
  width: number;
  height: number;
  url: string;
  title: string;
}

interface CDPMessage {
  id?: number;
  method?: string;
  params?: any;
  sessionId?: string;
  result?: any;
  error?: { message: string };
}

interface Connection {
  client: CDP;
  sessionId: string;
  targetInfo: TargetInfo;
  createdAt: number;
}

class CDP {
  private targetInfo: TargetInfo;
  private ws: WebSocket | null = null;
  private messageId: number = 0;
  private pendingMessages = new Map<
    number,
    { resolve: (value: any) => void; reject: (reason: any) => void }
  >();

  constructor(targetInfo: TargetInfo) {
    this.targetInfo = targetInfo;
  }

  async connect(): Promise<void> {
    if (this.ws) return;

    this.ws = new WebSocket(this.targetInfo.webSocketDebuggerUrl);

    return new Promise((resolve, reject) => {
      if (!this.ws) return reject(new Error("WebSocket not initialized"));

      this.ws.onopen = () => {
        console.log(`Connected to target: ${this.targetInfo.title}`);
        resolve();
      };

      this.ws.onerror = reject;

      this.ws.onmessage = (event) => {
        const message: CDPMessage = JSON.parse(event.data);

        if (message.id && this.pendingMessages.has(message.id)) {
          const { resolve, reject } = this.pendingMessages.get(message.id)!;
          this.pendingMessages.delete(message.id);

          if (message.error) {
            reject(new Error(message.error.message));
          } else {
            resolve(message.result);
          }
        }
      };
    });
  }

  async send(
    method: string,
    params: any = {},
    sessionId: string | null = null
  ): Promise<any> {
    if (!this.ws) {
      await this.connect();
    }

    const id = ++this.messageId;
    const message: CDPMessage = { id, method, params };

    if (sessionId) {
      message.sessionId = sessionId;
    }

    return new Promise((resolve, reject) => {
      this.pendingMessages.set(id, { resolve, reject });
      this.ws!.send(JSON.stringify(message));
    });
  }

  // Runtime domain
  get Runtime() {
    return {
      enable: (params: any = {}, sessionId: string | null = null) =>
        this.send("Runtime.enable", params, sessionId),
      evaluate: (params: any, sessionId: string | null = null) =>
        this.send("Runtime.evaluate", params, sessionId),
    };
  }

  // Target domain
  get Target() {
    return {
      getTargets: (params: any = {}, sessionId: string | null = null) =>
        this.send("Target.getTargets", params, sessionId),
      createTarget: (params: any, sessionId: string | null = null) =>
        this.send("Target.createTarget", params, sessionId),
      attachToTarget: (params: any, sessionId: string | null = null) =>
        this.send("Target.attachToTarget", params, sessionId),
      closeTarget: (params: any, sessionId: string | null = null) =>
        this.send("Target.closeTarget", params, sessionId),
      activateTarget: (params: any, sessionId: string | null = null) =>
        this.send("Target.activateTarget", params, sessionId),
    };
  }

  // Input domain
  get Input() {
    return {
      dispatchKeyEvent: (params: any, sessionId: string | null = null) =>
        this.send("Input.dispatchKeyEvent", params, sessionId),
      dispatchMouseEvent: (params: any, sessionId: string | null = null) =>
        this.send("Input.dispatchMouseEvent", params, sessionId),
    };
  }

  async close(): Promise<void> {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

/**
 * CDP Manager - High-level API for CDP operations
 */
class CDPManager {
  private options: {
    debugPort: number;
    debug: boolean;
  };
  private connections = new Map<string, Connection>();
  private eventHandlers = new Map<
    string,
    (userEvent: UserEvent, targetTabId: string) => Promise<void>
  >();

  constructor(options: { debugPort?: number; debug?: boolean } = {}) {
    this.options = {
      debugPort: options.debugPort || 9222,
      debug: options.debug || false,
    };

    this.log("[CDP-Manager] Initialized");
  }

  /**
   * Log messages with CDP-Manager prefix
   */
  private log(message: string, ...args: any[]): void {
    if (this.options.debug) {
      console.log(`[CDP-Manager] ${message}`, ...args);
    }
  }

  /**
   * Add a new CDP connection to a target tab
   */
  async addConnection(
    targetTabId: string,
    targetInfo: TargetInfo | null = null
  ): Promise<Connection> {
    try {
      if (this.connections.has(targetTabId)) {
        return this.connections.get(targetTabId)!;
      }

      if (!targetInfo) {
        targetInfo = await this.getTargetInfo(targetTabId);
      }

      if (!targetInfo) {
        throw new Error(`Target tab ${targetTabId} not found`);
      }

      const client = new CDP(targetInfo);
      await client.connect();

      const attachResult = await client.Target.attachToTarget({
        targetId: targetTabId,
        flatten: true,
      });

      const sessionId = attachResult.sessionId;

      const connection: Connection = {
        client,
        sessionId,
        targetInfo,
        createdAt: Date.now(),
      };

      this.connections.set(targetTabId, connection);
      this.log(`CDP connection established for tab: ${targetTabId}`);
      return connection;
    } catch (error) {
      this.log(`Failed to add connection to tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a CDP connection
   */
  async removeConnection(targetTabId: string): Promise<void> {
    try {
      const connection = this.connections.get(targetTabId);
      if (!connection) {
        return;
      }

      await connection.client.close();

      this.connections.delete(targetTabId);
      this.log(`CDP connection removed for tab: ${targetTabId}`);
    } catch (error) {
      this.log(`Failed to remove connection from tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Get connection info for a target tab
   */
  getConnection(targetTabId: string): Connection | null {
    return this.connections.get(targetTabId) || null;
  }

  /**
   * Get all active connections
   */
  getAllConnections(): Map<string, Connection> {
    return new Map(this.connections);
  }

  /**
   * Execute a command on a target tab
   */
  async executeCommand(
    targetTabId: string,
    method: string,
    params: any = {}
  ): Promise<any> {
    try {
      const connection = this.connections.get(targetTabId);
      if (!connection) {
        throw new Error(`No connection found for tab: ${targetTabId}`);
      }

      // Parse method to determine domain and method
      const [domain, methodName] = method.split(".");

      const domainObj = (connection.client as any)[domain];
      if (!domainObj) {
        throw new Error(`Unsupported domain: ${domain}`);
      }

      const result = await domainObj[methodName](params, connection.sessionId);
      return result;
    } catch (error) {
      this.log(`Failed to execute ${method} on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Get target tab information
   */
  async getTargetTabInfo(targetTabId: string): Promise<TabInfo> {
    try {
      const result = await this.executeCommand(
        targetTabId,
        "Runtime.evaluate",
        {
          expression: `({
          width: window.innerWidth,
          height: window.innerHeight,
          url: window.location.href,
          title: document.title
        })`,
          returnByValue: true,
          awaitPromise: true,
        }
      );

      return result.result.value;
    } catch (error) {
      this.log(`Failed to get tab info for ${targetTabId}:`, error);
      // Return default values as fallback
      return { width: 1920, height: 1080, url: "unknown", title: "Unknown" };
    }
  }

  /**
   * Execute JavaScript on a target tab
   */
  async executeScript(targetTabId: string, script: string): Promise<any> {
    try {
      const result = await this.executeCommand(
        targetTabId,
        "Runtime.evaluate",
        {
          expression: script,
          returnByValue: true,
        }
      );

      return result.result.value;
    } catch (error) {
      this.log(`Failed to execute script on tab ${targetTabId}:`, error);
      return null;
    }
  }

  /**
   * Register an event handler for user events
   */
  registerEventHandler(
    eventType: string,
    handler: (userEvent: UserEvent, targetTabId: string) => Promise<void>
  ): void {
    this.eventHandlers.set(eventType, handler);
  }

  /**
   * Unregister an event handler
   */
  unregisterEventHandler(eventType: string): void {
    this.eventHandlers.delete(eventType);
  }

  /**
   * Handle a user event by dispatching it to the appropriate target tab
   */
  async handleUserEvent(
    userEvent: UserEvent,
    targetTabId: string
  ): Promise<void> {
    try {
      const handler = this.eventHandlers.get(userEvent.eventType);
      if (!handler) {
        this.log(
          `No handler registered for event type: ${userEvent.eventType}`
        );
        return;
      }

      await handler(userEvent, targetTabId);
    } catch (error) {
      this.log(`Failed to handle user event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Handle click events
   */
  async handleClickEvent(
    userEvent: UserEvent,
    targetTabId: string
  ): Promise<void> {
    try {
      const tabInfo = await this.getTargetTabInfo(targetTabId);
      if (!tabInfo) {
        throw new Error("Could not get target tab info");
      }

      const targetX = userEvent.x * tabInfo.width;
      const targetY = userEvent.y * tabInfo.height;

      await this.executeCommand(targetTabId, "Input.dispatchMouseEvent", {
        type: "mousePressed",
        x: Math.round(targetX),
        y: Math.round(targetY),
        button: "left",
        clickCount: 1,
        buttons: 1,
      });

      await new Promise((resolve) => setTimeout(resolve, 50));

      await this.executeCommand(targetTabId, "Input.dispatchMouseEvent", {
        type: "mouseReleased",
        x: Math.round(targetX),
        y: Math.round(targetY),
        button: "left",
        clickCount: 1,
        buttons: 0,
      });
    } catch (error) {
      this.log(`Failed to handle click event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Handle scroll events
   */
  async handleScrollEvent(
    userEvent: UserEvent,
    targetTabId: string
  ): Promise<void> {
    try {
      const tabInfo = await this.getTargetTabInfo(targetTabId);
      if (!tabInfo) {
        throw new Error("Could not get target tab info");
      }

      const targetX = userEvent.x * tabInfo.width;
      const targetY = userEvent.y * tabInfo.height;

      await this.executeCommand(targetTabId, "Input.dispatchMouseEvent", {
        type: "mouseWheel",
        x: Math.round(targetX),
        y: Math.round(targetY),
        deltaX: userEvent.deltaX || 0,
        deltaY: userEvent.deltaY || 0,
      });
    } catch (error) {
      this.log(`Failed to handle scroll event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Handle key events
   */
  async handleKeyEvent(
    userEvent: UserEvent,
    targetTabId: string
  ): Promise<void> {
    try {
      await this.executeCommand(targetTabId, "Input.dispatchKeyEvent", {
        type: userEvent.keyType || "keyDown",
        key: userEvent.key,
        text: userEvent.text,
        code: userEvent.code,
        keyCode: userEvent.keyCode,
      });
    } catch (error) {
      this.log(`Failed to handle key event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Get target info from CDP debug port
   */
  async getTargetInfo(targetTabId: string): Promise<TargetInfo | null> {
    try {
      const response = await fetch(
        `http://localhost:${this.options.debugPort}/json`
      );
      const targets: TargetInfo[] = await response.json();
      return targets.find((tab) => tab.id === targetTabId) || null;
    } catch (error) {
      this.log(`Failed to get target info for ${targetTabId}:`, error);
      return null;
    }
  }

  /**
   * Initialize default event handlers
   */
  initializeDefaultHandlers(): void {
    this.registerEventHandler("click", this.handleClickEvent.bind(this));
    this.registerEventHandler("scroll", this.handleScrollEvent.bind(this));
    this.registerEventHandler("keydown", this.handleKeyEvent.bind(this));
    this.registerEventHandler("keyup", this.handleKeyEvent.bind(this));
    this.registerEventHandler("keypress", this.handleKeyEvent.bind(this));
  }

  /**
   * Clean up all connections
   */
  async cleanup(): Promise<void> {
    const cleanupPromises = Array.from(this.connections.keys()).map(
      (targetTabId) => this.removeConnection(targetTabId)
    );

    await Promise.all(cleanupPromises);

    this.eventHandlers.clear();
    this.log("CDP Manager cleanup completed");
  }

  /**
   * Get connection statistics
   */
  getStats(): {
    totalConnections: number;
    eventHandlers: number;
    connections: Array<{
      tabId: string;
      sessionId: string;
      createdAt: number;
      targetInfo: TargetInfo;
    }>;
  } {
    return {
      totalConnections: this.connections.size,
      eventHandlers: this.eventHandlers.size,
      connections: Array.from(this.connections.entries()).map(
        ([tabId, conn]) => ({
          tabId,
          sessionId: conn.sessionId,
          createdAt: conn.createdAt,
          targetInfo: conn.targetInfo,
        })
      ),
    };
  }
}

// Export for use in other modules
export { CDPManager, CDP };
export default CDPManager;

// Create global instance for injection scripts
const cdpManager = new CDPManager({
  debug: true,
});

// Make available globally for injection scripts
if (typeof window !== "undefined") {
  (window as any).CDPManager = cdpManager;
  (window as any).CDP = CDP;
}
