/**
 * Blur Effect Interceptor
 *
 * A video frame interceptor that applies blur effects to video frames.
 * Demonstrates the multi-interceptor system with CSS filter-based blur.
 *
 * Features:
 * - Real-time blur effect application
 * - Configurable blur intensity
 * - Extends BaseInterceptor for standardized interface
 * - CSS filter-based processing for performance
 */

import BaseInterceptor from "./base-interceptor.js";
import type { BlurConfig, InterceptorConfig } from "./types/global.js";

type BlurType = "gaussian" | "motion";

class BlurInterceptor extends BaseInterceptor {
  // Canvas for frame processing
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;

  declare config: BlurConfig & {
    maxBlurRadius: number;
    blurType: BlurType;
  };

  constructor(name: string = "blur-effect", options: BlurConfig = {}) {
    // Default configuration for blur interceptor
    const defaultConfig = {
      debug: false,
      blurRadius: 0, // 0 = no blur, higher values = more blur
      maxBlurRadius: 20,
      blurType: "gaussian" as BlurType, // 'gaussian' or 'motion'
      ...options,
    };

    super(name, defaultConfig);

    // Set up configuration change handler
    this.onConfigChange = this.onConfigUpdate.bind(this);

    this.log("BlurInterceptor initialized", this.config);
  }

  /**
   * Handle configuration updates
   */
  private onConfigUpdate(
    oldConfig: InterceptorConfig,
    newConfig: InterceptorConfig
  ): void {
    const oldBlurRadius = (oldConfig as any).blurRadius || 0;
    const newBlurRadius = (newConfig as any).blurRadius || 0;

    // Validate blur radius value
    if (newBlurRadius < 0) {
      this.config.blurRadius = 0;
      this.log("warn", "Blur radius clamped to minimum: 0");
    } else if (newBlurRadius > this.config.maxBlurRadius) {
      this.config.blurRadius = this.config.maxBlurRadius;
      this.log(
        "warn",
        `Blur radius clamped to maximum: ${this.config.maxBlurRadius}`
      );
    }

    this.log(
      "info",
      `Blur radius updated from ${oldBlurRadius} to ${this.config.blurRadius}`
    );
  }

  /**
   * Process a video frame - implements BaseInterceptor interface
   */
  async processVideoFrame(frame: VideoFrame): Promise<VideoFrame> {
    // If blur radius is 0, pass through unchanged
    if ((this.config.blurRadius || 0) <= 0) {
      return frame;
    }

    try {
      // Initialize canvas if needed
      if (
        !this.canvas ||
        this.canvas.width !== frame.codedWidth ||
        this.canvas.height !== frame.codedHeight
      ) {
        this.initializeCanvas(frame.codedWidth, frame.codedHeight);
      }

      if (!this.ctx) {
        throw new Error("Canvas context not available");
      }

      // Apply blur filter to canvas context
      const blurRadius = Math.min(
        this.config.blurRadius || 0,
        this.config.maxBlurRadius
      );
      this.ctx.filter = `blur(${blurRadius}px)`;

      // Clear canvas and draw frame with blur effect
      this.ctx.clearRect(0, 0, this.canvas!.width, this.canvas!.height);
      this.ctx.drawImage(frame, 0, 0);

      // Reset filter for future operations
      this.ctx.filter = "none";

      // Create new VideoFrame from canvas
      const processedFrame = new VideoFrame(this.canvas!, {
        timestamp: frame.timestamp,
        duration: frame.duration ?? undefined,
      });

      return processedFrame;
    } catch (error) {
      this.log("Error processing blur:", error);
      // Return original frame on error
      return frame;
    }
  }

  /**
   * Initialize canvas for frame processing
   */
  private initializeCanvas(width: number, height: number): void {
    this.canvas = document.createElement("canvas");
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext("2d");

    if (!this.ctx) {
      throw new Error("Failed to get 2D canvas context");
    }

    // Set canvas properties for better quality
    this.ctx.imageSmoothingEnabled = true;
    this.ctx.imageSmoothingQuality = "high";

    this.log(`Canvas initialized: ${width}x${height}`);
  }

  /**
   * Set blur radius
   */
  setBlurRadius(radius: number): void {
    const clampedRadius = Math.max(
      0,
      Math.min(this.config.maxBlurRadius, radius)
    );
    this.updateConfig({ blurRadius: clampedRadius });
    this.log(`Blur radius set to: ${clampedRadius}px`);
  }

  /**
   * Get current blur radius
   */
  getBlurRadius(): number {
    return this.config.blurRadius || 0;
  }

  /**
   * Set blur type
   */
  setBlurType(type: BlurType): void {
    if (["gaussian", "motion"].includes(type)) {
      this.updateConfig({ blurType: type });
      this.log(`Blur type set to: ${type}`);
    } else {
      this.log(`Invalid blur type: ${type}. Using 'gaussian'.`);
    }
  }

  /**
   * Enable/disable blur effect
   */
  setBlurEnabled(enabled: boolean): void {
    const radius = enabled ? this.config.blurRadius || 5 : 0;
    this.setBlurRadius(radius);
  }

  /**
   * Override cleanup to handle canvas resources
   */
  override async cleanup(): Promise<void> {
    this.log("Cleaning up blur interceptor...");

    // Clean up canvas resources
    if (this.canvas) {
      this.canvas.width = 0;
      this.canvas.height = 0;
      this.canvas = null;
    }

    this.ctx = null;

    // Call parent cleanup
    await super.cleanup();

    this.log("Blur interceptor cleanup complete");
  }

  /**
   * Get interceptor-specific status
   */
  getStatus(): {
    name: string;
    type: string;
    isInitialized: boolean;
    isEnabled: boolean;
    config: BlurConfig;
    stats: any;
    blurRadius: number;
    blurType: BlurType;
    blurEnabled: boolean;
    canvasInitialized: boolean;
    canvasSize: string | null;
  } {
    return {
      ...this.getMetadata(),
      blurRadius: this.config.blurRadius || 0,
      blurType: this.config.blurType,
      blurEnabled: (this.config.blurRadius || 0) > 0,
      canvasInitialized: !!this.canvas,
      canvasSize: this.canvas
        ? `${this.canvas.width}x${this.canvas.height}`
        : null,
    };
  }
}

// Export for use in other modules
export default BlurInterceptor;

// Make available globally for injection scripts
if (typeof window !== "undefined") {
  (window as any).BlurInterceptor = BlurInterceptor;
}
