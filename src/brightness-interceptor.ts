/**
 * Brightness Filter Interceptor
 *
 * A video frame interceptor that adjusts the brightness of video frames.
 * Demonstrates the multi-interceptor system with a simple brightness adjustment.
 *
 * Features:
 * - Real-time brightness adjustment
 * - Configurable brightness levels
 * - Extends BaseInterceptor for standardized interface
 * - Canvas-based frame processing
 */

import BaseInterceptor from "./base-interceptor.js";
import type { BrightnessConfig, InterceptorConfig } from "./types/global.js";

class BrightnessInterceptor extends BaseInterceptor {
  // Canvas for frame processing
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private imageData: ImageData | null = null;

  declare config: BrightnessConfig & {
    minBrightness: number;
    maxBrightness: number;
  };

  constructor(
    name: string = "brightness-filter",
    options: BrightnessConfig = {}
  ) {
    // Default configuration for brightness interceptor
    const defaultConfig = {
      debug: false,
      brightness: 1.0, // 1.0 = normal, 0.5 = darker, 1.5 = brighter
      minBrightness: 0.1,
      maxBrightness: 3.0,
      ...options,
    };

    super(name, defaultConfig);

    // Set up configuration change handler
    this.onConfigChange = this.onConfigUpdate.bind(this);

    this.log("BrightnessInterceptor initialized", this.config);
  }

  /**
   * Handle configuration updates
   */
  private onConfigUpdate(
    oldConfig: InterceptorConfig,
    newConfig: InterceptorConfig
  ): void {
    const oldBrightness = (oldConfig as any).brightness || 1.0;
    const newBrightness = (newConfig as any).brightness || 1.0;

    // Validate brightness value
    if (newBrightness < this.config.minBrightness) {
      this.config.brightness = this.config.minBrightness;
      this.log(
        "warn",
        `Brightness clamped to minimum: ${this.config.minBrightness}`
      );
    } else if (newBrightness > this.config.maxBrightness) {
      this.config.brightness = this.config.maxBrightness;
      this.log(
        "warn",
        `Brightness clamped to maximum: ${this.config.maxBrightness}`
      );
    }

    this.log(
      "info",
      `Brightness updated from ${oldBrightness} to ${this.config.brightness}`
    );
  }

  /**
   * Process a video frame - implements BaseInterceptor interface
   */
  async processVideoFrame(frame: VideoFrame): Promise<VideoFrame> {
    // If brightness is 1.0 (normal), pass through unchanged
    if (Math.abs((this.config.brightness || 1.0) - 1.0) < 0.01) {
      return frame;
    }

    try {
      // Initialize canvas if needed
      if (
        !this.canvas ||
        this.canvas.width !== frame.codedWidth ||
        this.canvas.height !== frame.codedHeight
      ) {
        this.initializeCanvas(frame.codedWidth, frame.codedHeight);
      }

      if (!this.ctx) {
        throw new Error("Canvas context not available");
      }

      // Draw frame to canvas
      this.ctx.drawImage(frame, 0, 0);

      // Get image data
      this.imageData = this.ctx.getImageData(
        0,
        0,
        this.canvas!.width,
        this.canvas!.height
      );
      const data = this.imageData.data;

      // Apply brightness adjustment
      const brightness = Math.max(
        this.config.minBrightness,
        Math.min(this.config.maxBrightness, this.config.brightness || 1.0)
      );

      for (let i = 0; i < data.length; i += 4) {
        // Adjust RGB channels (skip alpha)
        data[i] = Math.min(255, data[i] * brightness); // Red
        data[i + 1] = Math.min(255, data[i + 1] * brightness); // Green
        data[i + 2] = Math.min(255, data[i + 2] * brightness); // Blue
        // Alpha channel (data[i + 3]) remains unchanged
      }

      // Put modified image data back to canvas
      this.ctx.putImageData(this.imageData, 0, 0);

      // Create new VideoFrame from canvas
      const processedFrame = new VideoFrame(this.canvas!, {
        timestamp: frame.timestamp,
        duration: frame.duration ?? undefined,
      });

      return processedFrame;
    } catch (error) {
      this.log("Error processing brightness:", error);
      // Return original frame on error
      return frame;
    }
  }

  /**
   * Initialize canvas for frame processing
   */
  private initializeCanvas(width: number, height: number): void {
    this.canvas = document.createElement("canvas");
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext("2d");

    if (!this.ctx) {
      throw new Error("Failed to get 2D canvas context");
    }

    this.log(`Canvas initialized: ${width}x${height}`);
  }

  /**
   * Set brightness level
   */
  setBrightness(brightness: number): void {
    const clampedBrightness = Math.max(
      this.config.minBrightness,
      Math.min(this.config.maxBrightness, brightness)
    );
    this.updateConfig({ brightness: clampedBrightness });
    this.log(`Brightness set to: ${clampedBrightness}`);
  }

  /**
   * Get current brightness level
   */
  getBrightness(): number {
    return this.config.brightness || 1.0;
  }

  /**
   * Override cleanup to handle canvas resources
   */
  override async cleanup(): Promise<void> {
    this.log("Cleaning up brightness interceptor...");

    // Clean up canvas resources
    if (this.canvas) {
      this.canvas.width = 0;
      this.canvas.height = 0;
      this.canvas = null;
    }

    this.ctx = null;
    this.imageData = null;

    // Call parent cleanup
    await super.cleanup();

    this.log("Brightness interceptor cleanup complete");
  }

  /**
   * Get interceptor-specific status
   */
  getStatus(): {
    name: string;
    type: string;
    isInitialized: boolean;
    isEnabled: boolean;
    config: BrightnessConfig;
    stats: any;
    brightness: number;
    canvasInitialized: boolean;
    canvasSize: string | null;
  } {
    return {
      ...this.getMetadata(),
      brightness: this.config.brightness || 1.0,
      canvasInitialized: !!this.canvas,
      canvasSize: this.canvas
        ? `${this.canvas.width}x${this.canvas.height}`
        : null,
    };
  }
}

// Export for use in other modules
export default BrightnessInterceptor;

// Make available globally for injection scripts
if (typeof window !== "undefined") {
  (window as any).BrightnessInterceptor = BrightnessInterceptor;
}
