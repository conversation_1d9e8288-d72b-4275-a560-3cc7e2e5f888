// Global type definitions for injection scripts

declare global {
  class MediaStreamTrackProcessor {
    readonly readable: ReadableStream<VideoFrame>;
    constructor(init: { track: MediaStreamTrack });
  }

  class MediaStreamTrackGenerator {
    readonly writable: WritableStream<VideoFrame>;
    readonly track: MediaStreamTrack;
    constructor(init: { kind: "video" | "audio" });
  }
}

interface TransformStreamDefaultController<O> {
  readonly desiredSize: number | null;
  enqueue(chunk: O): void;
  error(reason?: any): void;
  terminate(): void;
}

// CDP (Chrome DevTools Protocol) Types
export interface CDPSession {
  send(method: string, params?: any): Promise<any>;
  on(event: string, listener: (...args: any[]) => void): void;
  off(event: string, listener: (...args: any[]) => void): void;
}

export interface CDPManager {
  getSession(tabId: number): Promise<CDPSession>;
  createSession(tabId: number): Promise<CDPSession>;
  closeSession(tabId: number): void;
}

// WebSocket Types
interface WebSocketMessage {
  type: string;
  data?: any;
  clientId?: string;
  timestamp?: number;
}

// Interceptor System Types
export interface InterceptorConfig {
  debug?: boolean;
  enabled?: boolean;
  [key: string]: any;
}

export interface InterceptorStats {
  framesProcessed: number;
  errorsEncountered: number;
  averageProcessingTime: number;
  lastProcessingTime: number;
  totalProcessingTime: number;
}

export interface BaseInterceptorInterface {
  readonly name: string;
  readonly type: string;
  readonly isInitialized: boolean;
  readonly isEnabled: boolean;
  readonly config: InterceptorConfig;
  readonly stats: InterceptorStats;

  initialize(videoTrack?: MediaStreamTrack): Promise<MediaStreamTrack>;
  processFrame(
    frame: VideoFrame,
    controller: TransformStreamDefaultController<VideoFrame>
  ): Promise<void>;
  processVideoFrame(frame: VideoFrame): Promise<VideoFrame>;
  updateConfig(newConfig: Partial<InterceptorConfig>): void;
  cleanup(): Promise<void>;
  enable(): void;
  disable(): void;
  getStats(): InterceptorStats;
  log(...args: any[]): void;
}

// Specific Interceptor Configs
export interface VideoCropConfig extends InterceptorConfig {
  enableCropping?: boolean;
  cropRegion?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  defaultCropRegion?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  frameRate?: number;
}

export interface BrightnessConfig extends InterceptorConfig {
  brightness?: number;
}

export interface BlurConfig extends InterceptorConfig {
  blurRadius?: number;
}

export interface ChangeDetectorConfig extends InterceptorConfig {
  changeThreshold?: number;
  stabilityThreshold?: number;
  consecutiveStableFrames?: number;
  maxWaitDuration?: number;
  comparisonInterval?: number;
  pixelSampling?: number;
}

// Registry Types
export interface InterceptorConstructor {
  new (name: string, config?: InterceptorConfig): BaseInterceptorInterface;
}

export interface InterceptorRegistryInterface {
  register(
    name: string,
    constructor: InterceptorConstructor,
    defaultConfig?: InterceptorConfig
  ): void;
  create(
    name: string,
    config?: InterceptorConfig
  ): BaseInterceptorInterface | null;
  getRegisteredInterceptors(): string[];
  isRegistered(name: string): boolean;
}

// Pipeline Types
export interface InterceptorPipelineInterface {
  readonly interceptors: BaseInterceptorInterface[];
  readonly isInitialized: boolean;
  readonly isEnabled: boolean;
  readonly stats: {
    framesProcessed: number;
    totalProcessingTime: number;
    averageProcessingTime: number;
    interceptorStats: Map<
      string,
      {
        framesProcessed: number;
        totalProcessingTime: number;
        averageProcessingTime: number;
      }
    >;
  };

  initialize(videoTrack: MediaStreamTrack): Promise<MediaStreamTrack>;
  addInterceptor(interceptor: BaseInterceptorInterface): void;
  removeInterceptor(name: string): boolean;
  updateInterceptorConfig(
    name: string,
    config: Partial<InterceptorConfig>
  ): boolean;
  enable(): void;
  disable(): void;
  cleanup(): Promise<void>;
}

// Global Window Extensions
declare global {
  interface Window {
    controlTabInjected?: boolean;
    targetTabInjected?: boolean;
    CDPManager?: CDPManager;
    interceptorRegistry?: InterceptorRegistryInterface;
    InterceptorPipeline?: new (
      interceptors?: BaseInterceptorInterface[]
    ) => InterceptorPipelineInterface;
    BaseInterceptor?: new (
      name: string,
      defaultConfig?: InterceptorConfig
    ) => BaseInterceptorInterface;
    VideoFrameInterceptor?: new (
      name?: string,
      options?: VideoCropConfig
    ) => BaseInterceptorInterface;
    BrightnessInterceptor?: new (
      name?: string,
      options?: BrightnessConfig
    ) => BaseInterceptorInterface;
    BlurInterceptor?: new (
      name?: string,
      options?: BlurConfig
    ) => BaseInterceptorInterface;
    ChangeDetectorInterceptor?: new (
      name?: string,
      options?: ChangeDetectorConfig
    ) => BaseInterceptorInterface;
  }
}

// Utility Types
export type FrameSubscriber = (frame: VideoFrame) => void;
export type LogFunction = (...args: any[]) => void;

export {};
