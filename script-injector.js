/**
 * <PERSON>ript Injection System for POC Streaming
 *
 * Handles injection of JavaScript into target tabs via CDP
 * Uses both Runtime.evaluate and Page.addScriptToEvaluateOnNewDocument
 * for persistence across reloads and redirects
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export function wrapForMainFrameOnly(script) {
  return `
    (function() {
      if (window.parent !== window) {
        return;
      }
      ${script}
    })();
  `;
}

export class ScriptInjector {
  constructor(browserManager, signalingServer) {
    this.browserManager = browserManager;
    this.signalingServer = signalingServer;
    this.injectedScripts = new Map(); // tabId -> { scriptId, isInjected }
    this.persistentScripts = new Map(); // tabId -> scriptId for new document scripts
    this.controlTabInjected = false;
  }

  /**
   * Get the path to a script file, preferring built TypeScript output if available
   */
  async getScriptPath(filename) {
    // Try IIFE built files first (minified) - these are browser-compatible
    const iifeMinifiedPath = path.join(
      process.cwd(),
      "injection-scripts",
      filename.replace(".js", ".min.js")
    );
    if (await this.fileExists(iifeMinifiedPath)) {
      return iifeMinifiedPath;
    }

    // Try IIFE built files (non-minified) - these are browser-compatible
    const iifePath = path.join(process.cwd(), "injection-scripts", filename);
    if (await this.fileExists(iifePath)) {
      return iifePath;
    }

    // Try legacy built TypeScript output (minified) - these may be ES modules
    const minifiedPath = path.join(
      process.cwd(),
      "out",
      filename.replace(".js", ".min.js")
    );
    if (await this.fileExists(minifiedPath)) {
      return minifiedPath;
    }

    // Try legacy built TypeScript output (non-minified) - these may be ES modules
    const builtPath = path.join(process.cwd(), "out", filename);
    if (await this.fileExists(builtPath)) {
      return builtPath;
    }

    // Final fallback - return the injection-scripts path even if it doesn't exist
    return path.join(process.cwd(), "injection-scripts", filename);
  }

  /**
   * Check if a file exists
   */
  async fileExists(filePath) {
    try {
      await fs.promises.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get the external streamer script for target tabs
   */
  async getTargetTabStreamerScript(signalingServerUrl, tabId) {
    const scriptPath = await this.getScriptPath("target-tab-streamer.js");
    let scriptContent = await fs.promises.readFile(scriptPath, "utf8");

    // Replace placeholders
    scriptContent = scriptContent.replace(
      "${SIGNALING_SERVER_URL}",
      signalingServerUrl
    );
    scriptContent = scriptContent.replace("${TAB_ID}", tabId);
    scriptContent = scriptContent.replace("${AUTO_INITIALIZE}", false);

    return scriptContent;
  }

  async getTargetTabStreamerScriptInitializer(signalingServerUrl, tabId) {
    const scriptPath = await this.getScriptPath("target-tab-streamer.js");
    let scriptContent = await fs.promises.readFile(scriptPath, "utf8");

    // Replace placeholders
    scriptContent = scriptContent.replace(
      "${SIGNALING_SERVER_URL}",
      signalingServerUrl
    );
    scriptContent = scriptContent.replace("${TAB_ID}", tabId);
    scriptContent = scriptContent.replace("${AUTO_INITIALIZE}", true);

    return scriptContent;
  }

  /**
   * Get the control tab script
   */
  async getControlTabScript(signalingServerUrl) {
    // Read all interceptor system files in dependency order
    const scriptFiles = [
      "base-interceptor.js",
      "interceptor-registry.js",
      "interceptor-pipeline.js",
      "video-crop-interceptor.js",
      "brightness-interceptor.js",
      "blur-interceptor.js",
      "cdp-manager-injector.js",
      "change-detector-interceptor.js",
      "control-tab-script.js",
    ];

    let combinedScript = "";

    // Load each script file
    for (const scriptFile of scriptFiles) {
      const scriptPath = await this.getScriptPath(scriptFile);

      // Check if file exists before trying to read it
      if (await this.fileExists(scriptPath)) {
        let content = await fs.promises.readFile(scriptPath, "utf8");

        // Replace signaling server URL in control tab script
        if (scriptFile.includes("control-tab-script.js")) {
          content = content.replace(
            "this.signalingServerUrl = 'ws://localhost:8080';",
            `this.signalingServerUrl = '${signalingServerUrl}';`
          );
        }

        combinedScript += `\n// === ${scriptFile} ===\n${content}\n`;
      } else {
        console.warn(`⚠️  Script file not found: ${scriptFile}`);
      }
    }

    return combinedScript;
  }

  /**
   * Inject script into control tab
   */
  async injectControlTabScript(signalingServerUrl) {
    const controlTab = this.browserManager.controlTab;
    if (!controlTab) {
      throw new Error("Control tab not found");
    }

    if (this.controlTabInjected) {
      console.log("Control tab script already injected");
      return;
    }

    console.log("💉 Injecting control tab script...");

    const script = await this.getControlTabScript(signalingServerUrl);

    try {
      // Inject into current page
      const result = await controlTab.cdp.Runtime.evaluate({
        expression: script,
        awaitPromise: false,
        returnByValue: false,
      });

      if (result.exceptionDetails) {
        console.error(
          "Control tab script injection error:",
          result.exceptionDetails
        );
        throw new Error(
          `Control tab script injection failed: ${result.exceptionDetails.text}`
        );
      }

      // Try to add script for new document loads (optional - may not be supported in all Chrome versions)
      try {
        await controlTab.cdp.Page.addScriptToEvaluateOnNewDocument({
          source: wrapForMainFrameOnly(script),
        });
        console.log("✅ Persistent script added for control tab");
      } catch (persistentError) {
        console.log(
          "⚠️  Persistent script not supported, using immediate injection only"
        );
      }

      this.controlTabInjected = true;
      console.log("✅ Control tab script injected successfully");
    } catch (error) {
      console.error("❌ Failed to inject control tab script:", error);
      throw error;
    }
  }

  /**
   * Inject streamer script into a target tab
   */
  async injectTargetTabScript(tabId, signalingServerUrl) {
    const targetTab = this.browserManager.getTargetTab(tabId);
    if (!targetTab) {
      throw new Error(`Target tab not found: ${tabId}`);
    }

    console.log(`💉 Injecting streamer script into tab: ${tabId}`);

    // Use the new external streamer script
    const script = await this.getTargetTabStreamerScript(
      signalingServerUrl,
      tabId
    );

    // Use the new external streamer script
    const autoScript = await this.getTargetTabStreamerScriptInitializer(
      signalingServerUrl,
      tabId
    );

    try {
      // Method 1: Inject into current page
      const result = await targetTab.cdp.Runtime.evaluate({
        expression: script,
        awaitPromise: false,
        returnByValue: false,
      });

      if (result.exceptionDetails) {
        console.error("Script injection error:", result.exceptionDetails);
        throw new Error(
          `Script injection failed: ${result.exceptionDetails.text}`
        );
      }

      // Method 2: Try to add script for new document loads (persistence)
      let persistentResult = null;
      try {
        persistentResult =
          await targetTab.cdp.Page.addScriptToEvaluateOnNewDocument({
            source: wrapForMainFrameOnly(autoScript),
          });
        console.log(`✅ Persistent script added for tab: ${tabId}`);
      } catch (persistentError) {
        console.log(
          `⚠️  Persistent script not supported for tab: ${tabId}, using immediate injection only ${persistentError}`
        );
      }

      // Store script IDs for cleanup
      this.injectedScripts.set(tabId, {
        isInjected: true,
        timestamp: Date.now(),
      });

      if (persistentResult && persistentResult.identifier) {
        this.persistentScripts.set(tabId, persistentResult.identifier);
      }

      targetTab.isInjected = true;

      console.log(`✅ Script injected successfully into tab: ${tabId}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to inject script into tab ${tabId}:`, error);
      throw error;
    }
  }

  /**
   * Remove injected script from a target tab
   */
  async removeScript(tabId) {
    const targetTab = this.browserManager.getTargetTab(tabId);
    if (!targetTab) {
      console.warn(`Target tab not found for script removal: ${tabId}`);
      return;
    }

    console.log(`🗑️ Removing script from tab: ${tabId}`);

    try {
      // Remove persistent script
      const persistentScriptId = this.persistentScripts.get(tabId);
      if (persistentScriptId) {
        await targetTab.cdp.Runtime.removeScriptToEvaluateOnNewDocument({
          identifier: persistentScriptId,
        });
        this.persistentScripts.delete(tabId);
      }

      // Clean up tracking
      this.injectedScripts.delete(tabId);
      targetTab.isInjected = false;

      console.log(`✅ Script removed from tab: ${tabId}`);
    } catch (error) {
      console.error(`❌ Failed to remove script from tab ${tabId}:`, error);
    }
  }

  /**
   * Check if script is injected in a tab
   */
  isScriptInjected(tabId) {
    return this.injectedScripts.has(tabId);
  }

  /**
   * Get all tabs with injected scripts
   */
  getInjectedTabs() {
    return Array.from(this.injectedScripts.keys());
  }

  /**
   * Cleanup all injected scripts
   */
  async cleanup() {
    console.log("🧹 Cleaning up script injector...");

    const tabIds = Array.from(this.injectedScripts.keys());
    for (const tabId of tabIds) {
      await this.removeScript(tabId);
    }

    console.log("✅ Script injector cleanup complete");
  }
}
