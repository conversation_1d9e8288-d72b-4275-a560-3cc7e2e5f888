{"name": "tab-streamer-poc", "version": "1.0.0", "type": "module", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "build": "npm run build-ts && npm run minify", "build-ts": "tsc --noEmit<PERSON>n<PERSON><PERSON>r false --skipLib<PERSON><PERSON>ck true", "minify": "npm run minify-if-exists", "minify-if-exists": "node -e \"const fs = require('fs'); const path = require('path'); const { execSync } = require('child_process'); const files = ['base-interceptor', 'interceptor-registry', 'interceptor-pipeline', 'video-crop-interceptor', 'brightness-interceptor', 'blur-interceptor', 'change-detector-interceptor', 'cdp-manager-injector', 'control-tab-script', 'target-tab-streamer']; files.forEach(file => { const jsPath = path.join('out', file + '.js'); if (fs.existsSync(jsPath)) { try { execSync(\\`terser \\${jsPath} -o out/\\${file}.min.js\\`, {stdio: 'inherit'}); console.log(\\`Minified \\${file}.js\\`); } catch(e) { console.warn(\\`Failed to minify \\${file}.js:\\`, e.message); } } else { console.warn(\\`Skipping \\${file}.js - file not found\\`); } });\"", "clean": "rm -rf out", "dev": "tsc --watch"}, "dependencies": {"@types/jsdom": "^21.1.7", "express": "^4.21.2", "jsdom": "^27.0.0", "puppeteer": "^24.20.0", "typescript": "^5.9.2", "ws": "^8.18.3"}, "devDependencies": {"@types/node": "^20.0.0", "terser": "^5.0.0"}, "author": "", "license": "ISC"}