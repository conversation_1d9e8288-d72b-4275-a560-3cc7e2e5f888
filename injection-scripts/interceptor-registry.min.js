var InterceptorRegistry=function(t){"use strict";class e{constructor(){this.interceptorClasses=new Map,this.defaultConfigs=new Map,this.clientInterceptors=new Map,this.clientConfigs=new Map,this.log("InterceptorRegistry initialized")}register(t,e,r={}){if(!t||"string"!=typeof t)throw new Error("Interceptor name must be a non-empty string");if(!e||"function"!=typeof e)throw new Error("Interceptor class must be a constructor function");this.interceptorClasses.set(t,e),this.defaultConfigs.set(t,r),this.log(`Registered interceptor: ${t}`)}unregister(t){this.interceptorClasses.has(t)&&(this.interceptorClasses.delete(t),this.defaultConfigs.delete(t),this.log(`Unregistered interceptor: ${t}`))}getRegisteredInterceptors(){return Array.from(this.interceptorClasses.keys())}isRegistered(t){return this.interceptorClasses.has(t)}create(t,e){const r=this.interceptorClasses.get(t);if(!r)return this.log(`Warning: Interceptor class not found for ${t}`),null;const n={...this.defaultConfigs.get(t)||{},...e};try{return new r(t,n)}catch(e){return this.log(`Error creating interceptor ${t}:`,e),null}}setClientConfiguration(t,e=[],r={}){for(const t of e)if(!this.interceptorClasses.has(t))throw new Error(`Unknown interceptor: ${t}`);const n={interceptorNames:[...e],configs:new Map};for(const t of e){const e=this.defaultConfigs.get(t)||{},s=r[t]||{};n.configs.set(t,{...e,...s})}this.clientConfigs.set(t,n),this.log(`Set configuration for client ${t}:`,e)}getClientConfiguration(t){const e=this.clientConfigs.get(t);return e?{interceptorNames:[...e.interceptorNames],configs:new Map(e.configs)}:{interceptorNames:[],configs:new Map}}createClientInterceptors(t){if(this.clientInterceptors.has(t)){this.log(`Interceptors already exist for client ${t}, returning existing instances`);const e=this.clientInterceptors.get(t);return Array.from(e.values())}const e=this.getClientConfiguration(t),r=[],n=new Map;try{for(const s of e.interceptorNames){const i=this.interceptorClasses.get(s),o=e.configs.get(s)||{};if(!i){this.log(`Warning: Interceptor class not found for ${s}, skipping`);continue}const c=new i(s,o);r.push(c),n.set(s,c),this.log(`Created ${s} interceptor for client ${t}`)}return this.clientInterceptors.set(t,n),r}catch(e){for(const t of r)try{t.cleanup()}catch(t){this.log("Error cleaning up interceptor during creation failure:",t)}throw new Error(`Failed to create interceptors for client ${t}: ${e instanceof Error?e.message:String(e)}`)}}getClientInterceptors(t){const e=this.clientInterceptors.get(t);if(!e)return[];return this.getClientConfiguration(t).interceptorNames.map(t=>e.get(t)).filter(t=>void 0!==t)}updateClientInterceptorConfig(t,e,r){const n=this.clientConfigs.get(t);if(!n)throw new Error(`No configuration found for client: ${t}`);const s=n.configs.get(e)||{};n.configs.set(e,{...s,...r});const i=this.clientInterceptors.get(t);if(i&&i.has(e)){i.get(e).updateConfig(r)}this.log(`Updated ${e} config for client ${t}`)}cleanupClientInterceptors(t){const e=this.clientInterceptors.get(t);if(e){for(const[r,n]of e)try{n.cleanup(),this.log(`Cleaned up ${r} interceptor for client ${t}`)}catch(t){this.log(`Error cleaning up ${r} interceptor:`,t)}this.clientInterceptors.delete(t)}this.clientConfigs.delete(t),this.log(`Cleaned up all interceptors for client ${t}`)}getStats(){const t={registeredInterceptors:this.interceptorClasses.size,activeClients:this.clientInterceptors.size,totalActiveInterceptors:0,clientStats:{}};for(const[e,r]of this.clientInterceptors){const n=Array.from(r.values());t.totalActiveInterceptors+=n.length,t.clientStats[e]={interceptorCount:n.length,interceptors:n.map(t=>({name:t.name,type:t.type,isEnabled:t.isEnabled,stats:t.getStats()}))}}return t}log(...t){console.log("[InterceptorRegistry]",...t)}}const r=new e;return"undefined"!=typeof window&&(window.InterceptorRegistry=e,window.interceptorRegistry=r),t.InterceptorRegistry=e,t.default=r,t.interceptorRegistry=r,Object.defineProperty(t,"__esModule",{value:!0}),t}({});
//# sourceMappingURL=interceptor-registry.min.js.map
