{"version": 3, "file": "cdp-manager-injector.min.js", "sources": ["../src/cdp-manager-injector.ts"], "sourcesContent": [null], "names": ["CDP", "constructor", "targetInfo", "this", "ws", "messageId", "pendingMessages", "Map", "connect", "WebSocket", "webSocketDebuggerUrl", "Promise", "resolve", "reject", "Error", "onopen", "console", "log", "title", "onerror", "onmessage", "event", "message", "JSON", "parse", "data", "id", "has", "get", "delete", "error", "result", "send", "method", "params", "sessionId", "set", "stringify", "Runtime", "enable", "evaluate", "Target", "getTargets", "createTarget", "attachToTarget", "<PERSON><PERSON><PERSON><PERSON>", "activateTarget", "Input", "dispatchKeyEvent", "dispatchMouseEvent", "close", "CDPManager", "options", "connections", "eventHandlers", "debugPort", "debug", "args", "addConnection", "targetTabId", "getTargetInfo", "client", "attachResult", "targetId", "flatten", "connection", "createdAt", "Date", "now", "removeConnection", "getConnection", "getAllConnections", "executeCommand", "domain", "methodName", "split", "domainObj", "getTargetTabInfo", "expression", "returnByValue", "await<PERSON><PERSON><PERSON>", "value", "width", "height", "url", "executeScript", "script", "registerEventHandler", "eventType", "handler", "unregisterEventHandler", "handleUserEvent", "userEvent", "handleClickEvent", "tabInfo", "targetX", "x", "targetY", "y", "type", "Math", "round", "button", "clickCount", "buttons", "setTimeout", "handleScrollEvent", "deltaX", "deltaY", "handleKeyEvent", "keyType", "key", "text", "code", "keyCode", "response", "fetch", "json", "find", "tab", "initializeDefaultHandlers", "bind", "cleanup", "cleanupPromises", "Array", "from", "keys", "map", "all", "clear", "getStats", "totalConnections", "size", "entries", "tabId", "conn", "cdpManager", "window"], "mappings": "gDAmDA,MAAMA,EASJ,WAAAC,CAAYC,GAPJC,KAAEC,GAAqB,KACvBD,KAASE,UAAW,EACpBF,KAAAG,gBAAkB,IAAIC,IAM5BJ,KAAKD,WAAaA,CACnB,CAED,aAAMM,GACJ,IAAIL,KAAKC,GAIT,OAFAD,KAAKC,GAAK,IAAIK,UAAUN,KAAKD,WAAWQ,sBAEjC,IAAIC,QAAQ,CAACC,EAASC,KAC3B,IAAKV,KAAKC,GAAI,OAAOS,EAAO,IAAIC,MAAM,8BAEtCX,KAAKC,GAAGW,OAAS,KACfC,QAAQC,IAAI,wBAAwBd,KAAKD,WAAWgB,SACpDN,KAGFT,KAAKC,GAAGe,QAAUN,EAElBV,KAAKC,GAAGgB,UAAaC,IACnB,MAAMC,EAAsBC,KAAKC,MAAMH,EAAMI,MAE7C,GAAIH,EAAQI,IAAMvB,KAAKG,gBAAgBqB,IAAIL,EAAQI,IAAK,CACtD,MAAMd,QAAEA,EAAOC,OAAEA,GAAWV,KAAKG,gBAAgBsB,IAAIN,EAAQI,IAC7DvB,KAAKG,gBAAgBuB,OAAOP,EAAQI,IAEhCJ,EAAQQ,MACVjB,EAAO,IAAIC,MAAMQ,EAAQQ,MAAMR,UAE/BV,EAAQU,EAAQS,OAEnB,IAGN,CAED,UAAMC,CACJC,EACAC,EAAc,CAAA,EACdC,EAA2B,MAEtBhC,KAAKC,UACFD,KAAKK,UAGb,MAAMkB,IAAOvB,KAAKE,UACZiB,EAAsB,CAAEI,KAAIO,SAAQC,UAM1C,OAJIC,IACFb,EAAQa,UAAYA,GAGf,IAAIxB,QAAQ,CAACC,EAASC,KAC3BV,KAAKG,gBAAgB8B,IAAIV,EAAI,CAAEd,UAASC,WACxCV,KAAKC,GAAI4B,KAAKT,KAAKc,UAAUf,KAEhC,CAGD,WAAIgB,GACF,MAAO,CACLC,OAAQ,CAACL,EAAc,CAAE,EAAEC,EAA2B,OACpDhC,KAAK6B,KAAK,iBAAkBE,EAAQC,GACtCK,SAAU,CAACN,EAAaC,EAA2B,OACjDhC,KAAK6B,KAAK,mBAAoBE,EAAQC,GAE3C,CAGD,UAAIM,GACF,MAAO,CACLC,WAAY,CAACR,EAAc,CAAE,EAAEC,EAA2B,OACxDhC,KAAK6B,KAAK,oBAAqBE,EAAQC,GACzCQ,aAAc,CAACT,EAAaC,EAA2B,OACrDhC,KAAK6B,KAAK,sBAAuBE,EAAQC,GAC3CS,eAAgB,CAACV,EAAaC,EAA2B,OACvDhC,KAAK6B,KAAK,wBAAyBE,EAAQC,GAC7CU,YAAa,CAACX,EAAaC,EAA2B,OACpDhC,KAAK6B,KAAK,qBAAsBE,EAAQC,GAC1CW,eAAgB,CAACZ,EAAaC,EAA2B,OACvDhC,KAAK6B,KAAK,wBAAyBE,EAAQC,GAEhD,CAGD,SAAIY,GACF,MAAO,CACLC,iBAAkB,CAACd,EAAaC,EAA2B,OACzDhC,KAAK6B,KAAK,yBAA0BE,EAAQC,GAC9Cc,mBAAoB,CAACf,EAAaC,EAA2B,OAC3DhC,KAAK6B,KAAK,2BAA4BE,EAAQC,GAEnD,CAED,WAAMe,GACA/C,KAAKC,KACPD,KAAKC,GAAG8C,QACR/C,KAAKC,GAAK,KAEb,EAMH,MAAM+C,EAWJ,WAAAlD,CAAYmD,EAAmD,IANvDjD,KAAAkD,YAAc,IAAI9C,IAClBJ,KAAAmD,cAAgB,IAAI/C,IAM1BJ,KAAKiD,QAAU,CACbG,UAAWH,EAAQG,WAAa,KAChCC,MAAOJ,EAAQI,QAAS,GAG1BrD,KAAKc,IAAI,4BACV,CAKO,GAAAA,CAAIK,KAAoBmC,GAC1BtD,KAAKiD,QAAQI,OACfxC,QAAQC,IAAI,iBAAiBK,OAAcmC,EAE9C,CAKD,mBAAMC,CACJC,EACAzD,EAAgC,MAEhC,IACE,GAAIC,KAAKkD,YAAY1B,IAAIgC,GACvB,OAAOxD,KAAKkD,YAAYzB,IAAI+B,GAO9B,GAJKzD,IACHA,QAAmBC,KAAKyD,cAAcD,KAGnCzD,EACH,MAAM,IAAIY,MAAM,cAAc6C,eAGhC,MAAME,EAAS,IAAI7D,EAAIE,SACjB2D,EAAOrD,UAEb,MAAMsD,QAAqBD,EAAOpB,OAAOG,eAAe,CACtDmB,SAAUJ,EACVK,SAAS,IAKLC,EAAyB,CAC7BJ,SACA1B,UAJgB2B,EAAa3B,UAK7BjC,aACAgE,UAAWC,KAAKC,OAKlB,OAFAjE,KAAKkD,YAAYjB,IAAIuB,EAAaM,GAClC9D,KAAKc,IAAI,uCAAuC0C,KACzCM,CACR,CAAC,MAAOnC,GAEP,MADA3B,KAAKc,IAAI,mCAAmC0C,KAAgB7B,GACtDA,CACP,CACF,CAKD,sBAAMuC,CAAiBV,GACrB,IACE,MAAMM,EAAa9D,KAAKkD,YAAYzB,IAAI+B,GACxC,IAAKM,EACH,aAGIA,EAAWJ,OAAOX,QAExB/C,KAAKkD,YAAYxB,OAAO8B,GACxBxD,KAAKc,IAAI,mCAAmC0C,IAC7C,CAAC,MAAO7B,GAEP,MADA3B,KAAKc,IAAI,wCAAwC0C,KAAgB7B,GAC3DA,CACP,CACF,CAKD,aAAAwC,CAAcX,GACZ,OAAOxD,KAAKkD,YAAYzB,IAAI+B,IAAgB,IAC7C,CAKD,iBAAAY,GACE,OAAO,IAAIhE,IAAIJ,KAAKkD,YACrB,CAKD,oBAAMmB,CACJb,EACA1B,EACAC,EAAc,CAAA,GAEd,IACE,MAAM+B,EAAa9D,KAAKkD,YAAYzB,IAAI+B,GACxC,IAAKM,EACH,MAAM,IAAInD,MAAM,gCAAgC6C,KAIlD,MAAOc,EAAQC,GAAczC,EAAO0C,MAAM,KAEpCC,EAAaX,EAAWJ,OAAeY,GAC7C,IAAKG,EACH,MAAM,IAAI9D,MAAM,uBAAuB2D,KAIzC,aADqBG,EAAUF,GAAYxC,EAAQ+B,EAAW9B,UAE/D,CAAC,MAAOL,GAEP,MADA3B,KAAKc,IAAI,qBAAqBgB,YAAiB0B,KAAgB7B,GACzDA,CACP,CACF,CAKD,sBAAM+C,CAAiBlB,GACrB,IAgBE,aAfqBxD,KAAKqE,eACxBb,EACA,mBACA,CACEmB,WAAY,oKAMZC,eAAe,EACfC,cAAc,KAIJjD,OAAOkD,KACtB,CAAC,MAAOnD,GAGP,OAFA3B,KAAKc,IAAI,8BAA8B0C,KAAgB7B,GAEhD,CAAEoD,MAAO,KAAMC,OAAQ,KAAMC,IAAK,UAAWlE,MAAO,UAC5D,CACF,CAKD,mBAAMmE,CAAc1B,EAAqB2B,GACvC,IAUE,aATqBnF,KAAKqE,eACxBb,EACA,mBACA,CACEmB,WAAYQ,EACZP,eAAe,KAILhD,OAAOkD,KACtB,CAAC,MAAOnD,GAEP,OADA3B,KAAKc,IAAI,mCAAmC0C,KAAgB7B,GACrD,IACR,CACF,CAKD,oBAAAyD,CACEC,EACAC,GAEAtF,KAAKmD,cAAclB,IAAIoD,EAAWC,EACnC,CAKD,sBAAAC,CAAuBF,GACrBrF,KAAKmD,cAAczB,OAAO2D,EAC3B,CAKD,qBAAMG,CACJC,EACAjC,GAEA,IACE,MAAM8B,EAAUtF,KAAKmD,cAAc1B,IAAIgE,EAAUJ,WACjD,IAAKC,EAIH,YAHAtF,KAAKc,IACH,yCAAyC2E,EAAUJ,mBAKjDC,EAAQG,EAAWjC,EAC1B,CAAC,MAAO7B,GAEP,MADA3B,KAAKc,IAAI,sCAAsC0C,KAAgB7B,GACzDA,CACP,CACF,CAKD,sBAAM+D,CACJD,EACAjC,GAEA,IACE,MAAMmC,QAAgB3F,KAAK0E,iBAAiBlB,GAC5C,IAAKmC,EACH,MAAM,IAAIhF,MAAM,iCAGlB,MAAMiF,EAAUH,EAAUI,EAAIF,EAAQZ,MAChCe,EAAUL,EAAUM,EAAIJ,EAAQX,aAEhChF,KAAKqE,eAAeb,EAAa,2BAA4B,CACjEwC,KAAM,eACNH,EAAGI,KAAKC,MAAMN,GACdG,EAAGE,KAAKC,MAAMJ,GACdK,OAAQ,OACRC,WAAY,EACZC,QAAS,UAGL,IAAI7F,QAASC,GAAY6F,WAAW7F,EAAS,WAE7CT,KAAKqE,eAAeb,EAAa,2BAA4B,CACjEwC,KAAM,gBACNH,EAAGI,KAAKC,MAAMN,GACdG,EAAGE,KAAKC,MAAMJ,GACdK,OAAQ,OACRC,WAAY,EACZC,QAAS,GAEZ,CAAC,MAAO1E,GAEP,MADA3B,KAAKc,IAAI,uCAAuC0C,KAAgB7B,GAC1DA,CACP,CACF,CAKD,uBAAM4E,CACJd,EACAjC,GAEA,IACE,MAAMmC,QAAgB3F,KAAK0E,iBAAiBlB,GAC5C,IAAKmC,EACH,MAAM,IAAIhF,MAAM,iCAGlB,MAAMiF,EAAUH,EAAUI,EAAIF,EAAQZ,MAChCe,EAAUL,EAAUM,EAAIJ,EAAQX,aAEhChF,KAAKqE,eAAeb,EAAa,2BAA4B,CACjEwC,KAAM,aACNH,EAAGI,KAAKC,MAAMN,GACdG,EAAGE,KAAKC,MAAMJ,GACdU,OAAQf,EAAUe,QAAU,EAC5BC,OAAQhB,EAAUgB,QAAU,GAE/B,CAAC,MAAO9E,GAEP,MADA3B,KAAKc,IAAI,wCAAwC0C,KAAgB7B,GAC3DA,CACP,CACF,CAKD,oBAAM+E,CACJjB,EACAjC,GAEA,UACQxD,KAAKqE,eAAeb,EAAa,yBAA0B,CAC/DwC,KAAMP,EAAUkB,SAAW,UAC3BC,IAAKnB,EAAUmB,IACfC,KAAMpB,EAAUoB,KAChBC,KAAMrB,EAAUqB,KAChBC,QAAStB,EAAUsB,SAEtB,CAAC,MAAOpF,GAEP,MADA3B,KAAKc,IAAI,qCAAqC0C,KAAgB7B,GACxDA,CACP,CACF,CAKD,mBAAM8B,CAAcD,GAClB,IACE,MAAMwD,QAAiBC,MACrB,oBAAoBjH,KAAKiD,QAAQG,kBAGnC,aADoC4D,EAASE,QAC9BC,KAAMC,GAAQA,EAAI7F,KAAOiC,IAAgB,IACzD,CAAC,MAAO7B,GAEP,OADA3B,KAAKc,IAAI,iCAAiC0C,KAAgB7B,GACnD,IACR,CACF,CAKD,yBAAA0F,GACErH,KAAKoF,qBAAqB,QAASpF,KAAK0F,iBAAiB4B,KAAKtH,OAC9DA,KAAKoF,qBAAqB,SAAUpF,KAAKuG,kBAAkBe,KAAKtH,OAChEA,KAAKoF,qBAAqB,UAAWpF,KAAK0G,eAAeY,KAAKtH,OAC9DA,KAAKoF,qBAAqB,QAASpF,KAAK0G,eAAeY,KAAKtH,OAC5DA,KAAKoF,qBAAqB,WAAYpF,KAAK0G,eAAeY,KAAKtH,MAChE,CAKD,aAAMuH,GACJ,MAAMC,EAAkBC,MAAMC,KAAK1H,KAAKkD,YAAYyE,QAAQC,IACzDpE,GAAgBxD,KAAKkE,iBAAiBV,UAGnChD,QAAQqH,IAAIL,GAElBxH,KAAKmD,cAAc2E,QACnB9H,KAAKc,IAAI,gCACV,CAKD,QAAAiH,GAUE,MAAO,CACLC,iBAAkBhI,KAAKkD,YAAY+E,KACnC9E,cAAenD,KAAKmD,cAAc8E,KAClC/E,YAAauE,MAAMC,KAAK1H,KAAKkD,YAAYgF,WAAWN,IAClD,EAAEO,EAAOC,MAAW,CAClBD,QACAnG,UAAWoG,EAAKpG,UAChB+B,UAAWqE,EAAKrE,UAChBhE,WAAYqI,EAAKrI,cAIxB,EAQH,MAAMsI,EAAa,IAAIrF,EAAW,CAChCK,OAAO,UAIa,oBAAXiF,SACRA,OAAetF,WAAaqF,EAC5BC,OAAezI,IAAMA"}