{"version": 3, "file": "target-tab-streamer.min.js", "sources": ["../src/target-tab-streamer.ts"], "sourcesContent": [null], "names": ["TargetTabStreamer", "constructor", "signalingServerUrl", "tabId", "autoInitialize", "this", "ws", "isConnected", "captureStream", "controlTabPeerConnection", "console", "log", "init", "connectToSignalingServer", "handleStartStream", "error", "Promise", "resolve", "reject", "WebSocket", "onopen", "sendMessage", "type", "role", "onmessage", "event", "handleSignalingMessage", "JSON", "parse", "data", "onclose", "onerror", "message", "readyState", "OPEN", "send", "stringify", "handleStopStream", "handleOffer", "handleIceCandidate", "stream", "navigator", "mediaDevices", "getDisplayMedia", "video", "mediaSource", "width", "ideal", "height", "frameRate", "audio", "preferCurrentTab", "createControlTabConnection", "getTracks", "for<PERSON>ach", "track", "stop", "close", "RTCPeerConnection", "iceServers", "urls", "addTrack", "onicecandidate", "candidate", "target", "onconnectionstatechange", "connectionState", "setRemoteDescription", "RTCSessionDescription", "offer", "answer", "createAnswer", "setLocalDescription", "addIceCandidate", "RTCIceCandidate", "cleanup", "window", "URLSearchParams", "location", "search", "get"], "mappings": "8CAaA,MAAMA,EAQJ,WAAAC,CACEC,EACAC,EACAC,GATMC,KAAEC,GAAqB,KAEvBD,KAAWE,aAAY,EACvBF,KAAaG,cAAuB,KACpCH,KAAwBI,yBAA6B,KAO3DJ,KAAKH,mBAAqBA,EAC1BG,KAAKF,MAAQA,EAEbO,QAAQC,IAAI,sDACZN,KAAKO,KAAwB,SAAnBR,IAAgD,IAAnBA,EACxC,CAED,UAAMQ,CAAKR,GACT,IAEEM,QAAQC,IAAI,+BAAgCN,KAAKF,aAG3CE,KAAKQ,2BAEPT,SACIC,KAAKS,oBAGbJ,QAAQC,IACN,8DAEH,CAAC,MAAOI,GACPL,QAAQK,MACN,uDACAA,EAEH,CACF,CAED,8BAAMF,GACJ,OAAO,IAAIG,QAAQ,CAACC,EAASC,KAC3B,IACEb,KAAKC,GAAK,IAAIa,UAAUd,KAAKH,oBAE7BG,KAAKC,GAAGc,OAAS,KACfV,QAAQC,IAAI,gDACZN,KAAKE,aAAc,EAGnBF,KAAKgB,YAAY,CACfC,KAAM,WACNC,KAAM,SACNpB,MAAOE,KAAKF,QAGdc,KAGFZ,KAAKC,GAAGkB,UAAaC,IACnBpB,KAAKqB,uBAAuBC,KAAKC,MAAMH,EAAMI,QAG/CxB,KAAKC,GAAGwB,QAAU,KAChBpB,QAAQC,IAAI,qDACZN,KAAKE,aAAc,GAGrBF,KAAKC,GAAGyB,QAAWhB,IACjBL,QAAQK,MAAM,kCAAmCA,GACjDG,EAAOH,GAEV,CAAC,MAAOA,GACPG,EAAOH,EACR,GAEJ,CAEO,WAAAM,CAAYW,GACd3B,KAAKC,IAAMD,KAAKC,GAAG2B,aAAed,UAAUe,MAC9C7B,KAAKC,GAAG6B,KAAKR,KAAKS,UAAUJ,GAE/B,CAEO,4BAAMN,CAAuBM,GAGnC,OAFAtB,QAAQC,IAAI,6CAA8CqB,EAAQV,MAE1DU,EAAQV,MACd,IAAK,qBACGjB,KAAKS,oBACX,MACF,IAAK,oBACGT,KAAKgC,mBACX,MACF,IAAK,cACGhC,KAAKiC,YAAYN,GACvB,MACF,IAAK,sBACG3B,KAAKkC,mBAAmBP,GAC9B,MACF,QACEtB,QAAQC,IAAI,uCAAwCqB,EAAQV,MAEjE,CAEO,uBAAMR,GACZ,IACEJ,QAAQC,IAAI,6CAGZ,MAAM6B,QAAeC,UAAUC,aAAaC,gBAAgB,CAC1DC,MAAO,CACLC,YAAa,SACbC,MAAO,CAAEC,MAAO,MAChBC,OAAQ,CAAED,MAAO,MACjBE,UAAW,CAAEF,MAAO,KAEtBG,OAAO,EACPC,kBAAkB,IAGpB9C,KAAKG,cAAgBgC,QAGfnC,KAAK+C,6BAEX1C,QAAQC,IAAI,sDAGZN,KAAKgB,YAAY,CACfC,KAAM,iBACNnB,MAAOE,KAAKF,OAEf,CAAC,MAAOY,GACPL,QAAQK,MAAM,iDAAkDA,GAChEV,KAAKgB,YAAY,CACfC,KAAM,eACNnB,MAAOE,KAAKF,MACZY,MAAOA,EAAMiB,SAEhB,CACF,CAEO,sBAAMK,GACZ,IACE3B,QAAQC,IAAI,qCAERN,KAAKG,gBACPH,KAAKG,cAAc6C,YAAYC,QAASC,GAAUA,EAAMC,QACxDnD,KAAKG,cAAgB,MAGnBH,KAAKI,2BACPJ,KAAKI,yBAAyBgD,QAC9BpD,KAAKI,yBAA2B,MAGlCC,QAAQC,IAAI,iCAEZN,KAAKgB,YAAY,CACfC,KAAM,iBACNnB,MAAOE,KAAKF,OAEf,CAAC,MAAOY,GACPL,QAAQK,MAAM,wCAAyCA,EACxD,CACF,CAEO,gCAAMqC,GACZ/C,KAAKI,yBAA2B,IAAIiD,kBAAkB,CACpDC,WAAY,CAAC,CAAEC,KAAM,mCAInBvD,KAAKG,eACPH,KAAKG,cAAc6C,YAAYC,QAASC,IACtClD,KAAKI,yBAA0BoD,SAASN,EAAOlD,KAAKG,iBAKxDH,KAAKI,yBAAyBqD,eAAkBrC,IAC1CA,EAAMsC,WACR1D,KAAKgB,YAAY,CACfC,KAAM,gBACNyC,UAAWtC,EAAMsC,UACjB5D,MAAOE,KAAKF,MACZ6D,OAAQ,aAMd3D,KAAKI,yBAAyBwD,wBAA0B,KACtDvD,QAAQC,IACN,+CACAN,KAAKI,yBAA0ByD,iBAGpC,CAEO,iBAAM5B,CAAYN,GACxB,IACO3B,KAAKI,gCACFJ,KAAK+C,mCAGP/C,KAAKI,yBAA0B0D,qBACnC,IAAIC,sBAAsBpC,EAAQqC,QAGpC,MAAMC,QAAejE,KAAKI,yBAA0B8D,qBAC9ClE,KAAKI,yBAA0B+D,oBAAoBF,GAEzDjE,KAAKgB,YAAY,CACfC,KAAM,SACNgD,OAAQA,EACRnE,MAAOE,KAAKF,MACZ6D,OAAQ,WAEX,CAAC,MAAOjD,GACPL,QAAQK,MAAM,uCAAwCA,EACvD,CACF,CAEO,wBAAMwB,CAAmBP,GAC/B,IACM3B,KAAKI,0BAA4BuB,EAAQ+B,iBACrC1D,KAAKI,yBAAyBgE,gBAClC,IAAIC,gBAAgB1C,EAAQ+B,WAGjC,CAAC,MAAOhD,GACPL,QAAQK,MAAM,+CAAgDA,EAC/D,CACF,CAED,OAAA4D,GACMtE,KAAKG,eACPH,KAAKG,cAAc6C,YAAYC,QAASC,GAAUA,EAAMC,QAGtDnD,KAAKI,0BACPJ,KAAKI,yBAAyBgD,QAG5BpD,KAAKC,IACPD,KAAKC,GAAGmD,OAEX,QAIH,MAIE,GAAKmB,OAAe5E,kBAElB,YADAU,QAAQC,IAAI,sDAKbiE,OAAe5E,kBAAoBA,EAGpC,MAGMI,EAHY,IAAIyE,gBAAgBD,OAAOE,SAASC,QAGrBC,IAAI,mBAAqB,QAGxD,IAAIhF,EALqB,0BACb,YAIqCI,EAEpD,EArBD"}