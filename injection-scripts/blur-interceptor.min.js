var BlurInterceptor=function(){"use strict";class t{constructor(i,s={}){if(this.isInitialized=!1,this.isEnabled=!0,this.processor=null,this.generator=null,this.transformStream=null,this.originalTrack=null,this.processedTrack=null,this.constructor===t)throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");this.name=i,this.type=this.constructor.name,this.config={debug:!1,enabled:!0,...s},this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} interceptor created`)}async initialize(t){if(!t||"video"!==t.kind)throw new Error(`${this.name} interceptor requires a valid video track`);this.originalTrack=t,this.log(`Initializing ${this.name} interceptor with video track:`,t.label);try{return this.processor=new MediaStreamTrackProcessor({track:t}),this.generator=new MediaStreamTrackGenerator({kind:"video"}),this.transformStream=new TransformStream({transform:this.processFrame.bind(this)}),this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(t=>{this.log(`Pipeline error in ${this.name}:`,t),this.stats.errorsEncountered++}),this.processedTrack=this.generator.track,this.isInitialized=!0,this.log(`${this.name} interceptor initialized successfully`),this.processedTrack}catch(t){throw this.log(`Error initializing ${this.name} interceptor:`,t),this.stats.errorsEncountered++,t}}async processFrame(t,i){const s=performance.now();try{if(this.stats.framesProcessed++,!this.isEnabled||!this.config.enabled)return void i.enqueue(t);const s=await this.processVideoFrame(t);i.enqueue(s),s!==t&&t.close()}catch(s){this.log(`Error processing frame in ${this.name}:`,s),this.stats.errorsEncountered++,i.enqueue(t)}finally{const t=performance.now()-s;this.stats.lastProcessingTime=t,this.stats.totalProcessingTime+=t,this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}}updateConfig(t){if(!t||"object"!=typeof t)return void this.log("warn","Invalid configuration provided to updateConfig");const i={...this.config};this.config={...this.config,...t},this.log(`${this.name} configuration updated:`,{old:i,new:this.config}),this.onConfigChange&&this.onConfigChange(i,this.config)}getConfig(){return{...this.config}}enable(){this.isEnabled=!0,this.config.enabled=!0,this.log(`${this.name} interceptor enabled`)}disable(){this.isEnabled=!1,this.config.enabled=!1,this.log(`${this.name} interceptor disabled`)}getStats(){return{...this.stats}}resetStats(){this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} stats reset`)}async cleanup(){try{this.processor&&(this.processor=null),this.generator&&(this.generator=null),this.transformStream&&(this.transformStream=null),this.originalTrack&&(this.originalTrack=null),this.processedTrack&&(this.processedTrack=null),this.isInitialized=!1,this.log(`${this.name} interceptor cleaned up`)}catch(t){this.log(`Error cleaning up ${this.name} interceptor:`,t)}}log(...t){this.config.debug&&console.log(`[${this.name}-Interceptor]`,...t)}getMetadata(){return{name:this.name,type:this.type,isInitialized:this.isInitialized,isEnabled:this.isEnabled,config:this.getConfig(),stats:this.getStats()}}}"undefined"!=typeof window&&(window.BaseInterceptor=t);class i extends t{constructor(t="blur-effect",i={}){super(t,{debug:!1,blurRadius:0,maxBlurRadius:20,blurType:"gaussian",...i}),this.canvas=null,this.ctx=null,this.onConfigChange=this.onConfigUpdate.bind(this),this.log("BlurInterceptor initialized",this.config)}onConfigUpdate(t,i){const s=t.blurRadius||0,e=i.blurRadius||0;e<0?(this.config.blurRadius=0,this.log("warn","Blur radius clamped to minimum: 0")):e>this.config.maxBlurRadius&&(this.config.blurRadius=this.config.maxBlurRadius,this.log("warn",`Blur radius clamped to maximum: ${this.config.maxBlurRadius}`)),this.log("info",`Blur radius updated from ${s} to ${this.config.blurRadius}`)}async processVideoFrame(t){if((this.config.blurRadius||0)<=0)return t;try{if(this.canvas&&this.canvas.width===t.codedWidth&&this.canvas.height===t.codedHeight||this.initializeCanvas(t.codedWidth,t.codedHeight),!this.ctx)throw new Error("Canvas context not available");const i=Math.min(this.config.blurRadius||0,this.config.maxBlurRadius);this.ctx.filter=`blur(${i}px)`,this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.ctx.drawImage(t,0,0),this.ctx.filter="none";return new VideoFrame(this.canvas,{timestamp:t.timestamp,duration:t.duration??void 0})}catch(i){return this.log("Error processing blur:",i),t}}initializeCanvas(t,i){if(this.canvas=document.createElement("canvas"),this.canvas.width=t,this.canvas.height=i,this.ctx=this.canvas.getContext("2d"),!this.ctx)throw new Error("Failed to get 2D canvas context");this.ctx.imageSmoothingEnabled=!0,this.ctx.imageSmoothingQuality="high",this.log(`Canvas initialized: ${t}x${i}`)}setBlurRadius(t){const i=Math.max(0,Math.min(this.config.maxBlurRadius,t));this.updateConfig({blurRadius:i}),this.log(`Blur radius set to: ${i}px`)}getBlurRadius(){return this.config.blurRadius||0}setBlurType(t){["gaussian","motion"].includes(t)?(this.updateConfig({blurType:t}),this.log(`Blur type set to: ${t}`)):this.log(`Invalid blur type: ${t}. Using 'gaussian'.`)}setBlurEnabled(t){const i=t?this.config.blurRadius||5:0;this.setBlurRadius(i)}async cleanup(){this.log("Cleaning up blur interceptor..."),this.canvas&&(this.canvas.width=0,this.canvas.height=0,this.canvas=null),this.ctx=null,await super.cleanup(),this.log("Blur interceptor cleanup complete")}getStatus(){return{...this.getMetadata(),blurRadius:this.config.blurRadius||0,blurType:this.config.blurType,blurEnabled:(this.config.blurRadius||0)>0,canvasInitialized:!!this.canvas,canvasSize:this.canvas?`${this.canvas.width}x${this.canvas.height}`:null}}}return"undefined"!=typeof window&&(window.BlurInterceptor=i),i}();
//# sourceMappingURL=blur-interceptor.min.js.map
