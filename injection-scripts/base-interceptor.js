var BaseInterceptor = (function () {
    'use strict';

    /**
     * Base Interceptor Interface
     *
     * Defines the standard interface that all video frame interceptors must implement.
     * Provides common functionality for configuration management, lifecycle, and processing.
     *
     * Features:
     * - Standardized interceptor interface
     * - Built-in configuration management
     * - Lifecycle management (initialize, process, cleanup)
     * - Error handling and fallback mechanisms
     * - Performance monitoring capabilities
     */
    class BaseInterceptor {
        constructor(name, defaultConfig = {}) {
            this.isInitialized = false;
            this.isEnabled = true;
            // Transform stream components
            this.processor = null;
            this.generator = null;
            this.transformStream = null;
            // Track references
            this.originalTrack = null;
            this.processedTrack = null;
            if (this.constructor === BaseInterceptor) {
                throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");
            }
            this.name = name;
            this.type = this.constructor.name;
            // Configuration management - stored internally
            this.config = {
                debug: false,
                enabled: true,
                ...defaultConfig,
            };
            // Performance tracking
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} interceptor created`);
        }
        /**
         * Initialize the interceptor with a video track
         */
        async initialize(videoTrack) {
            if (!videoTrack || videoTrack.kind !== "video") {
                throw new Error(`${this.name} interceptor requires a valid video track`);
            }
            this.originalTrack = videoTrack;
            this.log(`Initializing ${this.name} interceptor with video track:`, videoTrack.label);
            try {
                // Create processor and generator
                this.processor = new MediaStreamTrackProcessor({ track: videoTrack });
                this.generator = new MediaStreamTrackGenerator({ kind: "video" });
                // Create transform stream with bound processing function
                this.transformStream = new TransformStream({
                    transform: this.processFrame.bind(this),
                });
                // Connect the pipeline
                this.processor.readable
                    .pipeThrough(this.transformStream)
                    .pipeTo(this.generator.writable)
                    .catch((error) => {
                    this.log(`Pipeline error in ${this.name}:`, error);
                    this.stats.errorsEncountered++;
                });
                this.processedTrack = this.generator.track;
                this.isInitialized = true;
                this.log(`${this.name} interceptor initialized successfully`);
                return this.processedTrack;
            }
            catch (error) {
                this.log(`Error initializing ${this.name} interceptor:`, error);
                this.stats.errorsEncountered++;
                throw error;
            }
        }
        /**
         * Process a single video frame - handles common logic and delegates to subclass
         */
        async processFrame(frame, controller) {
            const startTime = performance.now();
            try {
                this.stats.framesProcessed++;
                if (!this.isEnabled || !this.config.enabled) {
                    // Pass through original frame if disabled
                    controller.enqueue(frame);
                    return;
                }
                // Call the specific interceptor's processing logic
                const processedFrame = await this.processVideoFrame(frame);
                // Enqueue the processed frame
                controller.enqueue(processedFrame);
                // Clean up original frame if a new one was created
                if (processedFrame !== frame) {
                    frame.close();
                }
            }
            catch (error) {
                this.log(`Error processing frame in ${this.name}:`, error);
                this.stats.errorsEncountered++;
                // Fallback: pass through original frame
                controller.enqueue(frame);
            }
            finally {
                // Update performance stats
                const processingTime = performance.now() - startTime;
                this.stats.lastProcessingTime = processingTime;
                this.stats.totalProcessingTime += processingTime;
                this.stats.averageProcessingTime =
                    this.stats.totalProcessingTime / this.stats.framesProcessed;
            }
        }
        /**
         * Update interceptor configuration
         */
        updateConfig(newConfig) {
            if (!newConfig || typeof newConfig !== "object") {
                this.log("warn", "Invalid configuration provided to updateConfig");
                return;
            }
            const oldConfig = { ...this.config };
            this.config = { ...this.config, ...newConfig };
            this.log(`${this.name} configuration updated:`, {
                old: oldConfig,
                new: this.config,
            });
            // Call configuration change handler if implemented
            if (this.onConfigChange) {
                this.onConfigChange(oldConfig, this.config);
            }
        }
        /**
         * Get current configuration
         */
        getConfig() {
            return { ...this.config };
        }
        /**
         * Enable the interceptor
         */
        enable() {
            this.isEnabled = true;
            this.config.enabled = true;
            this.log(`${this.name} interceptor enabled`);
        }
        /**
         * Disable the interceptor
         */
        disable() {
            this.isEnabled = false;
            this.config.enabled = false;
            this.log(`${this.name} interceptor disabled`);
        }
        /**
         * Get performance statistics
         */
        getStats() {
            return { ...this.stats };
        }
        /**
         * Reset performance statistics
         */
        resetStats() {
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} stats reset`);
        }
        /**
         * Cleanup resources
         */
        async cleanup() {
            try {
                if (this.processor) {
                    this.processor = null;
                }
                if (this.generator) {
                    this.generator = null;
                }
                if (this.transformStream) {
                    this.transformStream = null;
                }
                if (this.originalTrack) {
                    this.originalTrack = null;
                }
                if (this.processedTrack) {
                    this.processedTrack = null;
                }
                this.isInitialized = false;
                this.log(`${this.name} interceptor cleaned up`);
            }
            catch (error) {
                this.log(`Error cleaning up ${this.name} interceptor:`, error);
            }
        }
        /**
         * Logging utility
         */
        log(...args) {
            if (this.config.debug) {
                console.log(`[${this.name}-Interceptor]`, ...args);
            }
        }
        /**
         * Get interceptor metadata
         */
        getMetadata() {
            return {
                name: this.name,
                type: this.type,
                isInitialized: this.isInitialized,
                isEnabled: this.isEnabled,
                config: this.getConfig(),
                stats: this.getStats(),
            };
        }
    }
    // Make available globally for injection scripts
    if (typeof window !== "undefined") {
        window.BaseInterceptor = BaseInterceptor;
    }

    return BaseInterceptor;

})();
//# sourceMappingURL=base-interceptor.js.map
