var BlurInterceptor = (function () {
    'use strict';

    /**
     * Base Interceptor Interface
     *
     * Defines the standard interface that all video frame interceptors must implement.
     * Provides common functionality for configuration management, lifecycle, and processing.
     *
     * Features:
     * - Standardized interceptor interface
     * - Built-in configuration management
     * - Lifecycle management (initialize, process, cleanup)
     * - Error handling and fallback mechanisms
     * - Performance monitoring capabilities
     */
    class BaseInterceptor {
        constructor(name, defaultConfig = {}) {
            this.isInitialized = false;
            this.isEnabled = true;
            // Transform stream components
            this.processor = null;
            this.generator = null;
            this.transformStream = null;
            // Track references
            this.originalTrack = null;
            this.processedTrack = null;
            if (this.constructor === BaseInterceptor) {
                throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");
            }
            this.name = name;
            this.type = this.constructor.name;
            // Configuration management - stored internally
            this.config = {
                debug: false,
                enabled: true,
                ...defaultConfig,
            };
            // Performance tracking
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} interceptor created`);
        }
        /**
         * Initialize the interceptor with a video track
         */
        async initialize(videoTrack) {
            if (!videoTrack || videoTrack.kind !== "video") {
                throw new Error(`${this.name} interceptor requires a valid video track`);
            }
            this.originalTrack = videoTrack;
            this.log(`Initializing ${this.name} interceptor with video track:`, videoTrack.label);
            try {
                // Create processor and generator
                this.processor = new MediaStreamTrackProcessor({ track: videoTrack });
                this.generator = new MediaStreamTrackGenerator({ kind: "video" });
                // Create transform stream with bound processing function
                this.transformStream = new TransformStream({
                    transform: this.processFrame.bind(this),
                });
                // Connect the pipeline
                this.processor.readable
                    .pipeThrough(this.transformStream)
                    .pipeTo(this.generator.writable)
                    .catch((error) => {
                    this.log(`Pipeline error in ${this.name}:`, error);
                    this.stats.errorsEncountered++;
                });
                this.processedTrack = this.generator.track;
                this.isInitialized = true;
                this.log(`${this.name} interceptor initialized successfully`);
                return this.processedTrack;
            }
            catch (error) {
                this.log(`Error initializing ${this.name} interceptor:`, error);
                this.stats.errorsEncountered++;
                throw error;
            }
        }
        /**
         * Process a single video frame - handles common logic and delegates to subclass
         */
        async processFrame(frame, controller) {
            const startTime = performance.now();
            try {
                this.stats.framesProcessed++;
                if (!this.isEnabled || !this.config.enabled) {
                    // Pass through original frame if disabled
                    controller.enqueue(frame);
                    return;
                }
                // Call the specific interceptor's processing logic
                const processedFrame = await this.processVideoFrame(frame);
                // Enqueue the processed frame
                controller.enqueue(processedFrame);
                // Clean up original frame if a new one was created
                if (processedFrame !== frame) {
                    frame.close();
                }
            }
            catch (error) {
                this.log(`Error processing frame in ${this.name}:`, error);
                this.stats.errorsEncountered++;
                // Fallback: pass through original frame
                controller.enqueue(frame);
            }
            finally {
                // Update performance stats
                const processingTime = performance.now() - startTime;
                this.stats.lastProcessingTime = processingTime;
                this.stats.totalProcessingTime += processingTime;
                this.stats.averageProcessingTime =
                    this.stats.totalProcessingTime / this.stats.framesProcessed;
            }
        }
        /**
         * Update interceptor configuration
         */
        updateConfig(newConfig) {
            if (!newConfig || typeof newConfig !== "object") {
                this.log("warn", "Invalid configuration provided to updateConfig");
                return;
            }
            const oldConfig = { ...this.config };
            this.config = { ...this.config, ...newConfig };
            this.log(`${this.name} configuration updated:`, {
                old: oldConfig,
                new: this.config,
            });
            // Call configuration change handler if implemented
            if (this.onConfigChange) {
                this.onConfigChange(oldConfig, this.config);
            }
        }
        /**
         * Get current configuration
         */
        getConfig() {
            return { ...this.config };
        }
        /**
         * Enable the interceptor
         */
        enable() {
            this.isEnabled = true;
            this.config.enabled = true;
            this.log(`${this.name} interceptor enabled`);
        }
        /**
         * Disable the interceptor
         */
        disable() {
            this.isEnabled = false;
            this.config.enabled = false;
            this.log(`${this.name} interceptor disabled`);
        }
        /**
         * Get performance statistics
         */
        getStats() {
            return { ...this.stats };
        }
        /**
         * Reset performance statistics
         */
        resetStats() {
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} stats reset`);
        }
        /**
         * Cleanup resources
         */
        async cleanup() {
            try {
                if (this.processor) {
                    this.processor = null;
                }
                if (this.generator) {
                    this.generator = null;
                }
                if (this.transformStream) {
                    this.transformStream = null;
                }
                if (this.originalTrack) {
                    this.originalTrack = null;
                }
                if (this.processedTrack) {
                    this.processedTrack = null;
                }
                this.isInitialized = false;
                this.log(`${this.name} interceptor cleaned up`);
            }
            catch (error) {
                this.log(`Error cleaning up ${this.name} interceptor:`, error);
            }
        }
        /**
         * Logging utility
         */
        log(...args) {
            if (this.config.debug) {
                console.log(`[${this.name}-Interceptor]`, ...args);
            }
        }
        /**
         * Get interceptor metadata
         */
        getMetadata() {
            return {
                name: this.name,
                type: this.type,
                isInitialized: this.isInitialized,
                isEnabled: this.isEnabled,
                config: this.getConfig(),
                stats: this.getStats(),
            };
        }
    }
    // Make available globally for injection scripts
    if (typeof window !== "undefined") {
        window.BaseInterceptor = BaseInterceptor;
    }

    /**
     * Blur Effect Interceptor
     *
     * A video frame interceptor that applies blur effects to video frames.
     * Demonstrates the multi-interceptor system with CSS filter-based blur.
     *
     * Features:
     * - Real-time blur effect application
     * - Configurable blur intensity
     * - Extends BaseInterceptor for standardized interface
     * - CSS filter-based processing for performance
     */
    class BlurInterceptor extends BaseInterceptor {
        constructor(name = "blur-effect", options = {}) {
            // Default configuration for blur interceptor
            const defaultConfig = {
                debug: false,
                blurRadius: 0, // 0 = no blur, higher values = more blur
                maxBlurRadius: 20,
                blurType: "gaussian", // 'gaussian' or 'motion'
                ...options,
            };
            super(name, defaultConfig);
            // Canvas for frame processing
            this.canvas = null;
            this.ctx = null;
            // Set up configuration change handler
            this.onConfigChange = this.onConfigUpdate.bind(this);
            this.log("BlurInterceptor initialized", this.config);
        }
        /**
         * Handle configuration updates
         */
        onConfigUpdate(oldConfig, newConfig) {
            const oldBlurRadius = oldConfig.blurRadius || 0;
            const newBlurRadius = newConfig.blurRadius || 0;
            // Validate blur radius value
            if (newBlurRadius < 0) {
                this.config.blurRadius = 0;
                this.log("warn", "Blur radius clamped to minimum: 0");
            }
            else if (newBlurRadius > this.config.maxBlurRadius) {
                this.config.blurRadius = this.config.maxBlurRadius;
                this.log("warn", `Blur radius clamped to maximum: ${this.config.maxBlurRadius}`);
            }
            this.log("info", `Blur radius updated from ${oldBlurRadius} to ${this.config.blurRadius}`);
        }
        /**
         * Process a video frame - implements BaseInterceptor interface
         */
        async processVideoFrame(frame) {
            // If blur radius is 0, pass through unchanged
            if ((this.config.blurRadius || 0) <= 0) {
                return frame;
            }
            try {
                // Initialize canvas if needed
                if (!this.canvas ||
                    this.canvas.width !== frame.codedWidth ||
                    this.canvas.height !== frame.codedHeight) {
                    this.initializeCanvas(frame.codedWidth, frame.codedHeight);
                }
                if (!this.ctx) {
                    throw new Error("Canvas context not available");
                }
                // Apply blur filter to canvas context
                const blurRadius = Math.min(this.config.blurRadius || 0, this.config.maxBlurRadius);
                this.ctx.filter = `blur(${blurRadius}px)`;
                // Clear canvas and draw frame with blur effect
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.drawImage(frame, 0, 0);
                // Reset filter for future operations
                this.ctx.filter = "none";
                // Create new VideoFrame from canvas
                const processedFrame = new VideoFrame(this.canvas, {
                    timestamp: frame.timestamp,
                    duration: frame.duration ?? undefined,
                });
                return processedFrame;
            }
            catch (error) {
                this.log("Error processing blur:", error);
                // Return original frame on error
                return frame;
            }
        }
        /**
         * Initialize canvas for frame processing
         */
        initializeCanvas(width, height) {
            this.canvas = document.createElement("canvas");
            this.canvas.width = width;
            this.canvas.height = height;
            this.ctx = this.canvas.getContext("2d");
            if (!this.ctx) {
                throw new Error("Failed to get 2D canvas context");
            }
            // Set canvas properties for better quality
            this.ctx.imageSmoothingEnabled = true;
            this.ctx.imageSmoothingQuality = "high";
            this.log(`Canvas initialized: ${width}x${height}`);
        }
        /**
         * Set blur radius
         */
        setBlurRadius(radius) {
            const clampedRadius = Math.max(0, Math.min(this.config.maxBlurRadius, radius));
            this.updateConfig({ blurRadius: clampedRadius });
            this.log(`Blur radius set to: ${clampedRadius}px`);
        }
        /**
         * Get current blur radius
         */
        getBlurRadius() {
            return this.config.blurRadius || 0;
        }
        /**
         * Set blur type
         */
        setBlurType(type) {
            if (["gaussian", "motion"].includes(type)) {
                this.updateConfig({ blurType: type });
                this.log(`Blur type set to: ${type}`);
            }
            else {
                this.log(`Invalid blur type: ${type}. Using 'gaussian'.`);
            }
        }
        /**
         * Enable/disable blur effect
         */
        setBlurEnabled(enabled) {
            const radius = enabled ? this.config.blurRadius || 5 : 0;
            this.setBlurRadius(radius);
        }
        /**
         * Override cleanup to handle canvas resources
         */
        async cleanup() {
            this.log("Cleaning up blur interceptor...");
            // Clean up canvas resources
            if (this.canvas) {
                this.canvas.width = 0;
                this.canvas.height = 0;
                this.canvas = null;
            }
            this.ctx = null;
            // Call parent cleanup
            await super.cleanup();
            this.log("Blur interceptor cleanup complete");
        }
        /**
         * Get interceptor-specific status
         */
        getStatus() {
            return {
                ...this.getMetadata(),
                blurRadius: this.config.blurRadius || 0,
                blurType: this.config.blurType,
                blurEnabled: (this.config.blurRadius || 0) > 0,
                canvasInitialized: !!this.canvas,
                canvasSize: this.canvas
                    ? `${this.canvas.width}x${this.canvas.height}`
                    : null,
            };
        }
    }
    // Make available globally for injection scripts
    if (typeof window !== "undefined") {
        window.BlurInterceptor = BlurInterceptor;
    }

    return BlurInterceptor;

})();
//# sourceMappingURL=blur-interceptor.js.map
