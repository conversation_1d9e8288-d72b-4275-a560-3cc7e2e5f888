/**
 * Blur Effect Interceptor
 *
 * A video frame interceptor that applies blur effects to video frames.
 * Demonstrates the multi-interceptor system with CSS filter-based blur.
 *
 * Features:
 * - Real-time blur effect application
 * - Configurable blur intensity
 * - Extends BaseInterceptor for standardized interface
 * - CSS filter-based processing for performance
 */

class BlurInterceptor extends BaseInterceptor {
  constructor(name = "blur-effect", options = {}) {
    // Default configuration for blur interceptor
    const defaultConfig = {
      debug: false,
      blurRadius: 0, // 0 = no blur, higher values = more blur
      maxBlurRadius: 20,
      blurType: "gaussian", // 'gaussian' or 'motion'
      ...options,
    };

    super(name, defaultConfig);

    // Canvas for frame processing
    this.canvas = null;
    this.ctx = null;

    this.log("BlurInterceptor initialized", this.config);
  }

  /**
   * Handle configuration updates
   * @param {Object} oldConfig - Previous configuration
   * @param {Object} newConfig - New configuration
   */
  onConfigUpdate(oldConfig, newConfig) {
    // Validate blur radius value
    if (newConfig.blurRadius < this.config.minBlurRadius) {
      this.config.blurRadius = this.config.minBlurRadius;
      this.log(
        "warn",
        `Blur radius clamped to minimum: ${this.config.minBlurRadius}`
      );
    } else if (newConfig.blurRadius > this.config.maxBlurRadius) {
      this.config.blurRadius = this.config.maxBlurRadius;
      this.log(
        "warn",
        `Blur radius clamped to maximum: ${this.config.maxBlurRadius}`
      );
    }

    this.log(
      "info",
      `Blur radius updated from ${oldConfig.blurRadius} to ${this.config.blurRadius}`
    );
  }

  /**
   * Process a video frame - implements BaseInterceptor interface
   * @param {VideoFrame} frame - Input video frame
   * @returns {VideoFrame} - Processed video frame with blur effect
   */
  async processVideoFrame(frame) {
    // If blur radius is 0, pass through unchanged
    if (this.config.blurRadius <= 0) {
      return frame;
    }

    try {
      // Initialize canvas if needed
      if (
        !this.canvas ||
        this.canvas.width !== frame.codedWidth ||
        this.canvas.height !== frame.codedHeight
      ) {
        this.initializeCanvas(frame.codedWidth, frame.codedHeight);
      }

      // Apply blur filter to canvas context
      const blurRadius = Math.min(
        this.config.blurRadius,
        this.config.maxBlurRadius
      );
      this.ctx.filter = `blur(${blurRadius}px)`;

      // Clear canvas and draw frame with blur effect
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.drawImage(frame, 0, 0);

      // Reset filter for future operations
      this.ctx.filter = "none";

      // Create new VideoFrame from canvas
      const processedFrame = new VideoFrame(this.canvas, {
        timestamp: frame.timestamp,
        duration: frame.duration,
      });

      return processedFrame;
    } catch (error) {
      this.log("Error processing blur:", error);
      // Return original frame on error
      return frame;
    }
  }

  /**
   * Initialize canvas for frame processing
   * @param {number} width - Frame width
   * @param {number} height - Frame height
   */
  initializeCanvas(width, height) {
    this.canvas = document.createElement("canvas");
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext("2d");

    // Set canvas properties for better quality
    this.ctx.imageSmoothingEnabled = true;
    this.ctx.imageSmoothingQuality = "high";

    this.log(`Canvas initialized: ${width}x${height}`);
  }

  /**
   * Set blur radius
   * @param {number} radius - Blur radius in pixels (0 to maxBlurRadius)
   */
  setBlurRadius(radius) {
    const clampedRadius = Math.max(
      0,
      Math.min(this.config.maxBlurRadius, radius)
    );
    this.updateConfig({ blurRadius: clampedRadius });
    this.log(`Blur radius set to: ${clampedRadius}px`);
  }

  /**
   * Get current blur radius
   * @returns {number} - Current blur radius in pixels
   */
  getBlurRadius() {
    return this.config.blurRadius;
  }

  /**
   * Set blur type
   * @param {string} type - Blur type ('gaussian' or 'motion')
   */
  setBlurType(type) {
    if (["gaussian", "motion"].includes(type)) {
      this.updateConfig({ blurType: type });
      this.log(`Blur type set to: ${type}`);
    } else {
      this.log(`Invalid blur type: ${type}. Using 'gaussian'.`);
    }
  }

  /**
   * Enable/disable blur effect
   * @param {boolean} enabled - Whether to enable blur
   */
  setBlurEnabled(enabled) {
    const radius = enabled ? this.config.blurRadius || 5 : 0;
    this.setBlurRadius(radius);
  }

  /**
   * Override cleanup to handle canvas resources
   */
  cleanup() {
    this.log("Cleaning up blur interceptor...");

    // Clean up canvas resources
    if (this.canvas) {
      this.canvas.width = 0;
      this.canvas.height = 0;
      this.canvas = null;
    }

    this.ctx = null;

    // Call parent cleanup
    super.cleanup();

    this.log("Blur interceptor cleanup complete");
  }

  /**
   * Get interceptor-specific status
   * @returns {Object} - Status information
   */
  getStatus() {
    return {
      ...this.getMetadata(),
      blurRadius: this.config.blurRadius,
      blurType: this.config.blurType,
      blurEnabled: this.config.blurRadius > 0,
      canvasInitialized: !!this.canvas,
      canvasSize: this.canvas
        ? `${this.canvas.width}x${this.canvas.height}`
        : null,
    };
  }
}

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = BlurInterceptor;
} else if (typeof window !== "undefined") {
  window.BlurInterceptor = BlurInterceptor;
}
