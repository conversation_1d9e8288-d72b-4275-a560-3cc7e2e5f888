var ControlTabScript=function(){"use strict";class e{constructor(){this.signalingServerUrl="ws://localhost:8080",this.websocket=null,this.isConnected=!1,this.rtcConfig={iceServers:[{urls:"stun:stun.cloudflare.com:3478"},{urls:"stun:stun.l.google.com:19302"}],iceCandidatePoolSize:10},this.webClientGroups=new Map,this.tabGroups=new Map,this.targetConnections=new Map,this.clientInterceptorConfigs=new Map,this.defaultInterceptorConfig={interceptorNames:["change-detector","brightness-filter","blur-effect","video-crop"],interceptorConfigs:{"video-crop":{enabled:!0,enableCropping:!0,cropRegion:{x:0,y:0,width:window.innerWidth,height:window.innerHeight}},"change-detector":{enabled:!1,changeThreshold:5,stabilityThreshold:1,consecutiveStableFrames:3,maxWaitDuration:5e3,comparisonInterval:100,pixelSampling:2},"brightness-filter":{enabled:!0,brightness:1.2},"blur-effect":{enabled:!0,blurRadius:3}}},this.cdpManager=null,this.init()}async init(){console.log("[POC-Streaming] Initializing control tab manager..."),this.initializeInterceptorRegistry(),await this.initializeCDPManager(),await this.connectToSignalingServer(),console.log("[POC-Streaming] Control tab manager initialized successfully")}async initializeCDPManager(){try{if(void 0===window.CDPManager)throw new Error("CDPManager not available - ensure CDP manager is injected first");this.cdpManager=window.CDPManager,this.cdpManager&&"function"==typeof this.cdpManager.initializeDefaultHandlers&&this.cdpManager.initializeDefaultHandlers(),console.log("[POC-Streaming] CDP Manager initialized successfully")}catch(e){throw console.error("[POC-Streaming] Failed to initialize CDP Manager:",e),e}}initializeInterceptorRegistry(){void 0!==window.interceptorRegistry?(void 0!==window.VideoFrameInterceptor&&window.interceptorRegistry.register("video-crop",window.VideoFrameInterceptor,{debug:!0,enableCropping:!0}),void 0!==window.BrightnessInterceptor&&window.interceptorRegistry.register("brightness-filter",window.BrightnessInterceptor,{debug:!0,brightness:1}),void 0!==window.BlurInterceptor&&window.interceptorRegistry.register("blur-effect",window.BlurInterceptor,{debug:!0,blurRadius:0}),void 0!==window.ChangeDetectorInterceptor&&window.interceptorRegistry.register("change-detector",window.ChangeDetectorInterceptor,{debug:!0,enabled:!1,changeThreshold:5,stabilityThreshold:1,consecutiveStableFrames:3,maxWaitDuration:5e3,comparisonInterval:100,pixelSampling:2}),console.log("[POC-Streaming] Interceptor registry initialized with:",window.interceptorRegistry.getRegisteredInterceptors())):console.warn("[POC-Streaming] Interceptor registry not available")}async connectToSignalingServer(){return new Promise((e,n)=>{try{this.websocket=new WebSocket(this.signalingServerUrl),this.websocket.onopen=()=>{console.log("[POC-Streaming] Connected to signaling server"),this.isConnected=!0,e()},this.websocket.onmessage=e=>{this.handleSignalingMessage(JSON.parse(e.data))},this.websocket.onclose=()=>{console.log("[POC-Streaming] Disconnected from signaling server"),this.isConnected=!1},this.websocket.onerror=e=>{console.error("[POC-Streaming] WebSocket error:",e),n(e)}}catch(e){console.error("[POC-Streaming] Failed to connect to signaling server:",e),n(e)}})}handleSignalingMessage(e){switch(console.log("[POC-Streaming] Received signaling message:",e),e.type){case"web-client-offer":this.handleWebClientOffer(e);break;case"web-client-ice-candidate":this.handleWebClientIceCandidate(e);break;case"user-event":this.handleUserEvent(e);break;case"interceptor-config-update":this.handleInterceptorConfigUpdate(e);break;default:console.warn("[POC-Streaming] Unknown message type:",e.type)}}async handleWebClientOffer(e){try{const{webClientId:n,offer:t}=e;console.log(`[POC-Streaming] Handling offer from web client: ${n}`);const i=new RTCPeerConnection(this.rtcConfig),r=i.createDataChannel("control",{ordered:!0});this.webClientGroups.set(n,{peerConnection:i,interceptorPipeline:null,dataChannel:r,webClient:{id:n}}),await i.setRemoteDescription(t);const o=await i.createAnswer();await i.setLocalDescription(o),this.sendMessage({type:"control-tab-answer",webClientId:n,answer:o}),console.log(`[POC-Streaming] Answer sent to web client: ${n}`)}catch(e){console.error("[POC-Streaming] Error handling web client offer:",e)}}async handleWebClientIceCandidate(e){try{const{webClientId:n,candidate:t}=e,i=this.webClientGroups.get(n);i&&(await i.peerConnection.addIceCandidate(t),console.log(`[POC-Streaming] ICE candidate added for web client: ${n}`))}catch(e){console.error("[POC-Streaming] Error handling ICE candidate:",e)}}async handleUserEvent(e){try{const{userEvent:n,targetTabId:t}=e;this.cdpManager&&"function"==typeof this.cdpManager.handleUserEvent?(await this.cdpManager.handleUserEvent(n,t),console.log(`[POC-Streaming] User event handled for tab: ${t}`)):console.warn("[POC-Streaming] CDP Manager not available for user event handling")}catch(e){console.error("[POC-Streaming] Error handling user event:",e)}}handleInterceptorConfigUpdate(e){try{const{webClientId:n,interceptorConfigs:t}=e;console.log(`[POC-Streaming] Updating interceptor config for client: ${n}`),this.clientInterceptorConfigs.set(n,t);const i=this.webClientGroups.get(n);if(i&&i.interceptorPipeline)for(const[e,n]of Object.entries(t))i.interceptorPipeline.updateInterceptorConfig(e,n);console.log(`[POC-Streaming] Interceptor config updated for client: ${n}`)}catch(e){console.error("[POC-Streaming] Error updating interceptor config:",e)}}sendMessage(e){this.websocket&&this.isConnected?this.websocket.send(JSON.stringify(e)):console.warn("[POC-Streaming] Cannot send message - not connected to signaling server")}async cleanup(){console.log("[POC-Streaming] Cleaning up control tab manager...");for(const[e,n]of this.webClientGroups)try{n.interceptorPipeline&&await n.interceptorPipeline.cleanup(),n.peerConnection.close()}catch(n){console.error(`[POC-Streaming] Error cleaning up web client ${e}:`,n)}for(const[e,n]of this.targetConnections)try{n.close()}catch(n){console.error(`[POC-Streaming] Error cleaning up target connection ${e}:`,n)}this.websocket&&this.websocket.close(),this.cdpManager&&"function"==typeof this.cdpManager.cleanup&&await this.cdpManager.cleanup(),console.log("[POC-Streaming] Control tab manager cleanup completed")}}return function(){if(console.log("[POC-Streaming] Control tab script initializing..."),window.controlTabInjected)return void console.log("[POC-Streaming] Control tab script already injected, skipping...");window.controlTabInjected=!0;const n=new e;window.controlTabManager=n,console.log("[POC-Streaming] Control tab script initialized successfully")}(),e}();
//# sourceMappingURL=control-tab-script.min.js.map
