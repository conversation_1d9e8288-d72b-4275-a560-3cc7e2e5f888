var TargetTabStreamer=function(){"use strict";class e{constructor(e,t,a){this.ws=null,this.isConnected=!1,this.captureStream=null,this.controlTabPeerConnection=null,this.signalingServerUrl=e,this.tabId=t,console.log("[POC-Streamer] Target tab streamer initializing..."),this.init("true"===a||!0===a)}async init(e){try{console.log("[POC-Streamer] Using tab ID:",this.tabId),await this.connectToSignalingServer(),e&&await this.handleStartStream(),console.log("[POC-Streamer] Target tab streamer initialized successfully")}catch(e){console.error("[POC-Streamer] Failed to initialize target streamer:",e)}}async connectToSignalingServer(){return new Promise((e,t)=>{try{this.ws=new WebSocket(this.signalingServerUrl),this.ws.onopen=()=>{console.log("[POC-Streamer] Connected to signaling server"),this.isConnected=!0,this.sendMessage({type:"register",role:"target",tabId:this.tabId}),e()},this.ws.onmessage=e=>{this.handleSignalingMessage(JSON.parse(e.data))},this.ws.onclose=()=>{console.log("[POC-Streamer] Disconnected from signaling server"),this.isConnected=!1},this.ws.onerror=e=>{console.error("[POC-Streamer] WebSocket error:",e),t(e)}}catch(e){t(e)}})}sendMessage(e){this.ws&&this.ws.readyState===WebSocket.OPEN&&this.ws.send(JSON.stringify(e))}async handleSignalingMessage(e){switch(console.log("[POC-Streamer] Received signaling message:",e.type),e.type){case"start-stream":await this.handleStartStream();break;case"stop-stream":await this.handleStopStream();break;case"offer":await this.handleOffer(e);break;case"ice-candidate":await this.handleIceCandidate(e);break;default:console.log("[POC-Streamer] Unknown message type:",e.type)}}async handleStartStream(){try{console.log("[POC-Streamer] Starting screen capture...");const e=await navigator.mediaDevices.getDisplayMedia({video:{mediaSource:"screen",width:{ideal:1920},height:{ideal:1080},frameRate:{ideal:30}},audio:!0,preferCurrentTab:!0});this.captureStream=e,await this.createControlTabConnection(),console.log("[POC-Streamer] Screen capture started successfully"),this.sendMessage({type:"stream-started",tabId:this.tabId})}catch(e){console.error("[POC-Streamer] Failed to start screen capture:",e),this.sendMessage({type:"stream-error",tabId:this.tabId,error:e.message})}}async handleStopStream(){try{console.log("[POC-Streamer] Stopping stream..."),this.captureStream&&(this.captureStream.getTracks().forEach(e=>e.stop()),this.captureStream=null),this.controlTabPeerConnection&&(this.controlTabPeerConnection.close(),this.controlTabPeerConnection=null),console.log("[POC-Streamer] Stream stopped"),this.sendMessage({type:"stream-stopped",tabId:this.tabId})}catch(e){console.error("[POC-Streamer] Error stopping stream:",e)}}async createControlTabConnection(){this.controlTabPeerConnection=new RTCPeerConnection({iceServers:[{urls:"stun:stun.l.google.com:19302"}]}),this.captureStream&&this.captureStream.getTracks().forEach(e=>{this.controlTabPeerConnection.addTrack(e,this.captureStream)}),this.controlTabPeerConnection.onicecandidate=e=>{e.candidate&&this.sendMessage({type:"ice-candidate",candidate:e.candidate,tabId:this.tabId,target:"control"})},this.controlTabPeerConnection.onconnectionstatechange=()=>{console.log("[POC-Streamer] Control tab connection state:",this.controlTabPeerConnection.connectionState)}}async handleOffer(e){try{this.controlTabPeerConnection||await this.createControlTabConnection(),await this.controlTabPeerConnection.setRemoteDescription(new RTCSessionDescription(e.offer));const t=await this.controlTabPeerConnection.createAnswer();await this.controlTabPeerConnection.setLocalDescription(t),this.sendMessage({type:"answer",answer:t,tabId:this.tabId,target:"control"})}catch(e){console.error("[POC-Streamer] Error handling offer:",e)}}async handleIceCandidate(e){try{this.controlTabPeerConnection&&e.candidate&&await this.controlTabPeerConnection.addIceCandidate(new RTCIceCandidate(e.candidate))}catch(e){console.error("[POC-Streamer] Error handling ICE candidate:",e)}}cleanup(){this.captureStream&&this.captureStream.getTracks().forEach(e=>e.stop()),this.controlTabPeerConnection&&this.controlTabPeerConnection.close(),this.ws&&this.ws.close()}}return(()=>{if(window.TargetTabStreamer)return void console.log("[POC-Streamer] Target streamer already initialized");window.TargetTabStreamer=e;const t=new URLSearchParams(window.location.search).get("autoInitialize")||"false";new e("${SIGNALING_SERVER_URL}","${TAB_ID}",t)})(),e}();
//# sourceMappingURL=target-tab-streamer.min.js.map
