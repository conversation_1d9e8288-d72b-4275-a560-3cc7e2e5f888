{"version": 3, "file": "change-detector-interceptor.min.js", "sources": ["../src/base-interceptor.ts", "../src/change-detector-interceptor.ts"], "sourcesContent": [null, null], "names": ["BaseInterceptor", "constructor", "name", "defaultConfig", "this", "isInitialized", "isEnabled", "processor", "generator", "transformStream", "originalTrack", "processedTrack", "Error", "type", "config", "debug", "enabled", "stats", "framesProcessed", "errorsEncountered", "averageProcessingTime", "lastProcessingTime", "totalProcessingTime", "log", "initialize", "videoTrack", "kind", "label", "MediaStreamTrackProcessor", "track", "MediaStreamTrackGenerator", "TransformStream", "transform", "processFrame", "bind", "readable", "pipeThrough", "pipeTo", "writable", "catch", "error", "frame", "controller", "startTime", "performance", "now", "enqueue", "processedFrame", "processVideoFrame", "close", "processingTime", "updateConfig", "newConfig", "oldConfig", "old", "new", "onConfigChange", "getConfig", "enable", "disable", "getStats", "resetStats", "cleanup", "args", "console", "getMetadata", "window", "ChangeDetectorInterceptor", "options", "super", "changeThreshold", "stabilityThreshold", "consecutiveStableFrames", "maxWaitDuration", "pixelSampling", "comparisonInterval", "canvas", "ctx", "isStreamPaused", "isMonitoring", "lastFrameData", "consecutiveStableCount", "maxWaitTimeout", "pendingFrames", "lastStableFrame", "controlTabManager", "triggeringWebClientId", "pauseStartTime", "setControlTabManager", "setTriggeringWebClient", "webClientId", "sendWebSocketNotification", "data", "message", "timestamp", "Date", "sendMessage", "width", "codedWidth", "height", "codedHeight", "initializeCanvas", "drawImage", "currentFrameData", "getImageData", "changePercentage", "compareFrames", "toFixed", "pauseStream", "handleStableFrame", "resetStability", "Uint8ClampedArray", "VideoFrame", "duration", "undefined", "document", "createElement", "getContext", "currentData", "previousData", "diffPixels", "totalPixels", "length", "sampling", "i", "rDiff", "Math", "abs", "gDiff", "bDiff", "clearTimeout", "setTimeout", "resumeStream", "reason", "pauseDuration", "resumeTime", "startMonitoring", "stopMonitoring"], "mappings": "sDAoBA,MAAeA,EAwBb,WAAAC,CAAYC,EAAcC,EAAmC,IAC3D,GAtBKC,KAAaC,eAAY,EACzBD,KAASE,WAAY,EAKpBF,KAASG,UAAqC,KAC9CH,KAASI,UAAqC,KAC9CJ,KAAeK,gBACrB,KAGML,KAAaM,cAA4B,KACzCN,KAAcO,eAA4B,KAS5CP,KAAKH,cAAgBD,EACvB,MAAM,IAAIY,MACR,4EAIJR,KAAKF,KAAOA,EACZE,KAAKS,KAAOT,KAAKH,YAAYC,KAG7BE,KAAKU,OAAS,CACZC,OAAO,EACPC,SAAS,KACNb,GAILC,KAAKa,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,GAGvBlB,KAAKmB,IAAI,GAAGnB,KAAKF,2BAClB,CAKD,gBAAMsB,CAAWC,GACf,IAAKA,GAAkC,UAApBA,EAAWC,KAC5B,MAAM,IAAId,MAAM,GAAGR,KAAKF,iDAG1BE,KAAKM,cAAgBe,EACrBrB,KAAKmB,IACH,gBAAgBnB,KAAKF,qCACrBuB,EAAWE,OAGb,IAuBE,OArBAvB,KAAKG,UAAY,IAAIqB,0BAA0B,CAAEC,MAAOJ,IACxDrB,KAAKI,UAAY,IAAIsB,0BAA0B,CAAEJ,KAAM,UAGvDtB,KAAKK,gBAAkB,IAAIsB,gBAAgB,CACzCC,UAAW5B,KAAK6B,aAAaC,KAAK9B,QAIpCA,KAAKG,UAAU4B,SACZC,YAAYhC,KAAKK,iBACjB4B,OAAOjC,KAAKI,UAAU8B,UACtBC,MAAOC,IACNpC,KAAKmB,IAAI,qBAAqBnB,KAAKF,QAASsC,GAC5CpC,KAAKa,MAAME,sBAGff,KAAKO,eAAiBP,KAAKI,UAAUqB,MACrCzB,KAAKC,eAAgB,EAErBD,KAAKmB,IAAI,GAAGnB,KAAKF,6CACVE,KAAKO,cACb,CAAC,MAAO6B,GAGP,MAFApC,KAAKmB,IAAI,sBAAsBnB,KAAKF,oBAAqBsC,GACzDpC,KAAKa,MAAME,oBACLqB,CACP,CACF,CAKD,kBAAMP,CACJQ,EACAC,GAEA,MAAMC,EAAYC,YAAYC,MAE9B,IAGE,GAFAzC,KAAKa,MAAMC,mBAENd,KAAKE,YAAcF,KAAKU,OAAOE,QAGlC,YADA0B,EAAWI,QAAQL,GAKrB,MAAMM,QAAuB3C,KAAK4C,kBAAkBP,GAGpDC,EAAWI,QAAQC,GAGfA,IAAmBN,GACrBA,EAAMQ,OAET,CAAC,MAAOT,GACPpC,KAAKmB,IAAI,6BAA6BnB,KAAKF,QAASsC,GACpDpC,KAAKa,MAAME,oBAGXuB,EAAWI,QAAQL,EACpB,CAAS,QAER,MAAMS,EAAiBN,YAAYC,MAAQF,EAC3CvC,KAAKa,MAAMI,mBAAqB6B,EAChC9C,KAAKa,MAAMK,qBAAuB4B,EAClC9C,KAAKa,MAAMG,sBACThB,KAAKa,MAAMK,oBAAsBlB,KAAKa,MAAMC,eAC/C,CACF,CAUD,YAAAiC,CAAaC,GACX,IAAKA,GAAkC,iBAAdA,EAEvB,YADAhD,KAAKmB,IAAI,OAAQ,kDAInB,MAAM8B,EAAY,IAAKjD,KAAKU,QAC5BV,KAAKU,OAAS,IAAKV,KAAKU,UAAWsC,GAEnChD,KAAKmB,IAAI,GAAGnB,KAAKF,8BAA+B,CAC9CoD,IAAKD,EACLE,IAAKnD,KAAKU,SAIRV,KAAKoD,gBACPpD,KAAKoD,eAAeH,EAAWjD,KAAKU,OAEvC,CAKD,SAAA2C,GACE,MAAO,IAAKrD,KAAKU,OAClB,CAKD,MAAA4C,GACEtD,KAAKE,WAAY,EACjBF,KAAKU,OAAOE,SAAU,EACtBZ,KAAKmB,IAAI,GAAGnB,KAAKF,2BAClB,CAKD,OAAAyD,GACEvD,KAAKE,WAAY,EACjBF,KAAKU,OAAOE,SAAU,EACtBZ,KAAKmB,IAAI,GAAGnB,KAAKF,4BAClB,CAKD,QAAA0D,GACE,MAAO,IAAKxD,KAAKa,MAClB,CAKD,UAAA4C,GACEzD,KAAKa,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,GAEvBlB,KAAKmB,IAAI,GAAGnB,KAAKF,mBAClB,CAKD,aAAM4D,GACJ,IACM1D,KAAKG,YACPH,KAAKG,UAAY,MAGfH,KAAKI,YACPJ,KAAKI,UAAY,MAGfJ,KAAKK,kBACPL,KAAKK,gBAAkB,MAGrBL,KAAKM,gBACPN,KAAKM,cAAgB,MAGnBN,KAAKO,iBACPP,KAAKO,eAAiB,MAGxBP,KAAKC,eAAgB,EACrBD,KAAKmB,IAAI,GAAGnB,KAAKF,8BAClB,CAAC,MAAOsC,GACPpC,KAAKmB,IAAI,qBAAqBnB,KAAKF,oBAAqBsC,EACzD,CACF,CAKD,GAAAjB,IAAOwC,GACD3D,KAAKU,OAAOC,OACdiD,QAAQzC,IAAI,IAAInB,KAAKF,uBAAwB6D,EAEhD,CAKD,WAAAE,GAQE,MAAO,CACL/D,KAAME,KAAKF,KACXW,KAAMT,KAAKS,KACXR,cAAeD,KAAKC,cACpBC,UAAWF,KAAKE,UAChBQ,OAAQV,KAAKqD,YACbxC,MAAOb,KAAKwD,WAEf,EAOmB,oBAAXM,SACRA,OAAelE,gBAAkBA,GCvRpC,MAAMmE,UAAkCnE,EAyBtC,WAAAC,CACEC,EAAe,kBACfkE,EAAgC,CAAA,GAiBhCC,MAAMnE,EAdgB,CACpBa,OAAO,EACPC,SAAS,EAETsD,gBAAiB,EACjBC,mBAAoB,EACpBC,wBAAyB,EACzBC,gBAAiB,IAEjBC,cAAe,EACfC,mBAAoB,OACjBP,IAvCChE,KAAMwE,OAA6B,KACnCxE,KAAGyE,IAAoC,KAGvCzE,KAAc0E,gBAAY,EAC1B1E,KAAY2E,cAAY,EACxB3E,KAAa4E,cAA6B,KAC1C5E,KAAsB6E,uBAAW,EACjC7E,KAAc8E,eAA0B,KAGxC9E,KAAa+E,cAAiB,GAC9B/E,KAAegF,gBAAsB,KAGrChF,KAAiBiF,kBAA6B,KAC9CjF,KAAqBkF,sBAAkB,KACvClF,KAAcmF,eAAkB,KA2BtCnF,KAAKmB,IAAI,wCAAyCnB,KAAKU,OACxD,CAKD,oBAAA0E,CAAqBH,GACnBjF,KAAKiF,kBAAoBA,EACzBjF,KAAKmB,IAAI,4DACV,CAKD,sBAAAkE,CAAuBC,GACrBtF,KAAKkF,sBAAwBI,EAC7BtF,KAAKmB,IAAI,8BAA8BmE,IACxC,CAKO,yBAAAC,CACN9E,EACA+E,EAA4B,IAE5B,IAAKxF,KAAKiF,oBAAsBjF,KAAKkF,sBAInC,YAHAlF,KAAKmB,IACH,qFAKJ,MAAMsE,EAAU,CACdhF,KAAMA,EACN6E,YAAatF,KAAKkF,sBAClBQ,UAAWC,KAAKlD,SACb+C,GAGLxF,KAAKmB,IACH,mCAAmCV,eAAkBT,KAAKkF,yBAE5DlF,KAAKiF,kBAAkBW,YAAYH,EACpC,CAKD,uBAAM7C,CAAkBP,GACtB,IAEE,IAAKrC,KAAK2E,aACR,OAAOtC,EAYT,GAPGrC,KAAKwE,QACNxE,KAAKwE,OAAOqB,QAAUxD,EAAMyD,YAC5B9F,KAAKwE,OAAOuB,SAAW1D,EAAM2D,aAE7BhG,KAAKiG,iBAAiB5D,EAAMyD,WAAYzD,EAAM2D,cAG3ChG,KAAKyE,IACR,MAAM,IAAIjE,MAAM,gCAIlBR,KAAKyE,IAAIyB,UAAU7D,EAAO,EAAG,GAC7B,MAMM8D,EANYnG,KAAKyE,IAAI2B,aACzB,EACA,EACApG,KAAKwE,OAAQqB,MACb7F,KAAKwE,OAAQuB,QAEoBP,KAGnC,GAAIxF,KAAK4E,cAAe,CACtB,MAAMyB,EAAmBrG,KAAKsG,cAC5BH,EACAnG,KAAK4E,eAQP,GALI5E,KAAKU,OAAOC,OACdX,KAAKmB,IAAI,qBAAqBkF,EAAiBE,QAAQ,cAKvDF,GAAoBrG,KAAKU,OAAOwD,iBAAmB,KAClDlE,KAAK0E,gBAUN,GARA1E,KAAKmB,IACH,mCAAmCkF,EAAiBE,QAClD,SACMvG,KAAKU,OAAOwD,0CAEtBlE,KAAKwG,cAGDxG,KAAKgF,gBAEP,OADA3C,EAAMQ,QACC7C,KAAKgF,qBAKdhF,KAAK0E,gBACL2B,IAAqBrG,KAAKU,OAAOyD,oBAAsB,GAEvDnE,KAAKyG,oBAILzG,KAAK0E,gBACL2B,GAAoBrG,KAAKU,OAAOyD,oBAAsB,IAEtDnE,KAAK0G,gBAER,CAiBD,OAdA1G,KAAK4E,cAAgB,IAAI+B,kBAAkBR,GAGtCnG,KAAK0E,iBACJ1E,KAAKgF,iBACPhF,KAAKgF,gBAAgBnC,QAEvB7C,KAAKgF,gBAAkB,IAAI4B,WAAWvE,EAAO,CAC3CqD,UAAWrD,EAAMqD,UACjBmB,SAAUxE,EAAMwE,eAAYC,KAKzB9G,KAAK0E,gBAAkB1E,KAAKgF,gBAC/BhF,KAAKgF,gBACL3C,CACL,CAAC,MAAOD,GAEP,OADApC,KAAKmB,IAAI,4BAA6BiB,GAC/BC,CACR,CACF,CAKO,gBAAA4D,CAAiBJ,EAAeE,GAMtC,GALA/F,KAAKwE,OAASuC,SAASC,cAAc,UACrChH,KAAKwE,OAAOqB,MAAQA,EACpB7F,KAAKwE,OAAOuB,OAASA,EACrB/F,KAAKyE,IAAMzE,KAAKwE,OAAOyC,WAAW,OAE7BjH,KAAKyE,IACR,MAAM,IAAIjE,MAAM,mCAGlBR,KAAKmB,IAAI,uBAAuB0E,KAASE,IAC1C,CAKO,aAAAO,CACNY,EACAC,GAEA,IAAIC,EAAa,EACjB,MAAMC,EAAcH,EAAYI,OAAS,EACnCC,EAAWvH,KAAKU,OAAO4D,eAAiB,EAE9C,IAAK,IAAIkD,EAAI,EAAGA,EAAIN,EAAYI,OAAQE,GAAK,EAAID,EAAU,CACzD,MAAME,EAAQC,KAAKC,IAAIT,EAAYM,GAAKL,EAAaK,IAC/CI,EAAQF,KAAKC,IAAIT,EAAYM,EAAI,GAAKL,EAAaK,EAAI,IACvDK,EAAQH,KAAKC,IAAIT,EAAYM,EAAI,GAAKL,EAAaK,EAAI,KAGzDC,EAAQ,IAAMG,EAAQ,IAAMC,EAAQ,KACtCT,GAEH,CAED,OAAQA,GAAcC,EAAcE,GAAa,GAClD,CAKO,WAAAf,GACNxG,KAAK0E,gBAAiB,EACtB1E,KAAKmF,eAAiBQ,KAAKlD,MAC3BzC,KAAK6E,uBAAyB,EAG1B7E,KAAK8E,gBACPgD,aAAa9H,KAAK8E,gBAGpB9E,KAAK8E,eAAiBiD,WAAW,KAC/B/H,KAAKmB,IAAI,kDACTnB,KAAKgI,gBACJhI,KAAKU,OAAO2D,iBAAmB,KAElCrE,KAAKuF,0BAA0B,gBAAiB,CAC9C0C,OAAQ,kBACR9C,eAAgBnF,KAAKmF,gBAExB,CAKO,iBAAAsB,GACNzG,KAAK6E,yBACL7E,KAAKmB,IACH,gBAAgBnB,KAAK6E,0BAA0B7E,KAAKU,OAAO0D,2BAI3DpE,KAAK6E,yBAA2B7E,KAAKU,OAAO0D,yBAA2B,IAEvEpE,KAAKgI,cAER,CAKO,cAAAtB,GACN1G,KAAK6E,uBAAyB,CAC/B,CAKO,YAAAmD,GACN,MAAME,EAAgBlI,KAAKmF,eACvBQ,KAAKlD,MAAQzC,KAAKmF,eAClB,EAEJnF,KAAK0E,gBAAiB,EACtB1E,KAAK6E,uBAAyB,EAC9B7E,KAAKmF,eAAiB,KAElBnF,KAAK8E,iBACPgD,aAAa9H,KAAK8E,gBAClB9E,KAAK8E,eAAiB,MAGxB9E,KAAKmB,IAAI,wBAAwB+G,OACjClI,KAAKuF,0BAA0B,iBAAkB,CAC/C2C,gBACAC,WAAYxC,KAAKlD,OAEpB,CAKD,eAAA2F,GACEpI,KAAK2E,cAAe,EACpB3E,KAAKmB,IAAI,sCACV,CAKD,cAAAkH,GACErI,KAAK2E,cAAe,EACpB3E,KAAK0E,gBAAiB,EACtB1E,KAAK6E,uBAAyB,EAE1B7E,KAAK8E,iBACPgD,aAAa9H,KAAK8E,gBAClB9E,KAAK8E,eAAiB,MAGxB9E,KAAKmB,IAAI,sCACV,CAKQ,aAAMuC,GACb1D,KAAKmB,IAAI,8CAETnB,KAAKqI,iBAGDrI,KAAKwE,SACPxE,KAAKwE,OAAOqB,MAAQ,EACpB7F,KAAKwE,OAAOuB,OAAS,EACrB/F,KAAKwE,OAAS,MAGhBxE,KAAKyE,IAAM,KACXzE,KAAK4E,cAAgB,KAGjB5E,KAAKgF,kBACPhF,KAAKgF,gBAAgBnC,QACrB7C,KAAKgF,gBAAkB,MAIzB,IAAK,MAAM3C,KAASrC,KAAK+E,cACvB1C,EAAMQ,QAER7C,KAAK+E,cAAgB,SAGfd,MAAMP,UAEZ1D,KAAKmB,IAAI,+CACV,QAOmB,oBAAX2C,SACRA,OAAeC,0BAA4BA"}