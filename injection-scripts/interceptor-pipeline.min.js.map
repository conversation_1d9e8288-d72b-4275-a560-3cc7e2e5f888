{"version": 3, "file": "interceptor-pipeline.min.js", "sources": ["../src/interceptor-pipeline.ts"], "sourcesContent": [null], "names": ["InterceptorPipeline", "constructor", "interceptors", "this", "isInitialized", "isEnabled", "originalTrack", "processedTrack", "processor", "generator", "transformStream", "stats", "framesProcessed", "errorsEncountered", "averageProcessingTime", "lastProcessingTime", "totalProcessingTime", "interceptorStats", "Map", "log", "length", "initialize", "videoTrack", "kind", "Error", "label", "MediaStreamTrackProcessor", "track", "MediaStreamTrackGenerator", "TransformStream", "transform", "processFrame", "bind", "readable", "pipeThrough", "pipeTo", "writable", "catch", "error", "frame", "controller", "startTime", "performance", "now", "enqueue", "currentFrame", "framesToCleanup", "i", "interceptor", "config", "enabled", "console", "name", "interceptorStartTime", "processedFrame", "processVideoFrame", "interceptorTime", "updateInterceptorStats", "push", "frameToCleanup", "close", "cleanupError", "processingTime", "interceptorName", "has", "set", "get", "addInterceptor", "index", "splice", "removeInterceptor", "findIndex", "removed", "getInterceptor", "identifier", "getInterceptorNames", "map", "enable", "disable", "updateInterceptorConfig", "updateConfig", "getStats", "interceptorCount", "enabledInterceptorCount", "filter", "resetStats", "updateInterceptorConfigs", "interceptorConfigs", "newConfig", "cleanup", "args", "window"], "mappings": "gDAgCA,MAAMA,EAgBJ,WAAAC,CAAYC,EAA2C,IAdhDC,KAAaC,eAAY,EACzBD,KAASE,WAAY,EAIpBF,KAAaG,cAA4B,KACzCH,KAAcI,eAA4B,KAG1CJ,KAASK,UAAqC,KAC9CL,KAASM,UAAqC,KAC9CN,KAAeO,gBACrB,KAGAP,KAAKD,aAAe,IAAIA,GAGxBC,KAAKQ,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,EACrBC,iBAAkB,IAAIC,KAGxBf,KAAKgB,IACH,mCACAhB,KAAKD,aAAakB,OAClB,eAEH,CAKD,gBAAMC,CAAWC,GACf,IAAKA,GAAkC,UAApBA,EAAWC,KAC5B,MAAM,IAAIC,MAAM,oDAGlBrB,KAAKG,cAAgBgB,EACrBnB,KAAKgB,IAAI,0CAA2CG,EAAWG,OAE/D,IAuBE,OArBAtB,KAAKK,UAAY,IAAIkB,0BAA0B,CAAEC,MAAOL,IACxDnB,KAAKM,UAAY,IAAImB,0BAA0B,CAAEL,KAAM,UAGvDpB,KAAKO,gBAAkB,IAAImB,gBAAgB,CACzCC,UAAW3B,KAAK4B,aAAaC,KAAK7B,QAIpCA,KAAKK,UAAUyB,SACZC,YAAY/B,KAAKO,iBACjByB,OAAOhC,KAAKM,UAAU2B,UACtBC,MAAOC,IACNnC,KAAKgB,IAAI,kBAAmBmB,GAC5BnC,KAAKQ,MAAME,sBAGfV,KAAKI,eAAiBJ,KAAKM,UAAUkB,MACrCxB,KAAKC,eAAgB,EAErBD,KAAKgB,IAAI,qCACFhB,KAAKI,cACb,CAAC,MAAO+B,GAGP,MAFAnC,KAAKgB,IAAI,+BAAgCmB,GACzCnC,KAAKQ,MAAME,oBACLyB,CACP,CACF,CAKO,kBAAMP,CACZQ,EACAC,GAEA,MAAMC,EAAYC,YAAYC,MAE9B,IAGE,GAFAxC,KAAKQ,MAAMC,mBAENT,KAAKE,WAA0C,IAA7BF,KAAKD,aAAakB,OAGvC,YADAoB,EAAWI,QAAQL,GAIrB,GAAa,MAATA,EAEF,OAGF,IAAIM,EAAeN,EACnB,MAAMO,EAAgC,GAGtC,IAAK,IAAIC,EAAI,EAAGA,EAAI5C,KAAKD,aAAakB,OAAQ2B,IAAK,CACjD,MAAMC,EAAc7C,KAAKD,aAAa6C,GACtC,GAAKC,GAAgBA,EAAYC,OAAOC,QAAxC,CAIAC,QAAQhC,IAAI,wCAAyC6B,EAAYI,MACjE,IACE,MAAMC,EAAuBX,YAAYC,MAGnCW,QAAuBN,EAAYO,kBACvCV,GAIIW,EAAkBd,YAAYC,MAAQU,EAC5ClD,KAAKsD,uBAAuBT,EAAYI,KAAMI,GAG1CF,IAAmBT,GAAgBA,IAAiBN,GACtDO,EAAgBY,KAAKb,GAGvBA,EAAeS,CAChB,CAAC,MAAOhB,GACPnC,KAAKgB,IAAI,wBAAwB6B,EAAYI,QAASd,GACtDnC,KAAKQ,MAAME,mBAIZ,CA3BA,CA4BF,CAGD2B,EAAWI,QAAQC,GAGnB,IAAK,MAAMc,KAAkBb,EAC3B,IACEa,EAAeC,OAChB,CAAC,MAAOC,GACP1D,KAAKgB,IAAI,wCAAyC0C,EACnD,CAIChB,IAAiBN,GACnBA,EAAMqB,OAET,CAAC,MAAOtB,GACPnC,KAAKgB,IAAI,2CAA4CmB,GACrDnC,KAAKQ,MAAME,oBAGX2B,EAAWI,QAAQL,EACpB,CAAS,QAER,MAAMuB,EAAiBpB,YAAYC,MAAQF,EAC3CtC,KAAKQ,MAAMI,mBAAqB+C,EAChC3D,KAAKQ,MAAMK,qBAAuB8C,EAClC3D,KAAKQ,MAAMG,sBACTX,KAAKQ,MAAMK,oBAAsBb,KAAKQ,MAAMC,eAC/C,CACF,CAKO,sBAAA6C,CACNM,EACAD,GAEK3D,KAAKQ,MAAMM,iBAAiB+C,IAAID,IACnC5D,KAAKQ,MAAMM,iBAAiBgD,IAAIF,EAAiB,CAC/CnD,gBAAiB,EACjBI,oBAAqB,EACrBF,sBAAuB,IAI3B,MAAMH,EAAQR,KAAKQ,MAAMM,iBAAiBiD,IAAIH,GAC9CpD,EAAMC,kBACND,EAAMK,qBAAuB8C,EAC7BnD,EAAMG,sBACJH,EAAMK,oBAAsBL,EAAMC,eACrC,CAKD,cAAAuD,CACEnB,EACAoB,GAAgB,GAEhB,IAAKpB,GAAwD,mBAAlCA,EAAYO,kBACrC,MAAM,IAAI/B,MACR,gEAIA4C,EAAQ,GAAKA,GAASjE,KAAKD,aAAakB,QAC1CjB,KAAKD,aAAawD,KAAKV,GACvB7C,KAAKgB,IAAI,qBAAqB6B,EAAYI,6BAE1CjD,KAAKD,aAAamE,OAAOD,EAAO,EAAGpB,GACnC7C,KAAKgB,IAAI,qBAAqB6B,EAAYI,iBAAiBgB,KAE9D,CAKD,iBAAAE,CAAkBlB,GAChB,MAAMgB,EAAQjE,KAAKD,aAAaqE,UAAWxB,GAAMA,EAAEK,OAASA,GAE5D,GAAIgB,GAAS,GAAKA,EAAQjE,KAAKD,aAAakB,OAAQ,CAClD,MAAMoD,EAAUrE,KAAKD,aAAamE,OAAOD,EAAO,GAAG,GAEnD,OADAjE,KAAKgB,IAAI,uBAAuBqD,EAAQpB,uBACjC,CACR,CAGD,OADAjD,KAAKgB,IAAI,0BAA0BiC,MAC5B,CACR,CAKD,cAAAqB,CAAeC,GACb,IAAIN,GAAS,EAQb,MAN0B,iBAAfM,EACTN,EAAQjE,KAAKD,aAAaqE,UAAWxB,GAAMA,EAAEK,OAASsB,GACvB,iBAAfA,IAChBN,EAAQM,GAGNN,GAAS,GAAKA,EAAQjE,KAAKD,aAAakB,OACnCjB,KAAKD,aAAakE,GAGpB,IACR,CAKD,mBAAAO,GACE,OAAOxE,KAAKD,aAAa0E,IAAK7B,GAAMA,EAAEK,KACvC,CAKD,MAAAyB,GACE1E,KAAKE,WAAY,EACjBF,KAAKgB,IAAI,mBACV,CAKD,OAAA2D,GACE3E,KAAKE,WAAY,EACjBF,KAAKgB,IAAI,oBACV,CAKD,uBAAA4D,CACE3B,EACAH,GAEA,MAAMD,EAAc7C,KAAKsE,eAAerB,GACxC,QAAIJ,IACFA,EAAYgC,aAAa/B,IAClB,EAGV,CAKD,QAAAgC,GAIE,MAAO,IACF9E,KAAKQ,MACRM,iBAAkB,IAAIC,IAAIf,KAAKQ,MAAMM,kBACrCiE,iBAAkB/E,KAAKD,aAAakB,OACpC+D,wBAAyBhF,KAAKD,aAAakF,OAAQrC,GAAMA,EAAE1C,WACxDe,OAEN,CAKD,UAAAiE,GACElF,KAAKQ,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,EACrBC,iBAAkB,IAAIC,KAExBf,KAAKgB,IAAI,uBACV,CAKD,wBAAAmE,CACEC,GAEA,GAAKA,GAAoD,iBAAvBA,EAAlC,CAKApF,KAAKgB,IACH,OACA,uCACAoE,GAGF,IAAK,MAAMvC,KAAe7C,KAAKD,aAAc,CAC3C,MAAMsF,EAAYD,EAAmBvC,EAAYI,MACjD,GAAIoC,EACF,IACExC,EAAYgC,aAAaQ,GACzBrF,KAAKgB,IACH,OACA,mCAAmC6B,EAAYI,OAElD,CAAC,MAAOd,GACPnC,KAAKgB,IACH,QACA,2CAA2C6B,EAAYI,QACvDd,EAEH,CAEJ,CAzBA,MAFCnC,KAAKgB,IAAI,OAAQ,8CA4BpB,CAKD,aAAMsE,GACJ,IACEtF,KAAKgB,IAAI,2BAGT,IAAK,MAAM6B,KAAe7C,KAAKD,aAC7B,IACM8C,EAAYyC,eACRzC,EAAYyC,SAErB,CAAC,MAAOnD,GACPnC,KAAKgB,IAAI,iCAAiC6B,EAAYI,QAASd,EAChE,CAIHnC,KAAKD,aAAakB,OAAS,EAGvBjB,KAAKK,YACPL,KAAKK,UAAY,MAGfL,KAAKM,YACPN,KAAKM,UAAY,MAGfN,KAAKO,kBACPP,KAAKO,gBAAkB,MAGrBP,KAAKG,gBACPH,KAAKG,cAAgB,MAGnBH,KAAKI,iBACPJ,KAAKI,eAAiB,MAGxBJ,KAAKC,eAAgB,EACrBD,KAAKgB,IAAI,4BACV,CAAC,MAAOmB,GACPnC,KAAKgB,IAAI,iCAAkCmB,EAC5C,CACF,CAKO,GAAAnB,IAAOuE,GACbvC,QAAQhC,IAAI,2BAA4BuE,EACzC,QAOmB,oBAAXC,SACRA,OAAe3F,oBAAsBA"}