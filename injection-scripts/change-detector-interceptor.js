var ChangeDetectorInterceptor = (function () {
    'use strict';

    /**
     * Base Interceptor Interface
     *
     * Defines the standard interface that all video frame interceptors must implement.
     * Provides common functionality for configuration management, lifecycle, and processing.
     *
     * Features:
     * - Standardized interceptor interface
     * - Built-in configuration management
     * - Lifecycle management (initialize, process, cleanup)
     * - Error handling and fallback mechanisms
     * - Performance monitoring capabilities
     */
    class BaseInterceptor {
        constructor(name, defaultConfig = {}) {
            this.isInitialized = false;
            this.isEnabled = true;
            // Transform stream components
            this.processor = null;
            this.generator = null;
            this.transformStream = null;
            // Track references
            this.originalTrack = null;
            this.processedTrack = null;
            if (this.constructor === BaseInterceptor) {
                throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");
            }
            this.name = name;
            this.type = this.constructor.name;
            // Configuration management - stored internally
            this.config = {
                debug: false,
                enabled: true,
                ...defaultConfig,
            };
            // Performance tracking
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} interceptor created`);
        }
        /**
         * Initialize the interceptor with a video track
         */
        async initialize(videoTrack) {
            if (!videoTrack || videoTrack.kind !== "video") {
                throw new Error(`${this.name} interceptor requires a valid video track`);
            }
            this.originalTrack = videoTrack;
            this.log(`Initializing ${this.name} interceptor with video track:`, videoTrack.label);
            try {
                // Create processor and generator
                this.processor = new MediaStreamTrackProcessor({ track: videoTrack });
                this.generator = new MediaStreamTrackGenerator({ kind: "video" });
                // Create transform stream with bound processing function
                this.transformStream = new TransformStream({
                    transform: this.processFrame.bind(this),
                });
                // Connect the pipeline
                this.processor.readable
                    .pipeThrough(this.transformStream)
                    .pipeTo(this.generator.writable)
                    .catch((error) => {
                    this.log(`Pipeline error in ${this.name}:`, error);
                    this.stats.errorsEncountered++;
                });
                this.processedTrack = this.generator.track;
                this.isInitialized = true;
                this.log(`${this.name} interceptor initialized successfully`);
                return this.processedTrack;
            }
            catch (error) {
                this.log(`Error initializing ${this.name} interceptor:`, error);
                this.stats.errorsEncountered++;
                throw error;
            }
        }
        /**
         * Process a single video frame - handles common logic and delegates to subclass
         */
        async processFrame(frame, controller) {
            const startTime = performance.now();
            try {
                this.stats.framesProcessed++;
                if (!this.isEnabled || !this.config.enabled) {
                    // Pass through original frame if disabled
                    controller.enqueue(frame);
                    return;
                }
                // Call the specific interceptor's processing logic
                const processedFrame = await this.processVideoFrame(frame);
                // Enqueue the processed frame
                controller.enqueue(processedFrame);
                // Clean up original frame if a new one was created
                if (processedFrame !== frame) {
                    frame.close();
                }
            }
            catch (error) {
                this.log(`Error processing frame in ${this.name}:`, error);
                this.stats.errorsEncountered++;
                // Fallback: pass through original frame
                controller.enqueue(frame);
            }
            finally {
                // Update performance stats
                const processingTime = performance.now() - startTime;
                this.stats.lastProcessingTime = processingTime;
                this.stats.totalProcessingTime += processingTime;
                this.stats.averageProcessingTime =
                    this.stats.totalProcessingTime / this.stats.framesProcessed;
            }
        }
        /**
         * Update interceptor configuration
         */
        updateConfig(newConfig) {
            if (!newConfig || typeof newConfig !== "object") {
                this.log("warn", "Invalid configuration provided to updateConfig");
                return;
            }
            const oldConfig = { ...this.config };
            this.config = { ...this.config, ...newConfig };
            this.log(`${this.name} configuration updated:`, {
                old: oldConfig,
                new: this.config,
            });
            // Call configuration change handler if implemented
            if (this.onConfigChange) {
                this.onConfigChange(oldConfig, this.config);
            }
        }
        /**
         * Get current configuration
         */
        getConfig() {
            return { ...this.config };
        }
        /**
         * Enable the interceptor
         */
        enable() {
            this.isEnabled = true;
            this.config.enabled = true;
            this.log(`${this.name} interceptor enabled`);
        }
        /**
         * Disable the interceptor
         */
        disable() {
            this.isEnabled = false;
            this.config.enabled = false;
            this.log(`${this.name} interceptor disabled`);
        }
        /**
         * Get performance statistics
         */
        getStats() {
            return { ...this.stats };
        }
        /**
         * Reset performance statistics
         */
        resetStats() {
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} stats reset`);
        }
        /**
         * Cleanup resources
         */
        async cleanup() {
            try {
                if (this.processor) {
                    this.processor = null;
                }
                if (this.generator) {
                    this.generator = null;
                }
                if (this.transformStream) {
                    this.transformStream = null;
                }
                if (this.originalTrack) {
                    this.originalTrack = null;
                }
                if (this.processedTrack) {
                    this.processedTrack = null;
                }
                this.isInitialized = false;
                this.log(`${this.name} interceptor cleaned up`);
            }
            catch (error) {
                this.log(`Error cleaning up ${this.name} interceptor:`, error);
            }
        }
        /**
         * Logging utility
         */
        log(...args) {
            if (this.config.debug) {
                console.log(`[${this.name}-Interceptor]`, ...args);
            }
        }
        /**
         * Get interceptor metadata
         */
        getMetadata() {
            return {
                name: this.name,
                type: this.type,
                isInitialized: this.isInitialized,
                isEnabled: this.isEnabled,
                config: this.getConfig(),
                stats: this.getStats(),
            };
        }
    }
    // Make available globally for injection scripts
    if (typeof window !== "undefined") {
        window.BaseInterceptor = BaseInterceptor;
    }

    /**
     * Change Detector Interceptor
     *
     * A video frame interceptor that detects click events and
     * pauses the video stream when screen changes are detected, resuming only
     * after the screen has stabilized.
     *
     * Features:
     * - Click event detection
     * - Screen stability detection using pixelmatch
     * - Stream pause/resume functionality
     * - Configurable thresholds and stability duration
     * - Integration with established interceptor architecture
     */
    class ChangeDetectorInterceptor extends BaseInterceptor {
        constructor(name = "change-detector", options = {}) {
            // Default configuration
            const defaultConfig = {
                debug: false,
                enabled: true,
                // Change detector settings
                changeThreshold: 5, // Percentage change to trigger pause
                stabilityThreshold: 1, // Percentage change to consider stable
                consecutiveStableFrames: 3, // Number of consecutive stable frames required
                maxWaitDuration: 5000, // Maximum time to wait for stability
                // Performance settings
                pixelSampling: 2, // Pixel sampling rate for performance
                comparisonInterval: 100, // Interval for frame comparison
                ...options,
            };
            super(name, defaultConfig);
            // Canvas for frame processing and comparison
            this.canvas = null;
            this.ctx = null;
            // Change detector state
            this.isStreamPaused = false;
            this.isMonitoring = false;
            this.lastFrameData = null;
            this.consecutiveStableCount = 0;
            this.maxWaitTimeout = null;
            // Frame tracking
            this.pendingFrames = [];
            this.lastStableFrame = null;
            // WebSocket messaging
            this.controlTabManager = null;
            this.triggeringWebClientId = null;
            this.pauseStartTime = null;
            this.log("ChangeDetectorInterceptor initialized", this.config);
        }
        /**
         * Set the control tab manager reference for WebSocket messaging
         */
        setControlTabManager(controlTabManager) {
            this.controlTabManager = controlTabManager;
            this.log("Control tab manager reference set for WebSocket messaging");
        }
        /**
         * Set the web client ID that triggered the change detection
         */
        setTriggeringWebClient(webClientId) {
            this.triggeringWebClientId = webClientId;
            this.log(`Triggering web client set: ${webClientId}`);
        }
        /**
         * Send WebSocket notification to the triggering web client
         */
        sendWebSocketNotification(type, data = {}) {
            if (!this.controlTabManager || !this.triggeringWebClientId) {
                this.log("Cannot send WebSocket notification - missing control tab manager or web client ID");
                return;
            }
            const message = {
                type: type,
                webClientId: this.triggeringWebClientId,
                timestamp: Date.now(),
                ...data,
            };
            this.log(`Sending WebSocket notification: ${type} to client ${this.triggeringWebClientId}`);
            this.controlTabManager.sendMessage(message);
        }
        /**
         * Process a video frame - implements BaseInterceptor interface
         */
        async processVideoFrame(frame) {
            try {
                // If not monitoring, pass through
                if (!this.isMonitoring) {
                    return frame;
                }
                // Initialize canvas if needed
                if (!this.canvas ||
                    this.canvas.width !== frame.codedWidth ||
                    this.canvas.height !== frame.codedHeight) {
                    this.initializeCanvas(frame.codedWidth, frame.codedHeight);
                }
                if (!this.ctx) {
                    throw new Error("Canvas context not available");
                }
                // Draw frame to canvas and get image data
                this.ctx.drawImage(frame, 0, 0);
                const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
                const currentFrameData = imageData.data;
                // Compare with previous frame if available
                if (this.lastFrameData) {
                    const changePercentage = this.compareFrames(currentFrameData, this.lastFrameData);
                    if (this.config.debug) {
                        this.log(`Frame comparison: ${changePercentage.toFixed(2)}% change`);
                    }
                    // Check if significant change detected - immediately pause and return held frame
                    if (changePercentage > (this.config.changeThreshold || 5) &&
                        !this.isStreamPaused) {
                        this.log(`📊 Significant change detected: ${changePercentage.toFixed(2)}% > ${this.config.changeThreshold}% - immediately pausing`);
                        this.pauseStream();
                        // Return the last stable frame instead of the changed frame
                        if (this.lastStableFrame) {
                            frame.close(); // Close the changed frame
                            return this.lastStableFrame;
                        }
                    }
                    // Check if screen has stabilized
                    else if (this.isStreamPaused &&
                        changePercentage <= (this.config.stabilityThreshold || 1)) {
                        this.handleStableFrame();
                    }
                    // Reset stability if change detected while monitoring stability
                    else if (this.isStreamPaused &&
                        changePercentage > (this.config.stabilityThreshold || 1)) {
                        this.resetStability();
                    }
                }
                // Store current frame data for next comparison
                this.lastFrameData = new Uint8ClampedArray(currentFrameData);
                // If not paused, store this as the last stable frame
                if (!this.isStreamPaused) {
                    if (this.lastStableFrame) {
                        this.lastStableFrame.close();
                    }
                    this.lastStableFrame = new VideoFrame(frame, {
                        timestamp: frame.timestamp,
                        duration: frame.duration ?? undefined,
                    });
                }
                // Return the frame (or held frame if paused)
                return this.isStreamPaused && this.lastStableFrame
                    ? this.lastStableFrame
                    : frame;
            }
            catch (error) {
                this.log("Error in change detector:", error);
                return frame;
            }
        }
        /**
         * Initialize canvas for frame processing
         */
        initializeCanvas(width, height) {
            this.canvas = document.createElement("canvas");
            this.canvas.width = width;
            this.canvas.height = height;
            this.ctx = this.canvas.getContext("2d");
            if (!this.ctx) {
                throw new Error("Failed to get 2D canvas context");
            }
            this.log(`Canvas initialized: ${width}x${height}`);
        }
        /**
         * Compare two frames and return percentage difference
         */
        compareFrames(currentData, previousData) {
            let diffPixels = 0;
            const totalPixels = currentData.length / 4; // RGBA = 4 bytes per pixel
            const sampling = this.config.pixelSampling || 2;
            for (let i = 0; i < currentData.length; i += 4 * sampling) {
                const rDiff = Math.abs(currentData[i] - previousData[i]);
                const gDiff = Math.abs(currentData[i + 1] - previousData[i + 1]);
                const bDiff = Math.abs(currentData[i + 2] - previousData[i + 2]);
                // Consider pixel changed if any channel differs by more than threshold
                if (rDiff > 30 || gDiff > 30 || bDiff > 30) {
                    diffPixels++;
                }
            }
            return (diffPixels / (totalPixels / sampling)) * 100;
        }
        /**
         * Pause the stream
         */
        pauseStream() {
            this.isStreamPaused = true;
            this.pauseStartTime = Date.now();
            this.consecutiveStableCount = 0;
            // Set maximum wait timeout
            if (this.maxWaitTimeout) {
                clearTimeout(this.maxWaitTimeout);
            }
            this.maxWaitTimeout = setTimeout(() => {
                this.log("Maximum wait duration reached, resuming stream");
                this.resumeStream();
            }, this.config.maxWaitDuration || 5000);
            this.sendWebSocketNotification("stream-paused", {
                reason: "change-detected",
                pauseStartTime: this.pauseStartTime,
            });
        }
        /**
         * Handle stable frame detection
         */
        handleStableFrame() {
            this.consecutiveStableCount++;
            this.log(`Stable frame ${this.consecutiveStableCount}/${this.config.consecutiveStableFrames}`);
            if (this.consecutiveStableCount >= (this.config.consecutiveStableFrames || 3)) {
                this.resumeStream();
            }
        }
        /**
         * Reset stability counter
         */
        resetStability() {
            this.consecutiveStableCount = 0;
        }
        /**
         * Resume the stream
         */
        resumeStream() {
            const pauseDuration = this.pauseStartTime
                ? Date.now() - this.pauseStartTime
                : 0;
            this.isStreamPaused = false;
            this.consecutiveStableCount = 0;
            this.pauseStartTime = null;
            if (this.maxWaitTimeout) {
                clearTimeout(this.maxWaitTimeout);
                this.maxWaitTimeout = null;
            }
            this.log(`Stream resumed after ${pauseDuration}ms`);
            this.sendWebSocketNotification("stream-resumed", {
                pauseDuration,
                resumeTime: Date.now(),
            });
        }
        /**
         * Start monitoring for changes
         */
        startMonitoring() {
            this.isMonitoring = true;
            this.log("Change detection monitoring started");
        }
        /**
         * Stop monitoring for changes
         */
        stopMonitoring() {
            this.isMonitoring = false;
            this.isStreamPaused = false;
            this.consecutiveStableCount = 0;
            if (this.maxWaitTimeout) {
                clearTimeout(this.maxWaitTimeout);
                this.maxWaitTimeout = null;
            }
            this.log("Change detection monitoring stopped");
        }
        /**
         * Override cleanup to handle change detector resources
         */
        async cleanup() {
            this.log("Cleaning up change detector interceptor...");
            this.stopMonitoring();
            // Clean up canvas resources
            if (this.canvas) {
                this.canvas.width = 0;
                this.canvas.height = 0;
                this.canvas = null;
            }
            this.ctx = null;
            this.lastFrameData = null;
            // Clean up frame references
            if (this.lastStableFrame) {
                this.lastStableFrame.close();
                this.lastStableFrame = null;
            }
            // Clean up pending frames
            for (const frame of this.pendingFrames) {
                frame.close();
            }
            this.pendingFrames = [];
            // Call parent cleanup
            await super.cleanup();
            this.log("Change detector interceptor cleanup complete");
        }
    }
    // Make available globally for injection scripts
    if (typeof window !== "undefined") {
        window.ChangeDetectorInterceptor = ChangeDetectorInterceptor;
    }

    return ChangeDetectorInterceptor;

})();
//# sourceMappingURL=change-detector-interceptor.js.map
