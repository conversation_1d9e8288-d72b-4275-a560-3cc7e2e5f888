{"version": 3, "file": "target-tab-streamer.js", "sources": ["../src/target-tab-streamer.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;IAAA;;;;;IAKG;IAQH,MAAM,iBAAiB,CAAA;IAQrB,IAAA,WAAA,CACE,kBAA0B,EAC1B,KAAa,EACb,cAAgC,EAAA;YAT1B,IAAE,CAAA,EAAA,GAAqB,IAAI,CAAC;YAE5B,IAAW,CAAA,WAAA,GAAY,KAAK,CAAC;YAC7B,IAAa,CAAA,aAAA,GAAuB,IAAI,CAAC;YACzC,IAAwB,CAAA,wBAAA,GAA6B,IAAI,CAAC;IAOhE,QAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC7C,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAEnB,QAAA,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;YAClE,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,MAAM,IAAI,cAAc,KAAK,IAAI,CAAC,CAAC;SACjE;QAED,MAAM,IAAI,CAAC,cAAuB,EAAA;IAChC,QAAA,IAAI;;gBAEF,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;;IAGxD,YAAA,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAEtC,IAAI,cAAc,EAAE;IAClB,gBAAA,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;iBAChC;IAED,YAAA,OAAO,CAAC,GAAG,CACT,6DAA6D,CAC9D,CAAC;aACH;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CACX,sDAAsD,EACtD,KAAK,CACN,CAAC;aACH;SACF;IAED,IAAA,MAAM,wBAAwB,GAAA;YAC5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;IACrC,YAAA,IAAI;oBACF,IAAI,CAAC,EAAE,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAEjD,gBAAA,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG,MAAK;IACpB,oBAAA,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAC5D,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;;wBAGxB,IAAI,CAAC,WAAW,CAAC;IACf,wBAAA,IAAI,EAAE,UAAU;IAChB,wBAAA,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,IAAI,CAAC,KAAK;IAClB,qBAAA,CAAC,CAAC;IAEH,oBAAA,OAAO,EAAE,CAAC;IACZ,iBAAC,CAAC;oBAEF,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC,KAAK,KAAI;IAC5B,oBAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,iBAAC,CAAC;IAEF,gBAAA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,MAAK;IACrB,oBAAA,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;IACjE,oBAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,iBAAC,CAAC;oBAEF,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,KAAK,KAAI;IAC1B,oBAAA,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;wBACxD,MAAM,CAAC,KAAK,CAAC,CAAC;IAChB,iBAAC,CAAC;iBACH;gBAAC,OAAO,KAAK,EAAE;oBACd,MAAM,CAAC,KAAK,CAAC,CAAC;iBACf;IACH,SAAC,CAAC,CAAC;SACJ;IAEO,IAAA,WAAW,CAAC,OAAY,EAAA;IAC9B,QAAA,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE;IACpD,YAAA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;aACvC;SACF;QAEO,MAAM,sBAAsB,CAAC,OAAY,EAAA;YAC/C,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAExE,QAAA,QAAQ,OAAO,CAAC,IAAI;IAClB,YAAA,KAAK,cAAc;IACjB,gBAAA,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC/B,MAAM;IACR,YAAA,KAAK,aAAa;IAChB,gBAAA,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC9B,MAAM;IACR,YAAA,KAAK,OAAO;IACV,gBAAA,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAChC,MAAM;IACR,YAAA,KAAK,eAAe;IAClB,gBAAA,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;oBACvC,MAAM;IACR,YAAA;oBACE,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;aACrE;SACF;IAEO,IAAA,MAAM,iBAAiB,GAAA;IAC7B,QAAA,IAAI;IACF,YAAA,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;;gBAGzD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,eAAe,CAAC;IAC1D,gBAAA,KAAK,EAAE;IACL,oBAAA,WAAW,EAAE,QAAQ;IACrB,oBAAA,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;IACtB,oBAAA,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;IACvB,oBAAA,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;IAClB,iBAAA;IACR,gBAAA,KAAK,EAAE,IAAI;IACX,gBAAA,gBAAgB,EAAE,IAAI;IAChB,aAAA,CAAC,CAAC;IAEV,YAAA,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;;IAG5B,YAAA,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;IAExC,YAAA,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;;gBAGlE,IAAI,CAAC,WAAW,CAAC;IACf,gBAAA,IAAI,EAAE,gBAAgB;oBACtB,KAAK,EAAE,IAAI,CAAC,KAAK;IAClB,aAAA,CAAC,CAAC;aACJ;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;gBACvE,IAAI,CAAC,WAAW,CAAC;IACf,gBAAA,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,KAAK,CAAC,OAAO;IACrB,aAAA,CAAC,CAAC;aACJ;SACF;IAEO,IAAA,MAAM,gBAAgB,GAAA;IAC5B,QAAA,IAAI;IACF,YAAA,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IAEjD,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;IACtB,gBAAA,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAChE,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;iBAC3B;IAED,YAAA,IAAI,IAAI,CAAC,wBAAwB,EAAE;IACjC,gBAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;IACtC,gBAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;iBACtC;IAED,YAAA,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAE7C,IAAI,CAAC,WAAW,CAAC;IACf,gBAAA,IAAI,EAAE,gBAAgB;oBACtB,KAAK,EAAE,IAAI,CAAC,KAAK;IAClB,aAAA,CAAC,CAAC;aACJ;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;aAC/D;SACF;IAEO,IAAA,MAAM,0BAA0B,GAAA;IACtC,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,iBAAiB,CAAC;IACpD,YAAA,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,8BAA8B,EAAE,CAAC;IACvD,SAAA,CAAC,CAAC;;IAGH,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;oBAC/C,IAAI,CAAC,wBAAyB,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,aAAc,CAAC,CAAC;IACtE,aAAC,CAAC,CAAC;aACJ;;YAGD,IAAI,CAAC,wBAAwB,CAAC,cAAc,GAAG,CAAC,KAAK,KAAI;IACvD,YAAA,IAAI,KAAK,CAAC,SAAS,EAAE;oBACnB,IAAI,CAAC,WAAW,CAAC;IACf,oBAAA,IAAI,EAAE,eAAe;wBACrB,SAAS,EAAE,KAAK,CAAC,SAAS;wBAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;IACjB,oBAAA,MAAM,EAAE,SAAS;IAClB,iBAAA,CAAC,CAAC;iBACJ;IACH,SAAC,CAAC;;IAGF,QAAA,IAAI,CAAC,wBAAwB,CAAC,uBAAuB,GAAG,MAAK;gBAC3D,OAAO,CAAC,GAAG,CACT,8CAA8C,EAC9C,IAAI,CAAC,wBAAyB,CAAC,eAAe,CAC/C,CAAC;IACJ,SAAC,CAAC;SACH;QAEO,MAAM,WAAW,CAAC,OAAY,EAAA;IACpC,QAAA,IAAI;IACF,YAAA,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;IAClC,gBAAA,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;iBACzC;IAED,YAAA,MAAM,IAAI,CAAC,wBAAyB,CAAC,oBAAoB,CACvD,IAAI,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,CACzC,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAyB,CAAC,YAAY,EAAE,CAAC;gBACnE,MAAM,IAAI,CAAC,wBAAyB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAEjE,IAAI,CAAC,WAAW,CAAC;IACf,gBAAA,IAAI,EAAE,QAAQ;IACd,gBAAA,MAAM,EAAE,MAAM;oBACd,KAAK,EAAE,IAAI,CAAC,KAAK;IACjB,gBAAA,MAAM,EAAE,SAAS;IAClB,aAAA,CAAC,CAAC;aACJ;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;aAC9D;SACF;QAEO,MAAM,kBAAkB,CAAC,OAAY,EAAA;IAC3C,QAAA,IAAI;gBACF,IAAI,IAAI,CAAC,wBAAwB,IAAI,OAAO,CAAC,SAAS,EAAE;IACtD,gBAAA,MAAM,IAAI,CAAC,wBAAwB,CAAC,eAAe,CACjD,IAAI,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CACvC,CAAC;iBACH;aACF;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;aACtE;SACF;QAED,OAAO,GAAA;IACL,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;IACtB,YAAA,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;aACjE;IAED,QAAA,IAAI,IAAI,CAAC,wBAAwB,EAAE;IACjC,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;aACvC;IAED,QAAA,IAAI,IAAI,CAAC,EAAE,EAAE;IACX,YAAA,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;aACjB;SACF;IACF,CAAA;IAED;IACA,CAAC,MAAK;;IAIJ,IAAA,IAAK,MAAc,CAAC,iBAAiB,EAAE;IACrC,QAAA,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;YAClE,OAAO;SACR;;IAGA,IAAA,MAAc,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;;QAGtD,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9D,MAAM,kBAAkB,GAAG,yBAAyB,CAAC;QACrD,MAAM,KAAK,GAAG,WAAW,CAAC;QAC1B,MAAM,cAAc,GAAG,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,OAAO,CAAC;IAElE,IAAiC;YAC/B,IAAI,iBAAiB,CAAC,kBAAkB,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;SAClE;IACH,CAAC,GAAG;;;;;;;;"}