var TargetTabStreamer = (function () {
    'use strict';

    /**
     * Target Tab Streamer Script
     *
     * This script is injected into target tabs to automatically capture their content
     * and stream it via WebRTC. Uses preferCurrentTab: true for automatic capture.
     */
    class TargetTabStreamer {
        constructor(signalingServerUrl, tabId, autoInitialize) {
            this.ws = null;
            this.isConnected = false;
            this.captureStream = null;
            this.controlTabPeerConnection = null;
            this.signalingServerUrl = signalingServerUrl;
            this.tabId = tabId;
            console.log("[POC-Streamer] Target tab streamer initializing...");
            this.init(autoInitialize === "true" || autoInitialize === true);
        }
        async init(autoInitialize) {
            try {
                // Tab ID is already set in constructor
                console.log("[POC-Streamer] Using tab ID:", this.tabId);
                // Connect to signaling server
                await this.connectToSignalingServer();
                if (autoInitialize) {
                    await this.handleStartStream();
                }
                console.log("[POC-Streamer] Target tab streamer initialized successfully");
            }
            catch (error) {
                console.error("[POC-Streamer] Failed to initialize target streamer:", error);
            }
        }
        async connectToSignalingServer() {
            return new Promise((resolve, reject) => {
                try {
                    this.ws = new WebSocket(this.signalingServerUrl);
                    this.ws.onopen = () => {
                        console.log("[POC-Streamer] Connected to signaling server");
                        this.isConnected = true;
                        // Register as target tab
                        this.sendMessage({
                            type: "register",
                            role: "target",
                            tabId: this.tabId,
                        });
                        resolve();
                    };
                    this.ws.onmessage = (event) => {
                        this.handleSignalingMessage(JSON.parse(event.data));
                    };
                    this.ws.onclose = () => {
                        console.log("[POC-Streamer] Disconnected from signaling server");
                        this.isConnected = false;
                    };
                    this.ws.onerror = (error) => {
                        console.error("[POC-Streamer] WebSocket error:", error);
                        reject(error);
                    };
                }
                catch (error) {
                    reject(error);
                }
            });
        }
        sendMessage(message) {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify(message));
            }
        }
        async handleSignalingMessage(message) {
            console.log("[POC-Streamer] Received signaling message:", message.type);
            switch (message.type) {
                case "start-stream":
                    await this.handleStartStream();
                    break;
                case "stop-stream":
                    await this.handleStopStream();
                    break;
                case "offer":
                    await this.handleOffer(message);
                    break;
                case "ice-candidate":
                    await this.handleIceCandidate(message);
                    break;
                default:
                    console.log("[POC-Streamer] Unknown message type:", message.type);
            }
        }
        async handleStartStream() {
            try {
                console.log("[POC-Streamer] Starting screen capture...");
                // Request screen capture with preferCurrentTab
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: "screen",
                        width: { ideal: 1920 },
                        height: { ideal: 1080 },
                        frameRate: { ideal: 30 },
                    },
                    audio: true,
                    preferCurrentTab: true,
                });
                this.captureStream = stream;
                // Create peer connection for control tab
                await this.createControlTabConnection();
                console.log("[POC-Streamer] Screen capture started successfully");
                // Notify signaling server
                this.sendMessage({
                    type: "stream-started",
                    tabId: this.tabId,
                });
            }
            catch (error) {
                console.error("[POC-Streamer] Failed to start screen capture:", error);
                this.sendMessage({
                    type: "stream-error",
                    tabId: this.tabId,
                    error: error.message,
                });
            }
        }
        async handleStopStream() {
            try {
                console.log("[POC-Streamer] Stopping stream...");
                if (this.captureStream) {
                    this.captureStream.getTracks().forEach((track) => track.stop());
                    this.captureStream = null;
                }
                if (this.controlTabPeerConnection) {
                    this.controlTabPeerConnection.close();
                    this.controlTabPeerConnection = null;
                }
                console.log("[POC-Streamer] Stream stopped");
                this.sendMessage({
                    type: "stream-stopped",
                    tabId: this.tabId,
                });
            }
            catch (error) {
                console.error("[POC-Streamer] Error stopping stream:", error);
            }
        }
        async createControlTabConnection() {
            this.controlTabPeerConnection = new RTCPeerConnection({
                iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
            });
            // Add stream tracks to peer connection
            if (this.captureStream) {
                this.captureStream.getTracks().forEach((track) => {
                    this.controlTabPeerConnection.addTrack(track, this.captureStream);
                });
            }
            // Handle ICE candidates
            this.controlTabPeerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    this.sendMessage({
                        type: "ice-candidate",
                        candidate: event.candidate,
                        tabId: this.tabId,
                        target: "control",
                    });
                }
            };
            // Handle connection state changes
            this.controlTabPeerConnection.onconnectionstatechange = () => {
                console.log("[POC-Streamer] Control tab connection state:", this.controlTabPeerConnection.connectionState);
            };
        }
        async handleOffer(message) {
            try {
                if (!this.controlTabPeerConnection) {
                    await this.createControlTabConnection();
                }
                await this.controlTabPeerConnection.setRemoteDescription(new RTCSessionDescription(message.offer));
                const answer = await this.controlTabPeerConnection.createAnswer();
                await this.controlTabPeerConnection.setLocalDescription(answer);
                this.sendMessage({
                    type: "answer",
                    answer: answer,
                    tabId: this.tabId,
                    target: "control",
                });
            }
            catch (error) {
                console.error("[POC-Streamer] Error handling offer:", error);
            }
        }
        async handleIceCandidate(message) {
            try {
                if (this.controlTabPeerConnection && message.candidate) {
                    await this.controlTabPeerConnection.addIceCandidate(new RTCIceCandidate(message.candidate));
                }
            }
            catch (error) {
                console.error("[POC-Streamer] Error handling ICE candidate:", error);
            }
        }
        cleanup() {
            if (this.captureStream) {
                this.captureStream.getTracks().forEach((track) => track.stop());
            }
            if (this.controlTabPeerConnection) {
                this.controlTabPeerConnection.close();
            }
            if (this.ws) {
                this.ws.close();
            }
        }
    }
    // IIFE to initialize the target tab streamer
    (() => {
        // Prevent multiple injections
        if (window.TargetTabStreamer) {
            console.log("[POC-Streamer] Target streamer already initialized");
            return;
        }
        // Make available globally
        window.TargetTabStreamer = TargetTabStreamer;
        // Auto-initialize if URL parameters are present
        const urlParams = new URLSearchParams(window.location.search);
        const signalingServerUrl = "${SIGNALING_SERVER_URL}";
        const tabId = "${TAB_ID}";
        const autoInitialize = urlParams.get("autoInitialize") || "false";
        {
            new TargetTabStreamer(signalingServerUrl, tabId, autoInitialize);
        }
    })();

    return TargetTabStreamer;

})();
//# sourceMappingURL=target-tab-streamer.js.map
