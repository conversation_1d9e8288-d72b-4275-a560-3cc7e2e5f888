{"version": 3, "file": "interceptor-registry.js", "sources": ["../src/interceptor-registry.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;IAAA;;;;;;;;;;;;IAYG;IAcH,MAAM,mBAAmB,CAAA;IAgBvB,IAAA,WAAA,GAAA;;IAdQ,QAAA,IAAA,CAAA,kBAAkB,GAAG,IAAI,GAAG,EAAkC,CAAC;;IAG/D,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAA6B,CAAC;;IAGtD,QAAA,IAAA,CAAA,kBAAkB,GAAG,IAAI,GAAG,EAGjC,CAAC;;IAGI,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAA+B,CAAC;IAG7D,QAAA,IAAI,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;SAC7C;IAED;;IAEG;IACH,IAAA,QAAQ,CACN,IAAY,EACZ,gBAAwC,EACxC,gBAAmC,EAAE,EAAA;YAErC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;IACrC,YAAA,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;aAChE;YAED,IAAI,CAAC,gBAAgB,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;IAC/D,YAAA,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;aACrE;YAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;YACpD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IAE7C,QAAA,IAAI,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAA,CAAE,CAAC,CAAC;SAC7C;IAED;;IAEG;IACH,IAAA,UAAU,CAAC,IAAY,EAAA;YACrB,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACrC,YAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjC,YAAA,IAAI,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAA,CAAE,CAAC,CAAC;aAC/C;SACF;IAED;;IAEG;QACH,yBAAyB,GAAA;YACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC;SACnD;IAED;;IAEG;IACH,IAAA,YAAY,CAAC,IAAY,EAAA;YACvB,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC1C;IAED;;IAEG;QACH,MAAM,CACJ,IAAY,EACZ,MAA0B,EAAA;YAE1B,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,CAAC,gBAAgB,EAAE;IACrB,YAAA,IAAI,CAAC,GAAG,CAAC,4CAA4C,IAAI,CAAA,CAAE,CAAC,CAAC;IAC7D,YAAA,OAAO,IAAI,CAAC;aACb;IAED,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAC1D,MAAM,WAAW,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC;IAEpD,QAAA,IAAI;IACF,YAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aAChD;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,GAAG,CAAC,CAAA,2BAAA,EAA8B,IAAI,CAAG,CAAA,CAAA,EAAE,KAAK,CAAC,CAAC;IACvD,YAAA,OAAO,IAAI,CAAC;aACb;SACF;IAED;;IAEG;IACH,IAAA,sBAAsB,CACpB,WAAmB,EACnB,mBAA6B,EAAE,EAC/B,UAA6C,EAAE,EAAA;;IAG/C,QAAA,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE;gBACnC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IACtC,gBAAA,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAA,CAAE,CAAC,CAAC;iBACjD;aACF;;IAGD,QAAA,MAAM,YAAY,GAAwB;IACxC,YAAA,gBAAgB,EAAE,CAAC,GAAG,gBAAgB,CAAC;gBACvC,OAAO,EAAE,IAAI,GAAG,EAAE;aACnB,CAAC;;IAGF,QAAA,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE;IACnC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC1D,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACvC,YAAA,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,GAAG,aAAa,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;aACrE;YAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAClD,IAAI,CAAC,GAAG,CAAC,CAAA,6BAAA,EAAgC,WAAW,CAAG,CAAA,CAAA,EAAE,gBAAgB,CAAC,CAAC;SAC5E;IAED;;IAEG;IACH,IAAA,sBAAsB,CAAC,WAAmB,EAAA;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,EAAE,gBAAgB,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC;aACrD;YAED,OAAO;IACL,YAAA,gBAAgB,EAAE,CAAC,GAAG,MAAM,CAAC,gBAAgB,CAAC;IAC9C,YAAA,OAAO,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC;aACjC,CAAC;SACH;IAED;;IAEG;IACH,IAAA,wBAAwB,CAAC,WAAmB,EAAA;;YAE1C,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;IAC5C,YAAA,IAAI,CAAC,GAAG,CACN,yCAAyC,WAAW,CAAA,8BAAA,CAAgC,CACrF,CAAC;gBACF,MAAM,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;gBACvE,OAAO,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,CAAC;aAClD;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;YAC9D,MAAM,YAAY,GAA+B,EAAE,CAAC;IACpD,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAoC,CAAC;IAEnE,QAAA,IAAI;IACF,YAAA,KAAK,MAAM,IAAI,IAAI,YAAY,CAAC,gBAAgB,EAAE;oBAChD,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3D,gBAAA,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBAEpD,IAAI,CAAC,gBAAgB,EAAE;IACrB,oBAAA,IAAI,CAAC,GAAG,CACN,4CAA4C,IAAI,CAAA,UAAA,CAAY,CAC7D,CAAC;wBACF,SAAS;qBACV;;oBAGD,MAAM,WAAW,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACvD,gBAAA,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC/B,gBAAA,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;oBAEtC,IAAI,CAAC,GAAG,CAAC,CAAA,QAAA,EAAW,IAAI,CAA2B,wBAAA,EAAA,WAAW,CAAE,CAAA,CAAC,CAAC;iBACnE;;gBAGD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IAEzD,YAAA,OAAO,YAAY,CAAC;aACrB;YAAC,OAAO,KAAK,EAAE;;IAEd,YAAA,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;IACtC,gBAAA,IAAI;wBACF,WAAW,CAAC,OAAO,EAAE,CAAC;qBACvB;oBAAC,OAAO,YAAY,EAAE;IACrB,oBAAA,IAAI,CAAC,GAAG,CACN,wDAAwD,EACxD,YAAY,CACb,CAAC;qBACH;iBACF;gBAED,MAAM,IAAI,KAAK,CACb,CAA4C,yCAAA,EAAA,WAAW,KACrD,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CACvD,CAAE,CAAA,CACH,CAAC;aACH;SACF;IAED;;IAEG;IACH,IAAA,qBAAqB,CAAC,WAAmB,EAAA;YACvC,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAChE,IAAI,CAAC,cAAc,EAAE;IACnB,YAAA,OAAO,EAAE,CAAC;aACX;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;YAC9D,OAAO,YAAY,CAAC,gBAAgB;IACjC,aAAA,GAAG,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBACvC,MAAM,CACL,CAAC,WAAW,KACV,WAAW,KAAK,SAAS,CAC5B,CAAC;SACL;IAED;;IAEG;IACH,IAAA,6BAA6B,CAC3B,WAAmB,EACnB,eAAuB,EACvB,SAAqC,EAAA;YAErC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACzD,IAAI,CAAC,YAAY,EAAE;IACjB,YAAA,MAAM,IAAI,KAAK,CAAC,sCAAsC,WAAW,CAAA,CAAE,CAAC,CAAC;aACtE;;IAGD,QAAA,MAAM,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;IACtE,QAAA,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE;IACxC,YAAA,GAAG,aAAa;IAChB,YAAA,GAAG,SAAS;IACb,SAAA,CAAC,CAAC;;YAGH,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAChE,IAAI,cAAc,IAAI,cAAc,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;gBACzD,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAAC,eAAe,CAAE,CAAC;IACzD,YAAA,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACrC;YAED,IAAI,CAAC,GAAG,CAAC,CAAA,QAAA,EAAW,eAAe,CAAsB,mBAAA,EAAA,WAAW,CAAE,CAAA,CAAC,CAAC;SACzE;IAED;;IAEG;IACH,IAAA,yBAAyB,CAAC,WAAmB,EAAA;YAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAChE,IAAI,cAAc,EAAE;gBAClB,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,cAAc,EAAE;IAChD,gBAAA,IAAI;wBACF,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,IAAI,CAAC,GAAG,CAAC,CAAA,WAAA,EAAc,IAAI,CAA2B,wBAAA,EAAA,WAAW,CAAE,CAAA,CAAC,CAAC;qBACtE;oBAAC,OAAO,KAAK,EAAE;wBACd,IAAI,CAAC,GAAG,CAAC,CAAA,kBAAA,EAAqB,IAAI,CAAe,aAAA,CAAA,EAAE,KAAK,CAAC,CAAC;qBAC3D;iBACF;IAED,YAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;aAC7C;;IAGD,QAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAEvC,QAAA,IAAI,CAAC,GAAG,CAAC,0CAA0C,WAAW,CAAA,CAAE,CAAC,CAAC;SACnE;IAED;;IAEG;QACH,QAAQ,GAAA;IAiBN,QAAA,MAAM,KAAK,GAAG;IACZ,YAAA,sBAAsB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI;IACpD,YAAA,aAAa,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI;IAC3C,YAAA,uBAAuB,EAAE,CAAC;IAC1B,YAAA,WAAW,EAAE,EAAyB;aACvC,CAAC;YAEF,KAAK,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACnE,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/D,YAAA,KAAK,CAAC,uBAAuB,IAAI,kBAAkB,CAAC,MAAM,CAAC;IAE3D,YAAA,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG;oBAC/B,gBAAgB,EAAE,kBAAkB,CAAC,MAAM;oBAC3C,YAAY,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;wBAC3C,IAAI,EAAE,CAAC,CAAC,IAAI;wBACZ,IAAI,EAAE,CAAC,CAAC,IAAI;wBACZ,SAAS,EAAE,CAAC,CAAC,SAAS;IACtB,oBAAA,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE;IACpB,iBAAA,CAAC,CAAC;iBACJ,CAAC;aACH;IAED,QAAA,OAAO,KAAK,CAAC;SACd;IAED;;IAEG;QACK,GAAG,CAAC,GAAG,IAAW,EAAA;YACxB,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,GAAG,IAAI,CAAC,CAAC;SAC/C;IACF,CAAA;IAED;AACA,UAAM,mBAAmB,GAAG,IAAI,mBAAmB,GAAG;IAMtD;IACA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;IAChC,IAAA,MAAc,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IACzD,IAAA,MAAc,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IAC5D;;;;;;;;;;;;;;"}