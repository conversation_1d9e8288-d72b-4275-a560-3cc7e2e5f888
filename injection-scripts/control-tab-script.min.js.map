{"version": 3, "file": "control-tab-script.min.js", "sources": ["../src/control-tab-script.ts"], "sourcesContent": [null], "names": ["ControlTabManager", "constructor", "this", "signalingServerUrl", "websocket", "isConnected", "rtcConfig", "iceServers", "urls", "iceCandidatePoolSize", "webClientGroups", "Map", "tabGroups", "targetConnections", "clientInterceptorConfigs", "defaultInterceptorConfig", "interceptorNames", "interceptorConfigs", "enabled", "enableCropping", "cropRegion", "x", "y", "width", "window", "innerWidth", "height", "innerHeight", "changeThreshold", "stabilityThreshold", "consecutiveStableFrames", "maxWaitDuration", "comparisonInterval", "pixelSampling", "brightness", "blurRadius", "cdpManager", "init", "console", "log", "initializeInterceptorRegistry", "initializeCDPManager", "connectToSignalingServer", "CDPManager", "Error", "initializeDefaultHandlers", "error", "interceptorRegistry", "VideoFrameInterceptor", "register", "debug", "BrightnessInterceptor", "BlurInterceptor", "ChangeDetectorInterceptor", "getRegisteredInterceptors", "warn", "Promise", "resolve", "reject", "WebSocket", "onopen", "onmessage", "event", "handleSignalingMessage", "JSON", "parse", "data", "onclose", "onerror", "message", "type", "handleWebClientOffer", "handleWebClientIceCandidate", "handleUserEvent", "handleInterceptorConfigUpdate", "webClientId", "offer", "peerConnection", "RTCPeerConnection", "dataChannel", "createDataChannel", "ordered", "set", "interceptorPipeline", "webClient", "id", "setRemoteDescription", "answer", "createAnswer", "setLocalDescription", "sendMessage", "candidate", "webClientGroup", "get", "addIceCandidate", "userEvent", "targetTabId", "name", "config", "Object", "entries", "updateInterceptorConfig", "send", "stringify", "cleanup", "group", "close", "tabId", "connection", "controlTabInjected", "controlTabManager"], "mappings": "6CA+BA,MAAMA,EA8DJ,WAAAC,GA7DQC,KAAkBC,mBAAW,sBAC7BD,KAASE,UAAqB,KAC9BF,KAAWG,aAAY,EAGvBH,KAAAI,UAA8B,CACpCC,WAAY,CACV,CAAEC,KAAM,iCACR,CAAEA,KAAM,iCAEVC,qBAAsB,IAIhBP,KAAAQ,gBAAkB,IAAIC,IACtBT,KAAAU,UAAY,IAAID,IAChBT,KAAAW,kBAAoB,IAAIF,IACxBT,KAAAY,yBAA2B,IAAIH,IAG/BT,KAAAa,yBAAqD,CAC3DC,iBAAkB,CAChB,kBACA,oBACA,cACA,cAEFC,mBAAoB,CAClB,aAAc,CACZC,SAAS,EACTC,gBAAgB,EAChBC,WAAY,CACVC,EAAG,EACHC,EAAG,EACHC,MAAOC,OAAOC,WACdC,OAAQF,OAAOG,cAGnB,kBAAmB,CACjBT,SAAS,EACTU,gBAAiB,EACjBC,mBAAoB,EACpBC,wBAAyB,EACzBC,gBAAiB,IACjBC,mBAAoB,IACpBC,cAAe,GAEjB,oBAAqB,CACnBf,SAAS,EACTgB,WAAY,KAEd,cAAe,CACbhB,SAAS,EACTiB,WAAY,KAMVjC,KAAUkC,WAAsB,KAGtClC,KAAKmC,MACN,CAED,UAAMA,GACJC,QAAQC,IAAI,uDAGZrC,KAAKsC,sCAGCtC,KAAKuC,6BAGLvC,KAAKwC,2BAEXJ,QAAQC,IAAI,+DACb,CAKD,0BAAME,GACJ,IACE,QAAiC,IAAtBjB,OAAOmB,WAChB,MAAM,IAAIC,MACR,mEAIJ1C,KAAKkC,WAAaZ,OAAOmB,WAIvBzC,KAAKkC,YACyD,mBAAtDlC,KAAKkC,WAAmBS,2BAE/B3C,KAAKkC,WAAmBS,4BAG3BP,QAAQC,IAAI,uDACb,CAAC,MAAOO,GAEP,MADAR,QAAQQ,MAAM,oDAAqDA,GAC7DA,CACP,CACF,CAKD,6BAAAN,QAE4C,IAA/BhB,OAAOuB,0BAE4B,IAAjCvB,OAAOwB,uBAChBxB,OAAOuB,oBAAoBE,SACzB,aACAzB,OAAOwB,sBACP,CACEE,OAAO,EACP/B,gBAAgB,SAMsB,IAAjCK,OAAO2B,uBAChB3B,OAAOuB,oBAAoBE,SACzB,oBACAzB,OAAO2B,sBACP,CACED,OAAO,EACPhB,WAAY,SAMoB,IAA3BV,OAAO4B,iBAChB5B,OAAOuB,oBAAoBE,SACzB,cACAzB,OAAO4B,gBACP,CACEF,OAAO,EACPf,WAAY,SAM8B,IAArCX,OAAO6B,2BAChB7B,OAAOuB,oBAAoBE,SACzB,kBACAzB,OAAO6B,0BACP,CACEH,OAAO,EACPhC,SAAS,EACTU,gBAAiB,EACjBC,mBAAoB,EACpBC,wBAAyB,EACzBC,gBAAiB,IACjBC,mBAAoB,IACpBC,cAAe,IAKrBK,QAAQC,IACN,yDACAf,OAAOuB,oBAAoBO,8BAG7BhB,QAAQiB,KAAK,qDAEhB,CAKD,8BAAMb,GACJ,OAAO,IAAIc,QAAQ,CAACC,EAASC,KAC3B,IACExD,KAAKE,UAAY,IAAIuD,UAAUzD,KAAKC,oBAEpCD,KAAKE,UAAUwD,OAAS,KACtBtB,QAAQC,IAAI,iDACZrC,KAAKG,aAAc,EACnBoD,KAGFvD,KAAKE,UAAUyD,UAAaC,IAC1B5D,KAAK6D,uBAAuBC,KAAKC,MAAMH,EAAMI,QAG/ChE,KAAKE,UAAU+D,QAAU,KACvB7B,QAAQC,IAAI,sDACZrC,KAAKG,aAAc,GAGrBH,KAAKE,UAAUgE,QAAWtB,IACxBR,QAAQQ,MAAM,mCAAoCA,GAClDY,EAAOZ,GAEV,CAAC,MAAOA,GACPR,QAAQQ,MACN,yDACAA,GAEFY,EAAOZ,EACR,GAEJ,CAKO,sBAAAiB,CAAuBM,GAG7B,OAFA/B,QAAQC,IAAI,8CAA+C8B,GAEnDA,EAAQC,MACd,IAAK,mBACHpE,KAAKqE,qBAAqBF,GAC1B,MACF,IAAK,2BACHnE,KAAKsE,4BAA4BH,GACjC,MACF,IAAK,aACHnE,KAAKuE,gBAAgBJ,GACrB,MACF,IAAK,4BACHnE,KAAKwE,8BAA8BL,GACnC,MACF,QACE/B,QAAQiB,KAAK,wCAAyCc,EAAQC,MAEnE,CAKO,0BAAMC,CAAqBF,GACjC,IACE,MAAMM,YAAEA,EAAWC,MAAEA,GAAUP,EAC/B/B,QAAQC,IACN,mDAAmDoC,KAIrD,MAAME,EAAiB,IAAIC,kBAAkB5E,KAAKI,WAG5CyE,EAAcF,EAAeG,kBAAkB,UAAW,CAC9DC,SAAS,IAIX/E,KAAKQ,gBAAgBwE,IAAIP,EAAa,CACpCE,iBACAM,oBAAqB,KACrBJ,cACAK,UAAW,CAAEC,GAAIV,WAIbE,EAAeS,qBAAqBV,GAG1C,MAAMW,QAAeV,EAAeW,qBAC9BX,EAAeY,oBAAoBF,GAGzCrF,KAAKwF,YAAY,CACfpB,KAAM,qBACNK,cACAY,WAGFjD,QAAQC,IAAI,8CAA8CoC,IAC3D,CAAC,MAAO7B,GACPR,QAAQQ,MAAM,mDAAoDA,EACnE,CACF,CAKO,iCAAM0B,CAA4BH,GACxC,IACE,MAAMM,YAAEA,EAAWgB,UAAEA,GAActB,EAC7BuB,EAAiB1F,KAAKQ,gBAAgBmF,IAAIlB,GAE5CiB,UACIA,EAAef,eAAeiB,gBAAgBH,GACpDrD,QAAQC,IACN,uDAAuDoC,KAG5D,CAAC,MAAO7B,GACPR,QAAQQ,MAAM,gDAAiDA,EAChE,CACF,CAKO,qBAAM2B,CAAgBJ,GAC5B,IACE,MAAM0B,UAAEA,EAASC,YAAEA,GAAgB3B,EAGjCnE,KAAKkC,YAC+C,mBAA5ClC,KAAKkC,WAAmBqC,uBAEzBvE,KAAKkC,WAAmBqC,gBAAgBsB,EAAWC,GAC1D1D,QAAQC,IACN,+CAA+CyD,MAGjD1D,QAAQiB,KACN,oEAGL,CAAC,MAAOT,GACPR,QAAQQ,MAAM,6CAA8CA,EAC7D,CACF,CAKO,6BAAA4B,CAA8BL,GACpC,IACE,MAAMM,YAAEA,EAAW1D,mBAAEA,GAAuBoD,EAC5C/B,QAAQC,IACN,2DAA2DoC,KAI7DzE,KAAKY,yBAAyBoE,IAAIP,EAAa1D,GAG/C,MAAM2E,EAAiB1F,KAAKQ,gBAAgBmF,IAAIlB,GAChD,GAAIiB,GAAkBA,EAAeT,oBAEnC,IAAK,MAAOc,EAAMC,KAAWC,OAAOC,QAAQnF,GAC1C2E,EAAeT,oBAAoBkB,wBACjCJ,EACAC,GAKN5D,QAAQC,IACN,0DAA0DoC,IAE7D,CAAC,MAAO7B,GACPR,QAAQQ,MACN,qDACAA,EAEH,CACF,CAKD,WAAA4C,CAAYrB,GACNnE,KAAKE,WAAaF,KAAKG,YACzBH,KAAKE,UAAUkG,KAAKtC,KAAKuC,UAAUlC,IAEnC/B,QAAQiB,KACN,0EAGL,CAKD,aAAMiD,GACJlE,QAAQC,IAAI,sDAGZ,IAAK,MAAOoC,EAAa8B,KAAUvG,KAAKQ,gBACtC,IACM+F,EAAMtB,2BACFsB,EAAMtB,oBAAoBqB,UAElCC,EAAM5B,eAAe6B,OACtB,CAAC,MAAO5D,GACPR,QAAQQ,MACN,gDAAgD6B,KAChD7B,EAEH,CAIH,IAAK,MAAO6D,EAAOC,KAAe1G,KAAKW,kBACrC,IACE+F,EAAWF,OACZ,CAAC,MAAO5D,GACPR,QAAQQ,MACN,uDAAuD6D,KACvD7D,EAEH,CAIC5C,KAAKE,WACPF,KAAKE,UAAUsG,QAKfxG,KAAKkC,YACuC,mBAApClC,KAAKkC,WAAmBoE,eAEzBtG,KAAKkC,WAAmBoE,UAGjClE,QAAQC,IAAI,wDACb,SAIH,WAKE,GAHAD,QAAQC,IAAI,sDAGRf,OAAOqF,mBAIT,YAHAvE,QAAQC,IACN,oEAIJf,OAAOqF,oBAAqB,EAG5B,MAAMC,EAAoB,IAAI9G,EAG7BwB,OAAesF,kBAAoBA,EAEpCxE,QAAQC,IAAI,8DACb,CApBD"}