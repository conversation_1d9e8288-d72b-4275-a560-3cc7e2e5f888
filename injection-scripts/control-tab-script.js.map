{"version": 3, "file": "control-tab-script.js", "sources": ["../src/control-tab-script.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;IAAA;;;;;IAKG;IA0BH,MAAM,iBAAiB,CAAA;IA8DrB,IAAA,WAAA,GAAA;YA7DQ,IAAkB,CAAA,kBAAA,GAAW,qBAAqB,CAAC;YACnD,IAAS,CAAA,SAAA,GAAqB,IAAI,CAAC;YACnC,IAAW,CAAA,WAAA,GAAY,KAAK,CAAC;;IAG7B,QAAA,IAAA,CAAA,SAAS,GAAqB;IACpC,YAAA,UAAU,EAAE;oBACV,EAAE,IAAI,EAAE,+BAA+B,EAAE;oBACzC,EAAE,IAAI,EAAE,8BAA8B,EAAE;IACzC,aAAA;IACD,YAAA,oBAAoB,EAAE,EAAE;aACzB,CAAC;;IAGM,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,GAAG,EAA0B,CAAC;IACpD,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAAoB,CAAC;IACxC,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,GAAG,EAA6B,CAAC;IACzD,QAAA,IAAA,CAAA,wBAAwB,GAAG,IAAI,GAAG,EAAe,CAAC;;IAGlD,QAAA,IAAA,CAAA,wBAAwB,GAA6B;IAC3D,YAAA,gBAAgB,EAAE;oBAChB,iBAAiB;oBACjB,mBAAmB;oBACnB,aAAa;oBACb,YAAY;IACb,aAAA;IACD,YAAA,kBAAkB,EAAE;IAClB,gBAAA,YAAY,EAAE;IACZ,oBAAA,OAAO,EAAE,IAAI;IACb,oBAAA,cAAc,EAAE,IAAI;IACpB,oBAAA,UAAU,EAAE;IACV,wBAAA,CAAC,EAAE,CAAC;IACJ,wBAAA,CAAC,EAAE,CAAC;4BACJ,KAAK,EAAE,MAAM,CAAC,UAAU;4BACxB,MAAM,EAAE,MAAM,CAAC,WAAW;IAC3B,qBAAA;IACF,iBAAA;IACD,gBAAA,iBAAiB,EAAE;IACjB,oBAAA,OAAO,EAAE,KAAK;IACd,oBAAA,eAAe,EAAE,CAAC;IAClB,oBAAA,kBAAkB,EAAE,CAAC;IACrB,oBAAA,uBAAuB,EAAE,CAAC;IAC1B,oBAAA,eAAe,EAAE,IAAI;IACrB,oBAAA,kBAAkB,EAAE,GAAG;IACvB,oBAAA,aAAa,EAAE,CAAC;IACjB,iBAAA;IACD,gBAAA,mBAAmB,EAAE;IACnB,oBAAA,OAAO,EAAE,IAAI;IACb,oBAAA,UAAU,EAAE,GAAG;IAChB,iBAAA;IACD,gBAAA,aAAa,EAAE;IACb,oBAAA,OAAO,EAAE,IAAI;IACb,oBAAA,UAAU,EAAE,CAAC;IACd,iBAAA;IACF,aAAA;aACF,CAAC;;YAGM,IAAU,CAAA,UAAA,GAAsB,IAAI,CAAC;YAG3C,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;IAED,IAAA,MAAM,IAAI,GAAA;IACR,QAAA,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;;YAGnE,IAAI,CAAC,6BAA6B,EAAE,CAAC;;IAGrC,QAAA,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;;IAGlC,QAAA,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAEtC,QAAA,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;SAC7E;IAED;;IAEG;IACH,IAAA,MAAM,oBAAoB,GAAA;IACxB,QAAA,IAAI;IACF,YAAA,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,WAAW,EAAE;IAC5C,gBAAA,MAAM,IAAI,KAAK,CACb,iEAAiE,CAClE,CAAC;iBACH;IAED,YAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;;gBAGpC,IACE,IAAI,CAAC,UAAU;oBACf,OAAQ,IAAI,CAAC,UAAkB,CAAC,yBAAyB,KAAK,UAAU,EACxE;IACC,gBAAA,IAAI,CAAC,UAAkB,CAAC,yBAAyB,EAAE,CAAC;iBACtD;IAED,YAAA,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;aACrE;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;IAC1E,YAAA,MAAM,KAAK,CAAC;aACb;SACF;IAED;;IAEG;QACH,6BAA6B,GAAA;;IAE3B,QAAA,IAAI,OAAO,MAAM,CAAC,mBAAmB,KAAK,WAAW,EAAE;;IAErD,YAAA,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,WAAW,EAAE;oBACvD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CACjC,YAAY,EACZ,MAAM,CAAC,qBAAqB,EAC5B;IACE,oBAAA,KAAK,EAAE,IAAI;IACX,oBAAA,cAAc,EAAE,IAAI;IACrB,iBAAA,CACF,CAAC;iBACH;;IAGD,YAAA,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,WAAW,EAAE;oBACvD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CACjC,mBAAmB,EACnB,MAAM,CAAC,qBAAqB,EAC5B;IACE,oBAAA,KAAK,EAAE,IAAI;IACX,oBAAA,UAAU,EAAE,GAAG;IAChB,iBAAA,CACF,CAAC;iBACH;;IAGD,YAAA,IAAI,OAAO,MAAM,CAAC,eAAe,KAAK,WAAW,EAAE;oBACjD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CACjC,aAAa,EACb,MAAM,CAAC,eAAe,EACtB;IACE,oBAAA,KAAK,EAAE,IAAI;IACX,oBAAA,UAAU,EAAE,CAAC;IACd,iBAAA,CACF,CAAC;iBACH;;IAGD,YAAA,IAAI,OAAO,MAAM,CAAC,yBAAyB,KAAK,WAAW,EAAE;oBAC3D,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CACjC,iBAAiB,EACjB,MAAM,CAAC,yBAAyB,EAChC;IACE,oBAAA,KAAK,EAAE,IAAI;IACX,oBAAA,OAAO,EAAE,KAAK;IACd,oBAAA,eAAe,EAAE,CAAC;IAClB,oBAAA,kBAAkB,EAAE,CAAC;IACrB,oBAAA,uBAAuB,EAAE,CAAC;IAC1B,oBAAA,eAAe,EAAE,IAAI;IACrB,oBAAA,kBAAkB,EAAE,GAAG;IACvB,oBAAA,aAAa,EAAE,CAAC;IACjB,iBAAA,CACF,CAAC;iBACH;IAED,YAAA,OAAO,CAAC,GAAG,CACT,wDAAwD,EACxD,MAAM,CAAC,mBAAmB,CAAC,yBAAyB,EAAE,CACvD,CAAC;aACH;iBAAM;IACL,YAAA,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;aACpE;SACF;IAED;;IAEG;IACH,IAAA,MAAM,wBAAwB,GAAA;YAC5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;IACrC,YAAA,IAAI;oBACF,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAExD,gBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,MAAK;IAC3B,oBAAA,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAC7D,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,oBAAA,OAAO,EAAE,CAAC;IACZ,iBAAC,CAAC;oBAEF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,KAAK,KAAI;IACnC,oBAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,iBAAC,CAAC;IAEF,gBAAA,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,MAAK;IAC5B,oBAAA,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IAClE,oBAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,iBAAC,CAAC;oBAEF,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,KAAK,KAAI;IACjC,oBAAA,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;wBACzD,MAAM,CAAC,KAAK,CAAC,CAAC;IAChB,iBAAC,CAAC;iBACH;gBAAC,OAAO,KAAK,EAAE;IACd,gBAAA,OAAO,CAAC,KAAK,CACX,wDAAwD,EACxD,KAAK,CACN,CAAC;oBACF,MAAM,CAAC,KAAK,CAAC,CAAC;iBACf;IACH,SAAC,CAAC,CAAC;SACJ;IAED;;IAEG;IACK,IAAA,sBAAsB,CAAC,OAAY,EAAA;IACzC,QAAA,OAAO,CAAC,GAAG,CAAC,6CAA6C,EAAE,OAAO,CAAC,CAAC;IAEpE,QAAA,QAAQ,OAAO,CAAC,IAAI;IAClB,YAAA,KAAK,kBAAkB;IACrB,gBAAA,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;oBACnC,MAAM;IACR,YAAA,KAAK,0BAA0B;IAC7B,gBAAA,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;oBAC1C,MAAM;IACR,YAAA,KAAK,YAAY;IACf,gBAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;oBAC9B,MAAM;IACR,YAAA,KAAK,2BAA2B;IAC9B,gBAAA,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;oBAC5C,MAAM;IACR,YAAA;oBACE,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;aACvE;SACF;IAED;;IAEG;QACK,MAAM,oBAAoB,CAAC,OAAY,EAAA;IAC7C,QAAA,IAAI;IACF,YAAA,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IACvC,YAAA,OAAO,CAAC,GAAG,CACT,mDAAmD,WAAW,CAAA,CAAE,CACjE,CAAC;;gBAGF,MAAM,cAAc,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;;IAG7D,YAAA,MAAM,WAAW,GAAG,cAAc,CAAC,iBAAiB,CAAC,SAAS,EAAE;IAC9D,gBAAA,OAAO,EAAE,IAAI;IACd,aAAA,CAAC,CAAC;;IAGH,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE;oBACpC,cAAc;IACd,gBAAA,mBAAmB,EAAE,IAAI;oBACzB,WAAW;IACX,gBAAA,SAAS,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;IAC/B,aAAA,CAAC,CAAC;;IAGH,YAAA,MAAM,cAAc,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;;IAGjD,YAAA,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,YAAY,EAAE,CAAC;IACnD,YAAA,MAAM,cAAc,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;;gBAGjD,IAAI,CAAC,WAAW,CAAC;IACf,gBAAA,IAAI,EAAE,oBAAoB;oBAC1B,WAAW;oBACX,MAAM;IACP,aAAA,CAAC,CAAC;IAEH,YAAA,OAAO,CAAC,GAAG,CAAC,8CAA8C,WAAW,CAAA,CAAE,CAAC,CAAC;aAC1E;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;aAC1E;SACF;IAED;;IAEG;QACK,MAAM,2BAA2B,CAAC,OAAY,EAAA;IACpD,QAAA,IAAI;IACF,YAAA,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;gBAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAE7D,IAAI,cAAc,EAAE;oBAClB,MAAM,cAAc,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAC/D,gBAAA,OAAO,CAAC,GAAG,CACT,uDAAuD,WAAW,CAAA,CAAE,CACrE,CAAC;iBACH;aACF;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;aACvE;SACF;IAED;;IAEG;QACK,MAAM,eAAe,CAAC,OAAY,EAAA;IACxC,QAAA,IAAI;IACF,YAAA,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;gBAE3C,IACE,IAAI,CAAC,UAAU;oBACf,OAAQ,IAAI,CAAC,UAAkB,CAAC,eAAe,KAAK,UAAU,EAC9D;oBACA,MAAO,IAAI,CAAC,UAAkB,CAAC,eAAe,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IACvE,gBAAA,OAAO,CAAC,GAAG,CACT,+CAA+C,WAAW,CAAA,CAAE,CAC7D,CAAC;iBACH;qBAAM;IACL,gBAAA,OAAO,CAAC,IAAI,CACV,mEAAmE,CACpE,CAAC;iBACH;aACF;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;aACpE;SACF;IAED;;IAEG;IACK,IAAA,6BAA6B,CAAC,OAAY,EAAA;IAChD,QAAA,IAAI;IACF,YAAA,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;IACpD,YAAA,OAAO,CAAC,GAAG,CACT,2DAA2D,WAAW,CAAA,CAAE,CACzE,CAAC;;gBAGF,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;;gBAGnE,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC7D,YAAA,IAAI,cAAc,IAAI,cAAc,CAAC,mBAAmB,EAAE;;IAExD,gBAAA,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;wBAC/D,cAAc,CAAC,mBAAmB,CAAC,uBAAuB,CACxD,IAAI,EACJ,MAAoC,CACrC,CAAC;qBACH;iBACF;IAED,YAAA,OAAO,CAAC,GAAG,CACT,0DAA0D,WAAW,CAAA,CAAE,CACxE,CAAC;aACH;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,KAAK,CACX,oDAAoD,EACpD,KAAK,CACN,CAAC;aACH;SACF;IAED;;IAEG;IACH,IAAA,WAAW,CAAC,OAAY,EAAA;YACtB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;IACtC,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;aAC9C;iBAAM;IACL,YAAA,OAAO,CAAC,IAAI,CACV,yEAAyE,CAC1E,CAAC;aACH;SACF;IAED;;IAEG;IACH,IAAA,MAAM,OAAO,GAAA;IACX,QAAA,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;;YAGlE,KAAK,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE;IACvD,YAAA,IAAI;IACF,gBAAA,IAAI,KAAK,CAAC,mBAAmB,EAAE;IAC7B,oBAAA,MAAM,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;qBAC3C;IACD,gBAAA,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;iBAC9B;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CACX,CAAA,6CAAA,EAAgD,WAAW,CAAG,CAAA,CAAA,EAC9D,KAAK,CACN,CAAC;iBACH;aACF;;YAGD,KAAK,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE;IACxD,YAAA,IAAI;oBACF,UAAU,CAAC,KAAK,EAAE,CAAC;iBACpB;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CACX,CAAA,oDAAA,EAAuD,KAAK,CAAG,CAAA,CAAA,EAC/D,KAAK,CACN,CAAC;iBACH;aACF;;IAGD,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;IAClB,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;aACxB;;YAGD,IACE,IAAI,CAAC,UAAU;gBACf,OAAQ,IAAI,CAAC,UAAkB,CAAC,OAAO,KAAK,UAAU,EACtD;IACA,YAAA,MAAO,IAAI,CAAC,UAAkB,CAAC,OAAO,EAAE,CAAC;aAC1C;IAED,QAAA,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;SACtE;IACF,CAAA;IAED;IACA,CAAC,YAAA;IAEC,IAAA,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;;IAGlE,IAAA,IAAI,MAAM,CAAC,kBAAkB,EAAE;IAC7B,QAAA,OAAO,CAAC,GAAG,CACT,kEAAkE,CACnE,CAAC;YACF,OAAO;SACR;IACD,IAAA,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;;IAGjC,IAAA,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;;IAGjD,IAAA,MAAc,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAEtD,IAAA,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;IAC7E,CAAC,GAAG;;;;;;;;"}