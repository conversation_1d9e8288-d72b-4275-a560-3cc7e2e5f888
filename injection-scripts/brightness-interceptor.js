var BrightnessInterceptor = (function () {
    'use strict';

    /**
     * Base Interceptor Interface
     *
     * Defines the standard interface that all video frame interceptors must implement.
     * Provides common functionality for configuration management, lifecycle, and processing.
     *
     * Features:
     * - Standardized interceptor interface
     * - Built-in configuration management
     * - Lifecycle management (initialize, process, cleanup)
     * - Error handling and fallback mechanisms
     * - Performance monitoring capabilities
     */
    class BaseInterceptor {
        constructor(name, defaultConfig = {}) {
            this.isInitialized = false;
            this.isEnabled = true;
            // Transform stream components
            this.processor = null;
            this.generator = null;
            this.transformStream = null;
            // Track references
            this.originalTrack = null;
            this.processedTrack = null;
            if (this.constructor === BaseInterceptor) {
                throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");
            }
            this.name = name;
            this.type = this.constructor.name;
            // Configuration management - stored internally
            this.config = {
                debug: false,
                enabled: true,
                ...defaultConfig,
            };
            // Performance tracking
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} interceptor created`);
        }
        /**
         * Initialize the interceptor with a video track
         */
        async initialize(videoTrack) {
            if (!videoTrack || videoTrack.kind !== "video") {
                throw new Error(`${this.name} interceptor requires a valid video track`);
            }
            this.originalTrack = videoTrack;
            this.log(`Initializing ${this.name} interceptor with video track:`, videoTrack.label);
            try {
                // Create processor and generator
                this.processor = new MediaStreamTrackProcessor({ track: videoTrack });
                this.generator = new MediaStreamTrackGenerator({ kind: "video" });
                // Create transform stream with bound processing function
                this.transformStream = new TransformStream({
                    transform: this.processFrame.bind(this),
                });
                // Connect the pipeline
                this.processor.readable
                    .pipeThrough(this.transformStream)
                    .pipeTo(this.generator.writable)
                    .catch((error) => {
                    this.log(`Pipeline error in ${this.name}:`, error);
                    this.stats.errorsEncountered++;
                });
                this.processedTrack = this.generator.track;
                this.isInitialized = true;
                this.log(`${this.name} interceptor initialized successfully`);
                return this.processedTrack;
            }
            catch (error) {
                this.log(`Error initializing ${this.name} interceptor:`, error);
                this.stats.errorsEncountered++;
                throw error;
            }
        }
        /**
         * Process a single video frame - handles common logic and delegates to subclass
         */
        async processFrame(frame, controller) {
            const startTime = performance.now();
            try {
                this.stats.framesProcessed++;
                if (!this.isEnabled || !this.config.enabled) {
                    // Pass through original frame if disabled
                    controller.enqueue(frame);
                    return;
                }
                // Call the specific interceptor's processing logic
                const processedFrame = await this.processVideoFrame(frame);
                // Enqueue the processed frame
                controller.enqueue(processedFrame);
                // Clean up original frame if a new one was created
                if (processedFrame !== frame) {
                    frame.close();
                }
            }
            catch (error) {
                this.log(`Error processing frame in ${this.name}:`, error);
                this.stats.errorsEncountered++;
                // Fallback: pass through original frame
                controller.enqueue(frame);
            }
            finally {
                // Update performance stats
                const processingTime = performance.now() - startTime;
                this.stats.lastProcessingTime = processingTime;
                this.stats.totalProcessingTime += processingTime;
                this.stats.averageProcessingTime =
                    this.stats.totalProcessingTime / this.stats.framesProcessed;
            }
        }
        /**
         * Update interceptor configuration
         */
        updateConfig(newConfig) {
            if (!newConfig || typeof newConfig !== "object") {
                this.log("warn", "Invalid configuration provided to updateConfig");
                return;
            }
            const oldConfig = { ...this.config };
            this.config = { ...this.config, ...newConfig };
            this.log(`${this.name} configuration updated:`, {
                old: oldConfig,
                new: this.config,
            });
            // Call configuration change handler if implemented
            if (this.onConfigChange) {
                this.onConfigChange(oldConfig, this.config);
            }
        }
        /**
         * Get current configuration
         */
        getConfig() {
            return { ...this.config };
        }
        /**
         * Enable the interceptor
         */
        enable() {
            this.isEnabled = true;
            this.config.enabled = true;
            this.log(`${this.name} interceptor enabled`);
        }
        /**
         * Disable the interceptor
         */
        disable() {
            this.isEnabled = false;
            this.config.enabled = false;
            this.log(`${this.name} interceptor disabled`);
        }
        /**
         * Get performance statistics
         */
        getStats() {
            return { ...this.stats };
        }
        /**
         * Reset performance statistics
         */
        resetStats() {
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} stats reset`);
        }
        /**
         * Cleanup resources
         */
        async cleanup() {
            try {
                if (this.processor) {
                    this.processor = null;
                }
                if (this.generator) {
                    this.generator = null;
                }
                if (this.transformStream) {
                    this.transformStream = null;
                }
                if (this.originalTrack) {
                    this.originalTrack = null;
                }
                if (this.processedTrack) {
                    this.processedTrack = null;
                }
                this.isInitialized = false;
                this.log(`${this.name} interceptor cleaned up`);
            }
            catch (error) {
                this.log(`Error cleaning up ${this.name} interceptor:`, error);
            }
        }
        /**
         * Logging utility
         */
        log(...args) {
            if (this.config.debug) {
                console.log(`[${this.name}-Interceptor]`, ...args);
            }
        }
        /**
         * Get interceptor metadata
         */
        getMetadata() {
            return {
                name: this.name,
                type: this.type,
                isInitialized: this.isInitialized,
                isEnabled: this.isEnabled,
                config: this.getConfig(),
                stats: this.getStats(),
            };
        }
    }
    // Make available globally for injection scripts
    if (typeof window !== "undefined") {
        window.BaseInterceptor = BaseInterceptor;
    }

    /**
     * Brightness Filter Interceptor
     *
     * A video frame interceptor that adjusts the brightness of video frames.
     * Demonstrates the multi-interceptor system with a simple brightness adjustment.
     *
     * Features:
     * - Real-time brightness adjustment
     * - Configurable brightness levels
     * - Extends BaseInterceptor for standardized interface
     * - Canvas-based frame processing
     */
    class BrightnessInterceptor extends BaseInterceptor {
        constructor(name = "brightness-filter", options = {}) {
            // Default configuration for brightness interceptor
            const defaultConfig = {
                debug: false,
                brightness: 1.0, // 1.0 = normal, 0.5 = darker, 1.5 = brighter
                minBrightness: 0.1,
                maxBrightness: 3.0,
                ...options,
            };
            super(name, defaultConfig);
            // Canvas for frame processing
            this.canvas = null;
            this.ctx = null;
            this.imageData = null;
            // Set up configuration change handler
            this.onConfigChange = this.onConfigUpdate.bind(this);
            this.log("BrightnessInterceptor initialized", this.config);
        }
        /**
         * Handle configuration updates
         */
        onConfigUpdate(oldConfig, newConfig) {
            const oldBrightness = oldConfig.brightness || 1.0;
            const newBrightness = newConfig.brightness || 1.0;
            // Validate brightness value
            if (newBrightness < this.config.minBrightness) {
                this.config.brightness = this.config.minBrightness;
                this.log("warn", `Brightness clamped to minimum: ${this.config.minBrightness}`);
            }
            else if (newBrightness > this.config.maxBrightness) {
                this.config.brightness = this.config.maxBrightness;
                this.log("warn", `Brightness clamped to maximum: ${this.config.maxBrightness}`);
            }
            this.log("info", `Brightness updated from ${oldBrightness} to ${this.config.brightness}`);
        }
        /**
         * Process a video frame - implements BaseInterceptor interface
         */
        async processVideoFrame(frame) {
            // If brightness is 1.0 (normal), pass through unchanged
            if (Math.abs((this.config.brightness || 1.0) - 1.0) < 0.01) {
                return frame;
            }
            try {
                // Initialize canvas if needed
                if (!this.canvas ||
                    this.canvas.width !== frame.codedWidth ||
                    this.canvas.height !== frame.codedHeight) {
                    this.initializeCanvas(frame.codedWidth, frame.codedHeight);
                }
                if (!this.ctx) {
                    throw new Error("Canvas context not available");
                }
                // Draw frame to canvas
                this.ctx.drawImage(frame, 0, 0);
                // Get image data
                this.imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
                const data = this.imageData.data;
                // Apply brightness adjustment
                const brightness = Math.max(this.config.minBrightness, Math.min(this.config.maxBrightness, this.config.brightness || 1.0));
                for (let i = 0; i < data.length; i += 4) {
                    // Adjust RGB channels (skip alpha)
                    data[i] = Math.min(255, data[i] * brightness); // Red
                    data[i + 1] = Math.min(255, data[i + 1] * brightness); // Green
                    data[i + 2] = Math.min(255, data[i + 2] * brightness); // Blue
                    // Alpha channel (data[i + 3]) remains unchanged
                }
                // Put modified image data back to canvas
                this.ctx.putImageData(this.imageData, 0, 0);
                // Create new VideoFrame from canvas
                const processedFrame = new VideoFrame(this.canvas, {
                    timestamp: frame.timestamp,
                    duration: frame.duration ?? undefined,
                });
                return processedFrame;
            }
            catch (error) {
                this.log("Error processing brightness:", error);
                // Return original frame on error
                return frame;
            }
        }
        /**
         * Initialize canvas for frame processing
         */
        initializeCanvas(width, height) {
            this.canvas = document.createElement("canvas");
            this.canvas.width = width;
            this.canvas.height = height;
            this.ctx = this.canvas.getContext("2d");
            if (!this.ctx) {
                throw new Error("Failed to get 2D canvas context");
            }
            this.log(`Canvas initialized: ${width}x${height}`);
        }
        /**
         * Set brightness level
         */
        setBrightness(brightness) {
            const clampedBrightness = Math.max(this.config.minBrightness, Math.min(this.config.maxBrightness, brightness));
            this.updateConfig({ brightness: clampedBrightness });
            this.log(`Brightness set to: ${clampedBrightness}`);
        }
        /**
         * Get current brightness level
         */
        getBrightness() {
            return this.config.brightness || 1.0;
        }
        /**
         * Override cleanup to handle canvas resources
         */
        async cleanup() {
            this.log("Cleaning up brightness interceptor...");
            // Clean up canvas resources
            if (this.canvas) {
                this.canvas.width = 0;
                this.canvas.height = 0;
                this.canvas = null;
            }
            this.ctx = null;
            this.imageData = null;
            // Call parent cleanup
            await super.cleanup();
            this.log("Brightness interceptor cleanup complete");
        }
        /**
         * Get interceptor-specific status
         */
        getStatus() {
            return {
                ...this.getMetadata(),
                brightness: this.config.brightness || 1.0,
                canvasInitialized: !!this.canvas,
                canvasSize: this.canvas
                    ? `${this.canvas.width}x${this.canvas.height}`
                    : null,
            };
        }
    }
    // Make available globally for injection scripts
    if (typeof window !== "undefined") {
        window.BrightnessInterceptor = BrightnessInterceptor;
    }

    return BrightnessInterceptor;

})();
//# sourceMappingURL=brightness-interceptor.js.map
