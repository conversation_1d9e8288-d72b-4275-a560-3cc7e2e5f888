var InterceptorPipeline=function(){"use strict";class e{constructor(e=[]){this.isInitialized=!1,this.isEnabled=!0,this.originalTrack=null,this.processedTrack=null,this.processor=null,this.generator=null,this.transformStream=null,this.interceptors=[...e],this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0,interceptorStats:new Map},this.log("InterceptorPipeline created with",this.interceptors.length,"interceptors")}async initialize(e){if(!e||"video"!==e.kind)throw new Error("InterceptorPipeline requires a valid video track");this.originalTrack=e,this.log("Initializing pipeline with video track:",e.label);try{return this.processor=new MediaStreamTrackProcessor({track:e}),this.generator=new MediaStreamTrackGenerator({kind:"video"}),this.transformStream=new TransformStream({transform:this.processFrame.bind(this)}),this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(e=>{this.log("Pipeline error:",e),this.stats.errorsEncountered++}),this.processedTrack=this.generator.track,this.isInitialized=!0,this.log("Pipeline initialized successfully"),this.processedTrack}catch(e){throw this.log("Error initializing pipeline:",e),this.stats.errorsEncountered++,e}}async processFrame(e,t){const r=performance.now();try{if(this.stats.framesProcessed++,!this.isEnabled||0===this.interceptors.length)return void t.enqueue(e);if(null==e)return;let r=e;const s=[];for(let t=0;t<this.interceptors.length;t++){const i=this.interceptors[t];if(i&&i.config.enabled){console.log("Processing frame through interceptor:",i.name);try{const t=performance.now(),n=await i.processVideoFrame(r),o=performance.now()-t;this.updateInterceptorStats(i.name,o),n!==r&&r!==e&&s.push(r),r=n}catch(e){this.log(`Error in interceptor ${i.name}:`,e),this.stats.errorsEncountered++}}}t.enqueue(r);for(const e of s)try{e.close()}catch(e){this.log("Error cleaning up intermediate frame:",e)}r!==e&&e.close()}catch(r){this.log("Error processing frame through pipeline:",r),this.stats.errorsEncountered++,t.enqueue(e)}finally{const e=performance.now()-r;this.stats.lastProcessingTime=e,this.stats.totalProcessingTime+=e,this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}}updateInterceptorStats(e,t){this.stats.interceptorStats.has(e)||this.stats.interceptorStats.set(e,{framesProcessed:0,totalProcessingTime:0,averageProcessingTime:0});const r=this.stats.interceptorStats.get(e);r.framesProcessed++,r.totalProcessingTime+=t,r.averageProcessingTime=r.totalProcessingTime/r.framesProcessed}addInterceptor(e,t=-1){if(!e||"function"!=typeof e.processVideoFrame)throw new Error("Invalid interceptor: must implement processVideoFrame method");t<0||t>=this.interceptors.length?(this.interceptors.push(e),this.log(`Added interceptor ${e.name} at end of pipeline`)):(this.interceptors.splice(t,0,e),this.log(`Added interceptor ${e.name} at index ${t}`))}removeInterceptor(e){const t=this.interceptors.findIndex(t=>t.name===e);if(t>=0&&t<this.interceptors.length){const e=this.interceptors.splice(t,1)[0];return this.log(`Removed interceptor ${e.name} from pipeline`),!0}return this.log(`Interceptor not found: ${e}`),!1}getInterceptor(e){let t=-1;return"string"==typeof e?t=this.interceptors.findIndex(t=>t.name===e):"number"==typeof e&&(t=e),t>=0&&t<this.interceptors.length?this.interceptors[t]:null}getInterceptorNames(){return this.interceptors.map(e=>e.name)}enable(){this.isEnabled=!0,this.log("Pipeline enabled")}disable(){this.isEnabled=!1,this.log("Pipeline disabled")}updateInterceptorConfig(e,t){const r=this.getInterceptor(e);return!!r&&(r.updateConfig(t),!0)}getStats(){return{...this.stats,interceptorStats:new Map(this.stats.interceptorStats),interceptorCount:this.interceptors.length,enabledInterceptorCount:this.interceptors.filter(e=>e.isEnabled).length}}resetStats(){this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0,interceptorStats:new Map},this.log("Pipeline stats reset")}updateInterceptorConfigs(e){if(e&&"object"==typeof e){this.log("info","Updating interceptor configurations:",e);for(const t of this.interceptors){const r=e[t.name];if(r)try{t.updateConfig(r),this.log("info",`Updated config for interceptor: ${t.name}`)}catch(e){this.log("error",`Failed to update config for interceptor ${t.name}:`,e)}}}else this.log("warn","Invalid interceptor configurations provided")}async cleanup(){try{this.log("Cleaning up pipeline...");for(const e of this.interceptors)try{e.cleanup&&await e.cleanup()}catch(t){this.log(`Error cleaning up interceptor ${e.name}:`,t)}this.interceptors.length=0,this.processor&&(this.processor=null),this.generator&&(this.generator=null),this.transformStream&&(this.transformStream=null),this.originalTrack&&(this.originalTrack=null),this.processedTrack&&(this.processedTrack=null),this.isInitialized=!1,this.log("Pipeline cleanup complete")}catch(e){this.log("Error during pipeline cleanup:",e)}}log(...e){console.log("[InterceptorPipeline]",...e)}}return"undefined"!=typeof window&&(window.InterceptorPipeline=e),e}();
//# sourceMappingURL=interceptor-pipeline.min.js.map
