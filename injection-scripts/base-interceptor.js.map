{"version": 3, "file": "base-interceptor.js", "sources": ["../src/base-interceptor.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;IAAA;;;;;;;;;;;;IAYG;IAQH,MAAe,eAAe,CAAA;QAwB5B,WAAY,CAAA,IAAY,EAAE,aAAA,GAAmC,EAAE,EAAA;YArBxD,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;YAC/B,IAAS,CAAA,SAAA,GAAY,IAAI,CAAC;;YAKzB,IAAS,CAAA,SAAA,GAAqC,IAAI,CAAC;YACnD,IAAS,CAAA,SAAA,GAAqC,IAAI,CAAC;YACnD,IAAe,CAAA,eAAA,GACrB,IAAI,CAAC;;YAGC,IAAa,CAAA,aAAA,GAA4B,IAAI,CAAC;YAC9C,IAAc,CAAA,cAAA,GAA4B,IAAI,CAAC;IASrD,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,eAAe,EAAE;IACxC,YAAA,MAAM,IAAI,KAAK,CACb,0EAA0E,CAC3E,CAAC;aACH;IAED,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;YAGlC,IAAI,CAAC,MAAM,GAAG;IACZ,YAAA,KAAK,EAAE,KAAK;IACZ,YAAA,OAAO,EAAE,IAAI;IACb,YAAA,GAAG,aAAa;aACjB,CAAC;;YAGF,IAAI,CAAC,KAAK,GAAG;IACX,YAAA,eAAe,EAAE,CAAC;IAClB,YAAA,iBAAiB,EAAE,CAAC;IACpB,YAAA,qBAAqB,EAAE,CAAC;IACxB,YAAA,kBAAkB,EAAE,CAAC;IACrB,YAAA,mBAAmB,EAAE,CAAC;aACvB,CAAC;YAEF,IAAI,CAAC,GAAG,CAAC,CAAA,EAAG,IAAI,CAAC,IAAI,CAAsB,oBAAA,CAAA,CAAC,CAAC;SAC9C;IAED;;IAEG;QACH,MAAM,UAAU,CAAC,UAA6B,EAAA;YAC5C,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC9C,MAAM,IAAI,KAAK,CAAC,CAAA,EAAG,IAAI,CAAC,IAAI,CAA2C,yCAAA,CAAA,CAAC,CAAC;aAC1E;IAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;IAChC,QAAA,IAAI,CAAC,GAAG,CACN,CAAA,aAAA,EAAgB,IAAI,CAAC,IAAI,CAAA,8BAAA,CAAgC,EACzD,UAAU,CAAC,KAAK,CACjB,CAAC;IAEF,QAAA,IAAI;;IAEF,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,yBAAyB,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;IACtE,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,yBAAyB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;;IAGlE,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC;oBACzC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;IACxC,aAAA,CAAC,CAAC;;gBAGH,IAAI,CAAC,SAAS,CAAC,QAAQ;IACpB,iBAAA,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;IACjC,iBAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC/B,iBAAA,KAAK,CAAC,CAAC,KAAK,KAAI;oBACf,IAAI,CAAC,GAAG,CAAC,CAAqB,kBAAA,EAAA,IAAI,CAAC,IAAI,CAAG,CAAA,CAAA,EAAE,KAAK,CAAC,CAAC;IACnD,gBAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACjC,aAAC,CAAC,CAAC;gBAEL,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC3C,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;gBAE1B,IAAI,CAAC,GAAG,CAAC,CAAA,EAAG,IAAI,CAAC,IAAI,CAAuC,qCAAA,CAAA,CAAC,CAAC;gBAC9D,OAAO,IAAI,CAAC,cAAc,CAAC;aAC5B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,GAAG,CAAC,CAAsB,mBAAA,EAAA,IAAI,CAAC,IAAI,CAAe,aAAA,CAAA,EAAE,KAAK,CAAC,CAAC;IAChE,YAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC/B,YAAA,MAAM,KAAK,CAAC;aACb;SACF;IAED;;IAEG;IACH,IAAA,MAAM,YAAY,CAChB,KAAiB,EACjB,UAAwD,EAAA;IAExD,QAAA,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;IAEpC,QAAA,IAAI;IACF,YAAA,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IAE7B,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;;IAE3C,gBAAA,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC1B,OAAO;iBACR;;gBAGD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;;IAG3D,YAAA,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;;IAGnC,YAAA,IAAI,cAAc,KAAK,KAAK,EAAE;oBAC5B,KAAK,CAAC,KAAK,EAAE,CAAC;iBACf;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,GAAG,CAAC,CAA6B,0BAAA,EAAA,IAAI,CAAC,IAAI,CAAG,CAAA,CAAA,EAAE,KAAK,CAAC,CAAC;IAC3D,YAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;;IAG/B,YAAA,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC3B;oBAAS;;gBAER,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;IACrD,YAAA,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,cAAc,CAAC;IAC/C,YAAA,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,cAAc,CAAC;gBACjD,IAAI,CAAC,KAAK,CAAC,qBAAqB;oBAC9B,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;aAC/D;SACF;IAOD;;IAEG;IACH,IAAA,YAAY,CAAC,SAAqC,EAAA;YAChD,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;IAC/C,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,gDAAgD,CAAC,CAAC;gBACnE,OAAO;aACR;YAED,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IACrC,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;YAE/C,IAAI,CAAC,GAAG,CAAC,CAAA,EAAG,IAAI,CAAC,IAAI,yBAAyB,EAAE;IAC9C,YAAA,GAAG,EAAE,SAAS;gBACd,GAAG,EAAE,IAAI,CAAC,MAAM;IACjB,SAAA,CAAC,CAAC;;IAGH,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;aAC7C;SACF;IAED;;IAEG;QACH,SAAS,GAAA;IACP,QAAA,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;SAC3B;IAED;;IAEG;QACH,MAAM,GAAA;IACJ,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,GAAG,CAAC,CAAA,EAAG,IAAI,CAAC,IAAI,CAAsB,oBAAA,CAAA,CAAC,CAAC;SAC9C;IAED;;IAEG;QACH,OAAO,GAAA;IACL,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACvB,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,CAAA,EAAG,IAAI,CAAC,IAAI,CAAuB,qBAAA,CAAA,CAAC,CAAC;SAC/C;IAED;;IAEG;QACH,QAAQ,GAAA;IACN,QAAA,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;SAC1B;IAED;;IAEG;QACH,UAAU,GAAA;YACR,IAAI,CAAC,KAAK,GAAG;IACX,YAAA,eAAe,EAAE,CAAC;IAClB,YAAA,iBAAiB,EAAE,CAAC;IACpB,YAAA,qBAAqB,EAAE,CAAC;IACxB,YAAA,kBAAkB,EAAE,CAAC;IACrB,YAAA,mBAAmB,EAAE,CAAC;aACvB,CAAC;YACF,IAAI,CAAC,GAAG,CAAC,CAAA,EAAG,IAAI,CAAC,IAAI,CAAc,YAAA,CAAA,CAAC,CAAC;SACtC;IAED;;IAEG;IACH,IAAA,MAAM,OAAO,GAAA;IACX,QAAA,IAAI;IACF,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;IAClB,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;iBACvB;IAED,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;IAClB,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;iBACvB;IAED,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;IACxB,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;iBAC7B;IAED,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;IACtB,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;iBAC3B;IAED,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;IACvB,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;iBAC5B;IAED,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;gBAC3B,IAAI,CAAC,GAAG,CAAC,CAAA,EAAG,IAAI,CAAC,IAAI,CAAyB,uBAAA,CAAA,CAAC,CAAC;aACjD;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,GAAG,CAAC,CAAqB,kBAAA,EAAA,IAAI,CAAC,IAAI,CAAe,aAAA,CAAA,EAAE,KAAK,CAAC,CAAC;aAChE;SACF;IAED;;IAEG;QACH,GAAG,CAAC,GAAG,IAAW,EAAA;IAChB,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;IACrB,YAAA,OAAO,CAAC,GAAG,CAAC,CAAA,CAAA,EAAI,IAAI,CAAC,IAAI,CAAA,aAAA,CAAe,EAAE,GAAG,IAAI,CAAC,CAAC;aACpD;SACF;IAED;;IAEG;QACH,WAAW,GAAA;YAQT,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,SAAS,EAAE,IAAI,CAAC,SAAS;IACzB,YAAA,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;IACxB,YAAA,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;aACvB,CAAC;SACH;IACF,CAAA;IAKD;IACA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;IAChC,IAAA,MAAc,CAAC,eAAe,GAAG,eAAe,CAAC;IACpD;;;;;;;;"}