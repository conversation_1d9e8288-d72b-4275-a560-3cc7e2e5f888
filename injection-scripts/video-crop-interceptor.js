var VideoCropInterceptor = (function () {
    'use strict';

    /**
     * Base Interceptor Interface
     *
     * Defines the standard interface that all video frame interceptors must implement.
     * Provides common functionality for configuration management, lifecycle, and processing.
     *
     * Features:
     * - Standardized interceptor interface
     * - Built-in configuration management
     * - Lifecycle management (initialize, process, cleanup)
     * - Error handling and fallback mechanisms
     * - Performance monitoring capabilities
     */
    class BaseInterceptor {
        constructor(name, defaultConfig = {}) {
            this.isInitialized = false;
            this.isEnabled = true;
            // Transform stream components
            this.processor = null;
            this.generator = null;
            this.transformStream = null;
            // Track references
            this.originalTrack = null;
            this.processedTrack = null;
            if (this.constructor === BaseInterceptor) {
                throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");
            }
            this.name = name;
            this.type = this.constructor.name;
            // Configuration management - stored internally
            this.config = {
                debug: false,
                enabled: true,
                ...defaultConfig,
            };
            // Performance tracking
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} interceptor created`);
        }
        /**
         * Initialize the interceptor with a video track
         */
        async initialize(videoTrack) {
            if (!videoTrack || videoTrack.kind !== "video") {
                throw new Error(`${this.name} interceptor requires a valid video track`);
            }
            this.originalTrack = videoTrack;
            this.log(`Initializing ${this.name} interceptor with video track:`, videoTrack.label);
            try {
                // Create processor and generator
                this.processor = new MediaStreamTrackProcessor({ track: videoTrack });
                this.generator = new MediaStreamTrackGenerator({ kind: "video" });
                // Create transform stream with bound processing function
                this.transformStream = new TransformStream({
                    transform: this.processFrame.bind(this),
                });
                // Connect the pipeline
                this.processor.readable
                    .pipeThrough(this.transformStream)
                    .pipeTo(this.generator.writable)
                    .catch((error) => {
                    this.log(`Pipeline error in ${this.name}:`, error);
                    this.stats.errorsEncountered++;
                });
                this.processedTrack = this.generator.track;
                this.isInitialized = true;
                this.log(`${this.name} interceptor initialized successfully`);
                return this.processedTrack;
            }
            catch (error) {
                this.log(`Error initializing ${this.name} interceptor:`, error);
                this.stats.errorsEncountered++;
                throw error;
            }
        }
        /**
         * Process a single video frame - handles common logic and delegates to subclass
         */
        async processFrame(frame, controller) {
            const startTime = performance.now();
            try {
                this.stats.framesProcessed++;
                if (!this.isEnabled || !this.config.enabled) {
                    // Pass through original frame if disabled
                    controller.enqueue(frame);
                    return;
                }
                // Call the specific interceptor's processing logic
                const processedFrame = await this.processVideoFrame(frame);
                // Enqueue the processed frame
                controller.enqueue(processedFrame);
                // Clean up original frame if a new one was created
                if (processedFrame !== frame) {
                    frame.close();
                }
            }
            catch (error) {
                this.log(`Error processing frame in ${this.name}:`, error);
                this.stats.errorsEncountered++;
                // Fallback: pass through original frame
                controller.enqueue(frame);
            }
            finally {
                // Update performance stats
                const processingTime = performance.now() - startTime;
                this.stats.lastProcessingTime = processingTime;
                this.stats.totalProcessingTime += processingTime;
                this.stats.averageProcessingTime =
                    this.stats.totalProcessingTime / this.stats.framesProcessed;
            }
        }
        /**
         * Update interceptor configuration
         */
        updateConfig(newConfig) {
            if (!newConfig || typeof newConfig !== "object") {
                this.log("warn", "Invalid configuration provided to updateConfig");
                return;
            }
            const oldConfig = { ...this.config };
            this.config = { ...this.config, ...newConfig };
            this.log(`${this.name} configuration updated:`, {
                old: oldConfig,
                new: this.config,
            });
            // Call configuration change handler if implemented
            if (this.onConfigChange) {
                this.onConfigChange(oldConfig, this.config);
            }
        }
        /**
         * Get current configuration
         */
        getConfig() {
            return { ...this.config };
        }
        /**
         * Enable the interceptor
         */
        enable() {
            this.isEnabled = true;
            this.config.enabled = true;
            this.log(`${this.name} interceptor enabled`);
        }
        /**
         * Disable the interceptor
         */
        disable() {
            this.isEnabled = false;
            this.config.enabled = false;
            this.log(`${this.name} interceptor disabled`);
        }
        /**
         * Get performance statistics
         */
        getStats() {
            return { ...this.stats };
        }
        /**
         * Reset performance statistics
         */
        resetStats() {
            this.stats = {
                framesProcessed: 0,
                errorsEncountered: 0,
                averageProcessingTime: 0,
                lastProcessingTime: 0,
                totalProcessingTime: 0,
            };
            this.log(`${this.name} stats reset`);
        }
        /**
         * Cleanup resources
         */
        async cleanup() {
            try {
                if (this.processor) {
                    this.processor = null;
                }
                if (this.generator) {
                    this.generator = null;
                }
                if (this.transformStream) {
                    this.transformStream = null;
                }
                if (this.originalTrack) {
                    this.originalTrack = null;
                }
                if (this.processedTrack) {
                    this.processedTrack = null;
                }
                this.isInitialized = false;
                this.log(`${this.name} interceptor cleaned up`);
            }
            catch (error) {
                this.log(`Error cleaning up ${this.name} interceptor:`, error);
            }
        }
        /**
         * Logging utility
         */
        log(...args) {
            if (this.config.debug) {
                console.log(`[${this.name}-Interceptor]`, ...args);
            }
        }
        /**
         * Get interceptor metadata
         */
        getMetadata() {
            return {
                name: this.name,
                type: this.type,
                isInitialized: this.isInitialized,
                isEnabled: this.isEnabled,
                config: this.getConfig(),
                stats: this.getStats(),
            };
        }
    }
    // Make available globally for injection scripts
    if (typeof window !== "undefined") {
        window.BaseInterceptor = BaseInterceptor;
    }

    /**
     * Video Frame Interceptor
     *
     * A modular interceptor that sits between video track capture and WebRTC peer connection.
     * Provides frame cropping and subscription capabilities for real-time frame analysis.
     *
     * Features:
     * - Frame cropping with configurable crop regions
     * - Event-driven subscription API for frame listeners
     * - Toggle-able interceptor functionality
     * - Maintains existing streaming functionality
     * - Support for multiple frame subscribers
     * - Extends BaseInterceptor for standardized interface
     */
    class VideoFrameInterceptor extends BaseInterceptor {
        constructor(name = "video-crop", options = {}) {
            // Default configuration for video cropping interceptor
            const defaultConfig = {
                debug: false,
                frameRate: 30,
                enableCropping: true,
                ...options,
            };
            super(name, defaultConfig);
            // Video-specific state management
            this.subscribers = new Map();
            this.frameCount = 0;
            this.lastFrameTime = 0;
            this.log("VideoFrameInterceptor initialized", this.config);
        }
        /**
         * Process a video frame - implements BaseInterceptor interface
         */
        async processVideoFrame(frame) {
            this.frameCount++;
            const currentTime = performance.now();
            // Throttle debug logging
            if (this.config.debug && currentTime - this.lastFrameTime > 1000) {
                this.log(`Processed ${this.frameCount} frames`);
                this.lastFrameTime = currentTime;
            }
            let processedFrame = frame;
            // Apply cropping if enabled and crop region is set
            if (this.config.enableCropping &&
                (this.config.cropRegion || this.config.defaultCropRegion)) {
                processedFrame = this.applyCropping(frame);
            }
            // Notify subscribers with the processed frame (before returning)
            if (this.subscribers.size > 0) {
                await this.notifySubscribers(processedFrame);
            }
            return processedFrame;
        }
        /**
         * Apply cropping to a video frame
         */
        applyCropping(frame) {
            const { codedWidth, codedHeight } = frame;
            const cropRegion = this.config.cropRegion || this.config.defaultCropRegion;
            if (!cropRegion) {
                return frame;
            }
            // Ensure crop region is within frame bounds and aligned for YUV 4:2:0
            const safeCropRegion = {
                x: this.makeEven(Math.max(0, Math.min(cropRegion.x, codedWidth))),
                y: this.makeEven(Math.max(0, Math.min(cropRegion.y, codedHeight))),
                width: this.makeEven(Math.max(2, Math.min(cropRegion.width, codedWidth - cropRegion.x))),
                height: this.makeEven(Math.max(2, Math.min(cropRegion.height, codedHeight - cropRegion.y))),
            };
            try {
                const croppedFrame = new VideoFrame(frame, {
                    visibleRect: safeCropRegion,
                    displayWidth: safeCropRegion.width,
                    displayHeight: safeCropRegion.height,
                    timestamp: frame.timestamp,
                    duration: frame.duration ?? undefined,
                });
                this.log("Frame cropped:", safeCropRegion);
                return croppedFrame;
            }
            catch (error) {
                this.log("Cropping failed, using original frame:", error);
                return frame;
            }
        }
        /**
         * Notify all subscribers with the processed frame
         */
        async notifySubscribers(frame) {
            const promises = [];
            for (const [subscriberId, callback] of this.subscribers) {
                try {
                    // Clone frame for each subscriber to prevent conflicts
                    const frameClone = new VideoFrame(frame, {
                        timestamp: frame.timestamp,
                        duration: frame.duration ?? undefined,
                    });
                    const metadata = {
                        subscriberId,
                        frameCount: this.frameCount,
                        timestamp: performance.now(),
                        cropRegion: this.config.cropRegion || this.config.defaultCropRegion || null,
                    };
                    const result = callback(frameClone, metadata);
                    // Handle async callbacks
                    if (result instanceof Promise) {
                        promises.push(result);
                    }
                }
                catch (error) {
                    this.log(`Error notifying subscriber ${subscriberId}:`, error);
                }
            }
            // Wait for all async callbacks to complete
            if (promises.length > 0) {
                await Promise.allSettled(promises);
            }
        }
        /**
         * Subscribe to processed frames
         */
        subscribe(subscriberId, callback) {
            if (typeof callback !== "function") {
                throw new Error("Callback must be a function");
            }
            this.subscribers.set(subscriberId, callback);
            this.log(`Subscriber added: ${subscriberId} (total: ${this.subscribers.size})`);
            // Return unsubscribe function
            return () => this.unsubscribe(subscriberId);
        }
        /**
         * Unsubscribe from processed frames
         */
        unsubscribe(subscriberId) {
            const removed = this.subscribers.delete(subscriberId);
            if (removed) {
                this.log(`Subscriber removed: ${subscriberId} (remaining: ${this.subscribers.size})`);
            }
            return removed;
        }
        /**
         * Update crop region
         */
        setCropRegion(cropRegion) {
            if (cropRegion && typeof cropRegion === "object") {
                this.updateConfig({
                    cropRegion: {
                        x: this.makeEven(cropRegion.x || 0),
                        y: this.makeEven(cropRegion.y || 0),
                        width: this.makeEven(cropRegion.width || 100),
                        height: this.makeEven(cropRegion.height || 100),
                    },
                });
                this.log("Crop region updated:", this.config.cropRegion);
            }
            else {
                this.updateConfig({ cropRegion: null });
                this.log("Crop region cleared");
            }
        }
        /**
         * Enable or disable cropping functionality
         */
        setCroppingEnabled(enabled) {
            this.updateConfig({
                enableCropping: !!enabled,
            });
            this.log(`Cropping ${this.config.enableCropping ? "enabled" : "disabled"}`);
        }
        /**
         * Get current interceptor status
         */
        getStatus() {
            return {
                ...this.getMetadata(),
                enableCropping: this.config.enableCropping || false,
                cropRegion: this.config.cropRegion || this.config.defaultCropRegion || null,
                subscriberCount: this.subscribers.size,
                frameCount: this.frameCount,
                hasOriginalTrack: false, // These would need to be tracked in base class
                hasProcessedTrack: false,
            };
        }
        /**
         * Override cleanup to handle video-specific resources
         */
        async cleanup() {
            this.log("Cleaning up video interceptor...");
            // Clear subscribers
            this.subscribers.clear();
            // Call parent cleanup
            await super.cleanup();
            this.log("Video interceptor cleanup complete");
        }
        /**
         * Utility function to ensure even numbers for YUV 4:2:0 alignment
         */
        makeEven(value) {
            return Math.floor(value / 2) * 2;
        }
    }
    // Make available globally for injection scripts
    if (typeof window !== "undefined") {
        window.VideoFrameInterceptor = VideoFrameInterceptor;
    }

    return VideoFrameInterceptor;

})();
//# sourceMappingURL=video-crop-interceptor.js.map
