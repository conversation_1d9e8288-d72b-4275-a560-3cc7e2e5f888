/**
 * Video Frame Interceptor
 *
 * A modular interceptor that sits between video track capture and WebRTC peer connection.
 * Provides frame cropping and subscription capabilities for real-time frame analysis.
 *
 * Features:
 * - Frame cropping with configurable crop regions
 * - Event-driven subscription API for frame listeners
 * - Toggle-able interceptor functionality
 * - Maintains existing streaming functionality
 * - Support for multiple frame subscribers
 * - Extends BaseInterceptor for standardized interface
 */

class VideoFrameInterceptor extends BaseInterceptor {
  constructor(name = "video-crop", options = {}) {
    // Default configuration for video cropping interceptor
    const defaultConfig = {
      debug: false,
      defaultCropRegion: null,
      frameRate: 30,
      enableCropping: true,
      ...options,
    };

    super(name, defaultConfig);

    // Video-specific state management
    this.subscribers = new Map(); // subscriberId -> callback
    this.frameCount = 0;
    this.lastFrameTime = 0;

    this.log("VideoFrameInterceptor initialized", this.config);
  }

  /**
   * Process a video frame - implements BaseInterceptor interface
   * @param {VideoFrame} frame - Input video frame
   * @returns {VideoFrame} - Processed video frame
   */
  async processVideoFrame(frame) {
    this.frameCount++;
    const currentTime = performance.now();

    // Throttle debug logging
    if (this.config.debug && currentTime - this.lastFrameTime > 1000) {
      this.log(`Processed ${this.frameCount} frames`);
      this.lastFrameTime = currentTime;
    }

    let processedFrame = frame;

    // Apply cropping if enabled and crop region is set
    if (
      this.config.enableCropping &&
      (this.config.cropRegion || this.config.defaultCropRegion)
    ) {
      processedFrame = this.applyCropping(frame);
    }

    // Notify subscribers with the processed frame (before returning)
    if (this.subscribers.size > 0) {
      await this.notifySubscribers(processedFrame);
    }

    return processedFrame;
  }

  /**
   * Apply cropping to a video frame
   * @param {VideoFrame} frame - Input frame
   * @returns {VideoFrame} - Cropped frame
   */
  applyCropping(frame) {
    const { codedWidth, codedHeight } = frame;
    const cropRegion = this.config.cropRegion || this.config.defaultCropRegion;

    // Ensure crop region is within frame bounds and aligned for YUV 4:2:0
    const safeCropRegion = {
      x: this.makeEven(Math.max(0, Math.min(cropRegion.x, codedWidth))),
      y: this.makeEven(Math.max(0, Math.min(cropRegion.y, codedHeight))),
      width: this.makeEven(
        Math.max(2, Math.min(cropRegion.width, codedWidth - cropRegion.x))
      ),
      height: this.makeEven(
        Math.max(2, Math.min(cropRegion.height, codedHeight - cropRegion.y))
      ),
    };

    try {
      const croppedFrame = new VideoFrame(frame, {
        visibleRect: safeCropRegion,
        displayWidth: safeCropRegion.width,
        displayHeight: safeCropRegion.height,
        timestamp: frame.timestamp,
        duration: frame.duration,
      });

      this.log("Frame cropped:", safeCropRegion);
      return croppedFrame;
    } catch (error) {
      this.log("Cropping failed, using original frame:", error);
      return frame;
    }
  }

  /**
   * Notify all subscribers with the processed frame
   * @param {VideoFrame} frame - Processed frame to send to subscribers
   */
  async notifySubscribers(frame) {
    const promises = [];

    for (const [subscriberId, callback] of this.subscribers) {
      try {
        // Clone frame for each subscriber to prevent conflicts
        const frameClone = new VideoFrame(frame, {
          timestamp: frame.timestamp,
          duration: frame.duration,
        });

        const result = callback(frameClone, {
          subscriberId,
          frameCount: this.frameCount,
          timestamp: performance.now(),
          cropRegion: this.config.cropRegion || this.config.defaultCropRegion,
        });

        // Handle async callbacks
        if (result instanceof Promise) {
          promises.push(result);
        }
      } catch (error) {
        this.log(`Error notifying subscriber ${subscriberId}:`, error);
      }
    }

    // Wait for all async callbacks to complete
    if (promises.length > 0) {
      await Promise.allSettled(promises);
    }
  }

  /**
   * Subscribe to processed frames
   * @param {string} subscriberId - Unique identifier for the subscriber
   * @param {Function} callback - Callback function to receive frames
   * @returns {Function} - Unsubscribe function
   */
  subscribe(subscriberId, callback) {
    if (typeof callback !== "function") {
      throw new Error("Callback must be a function");
    }

    this.subscribers.set(subscriberId, callback);
    this.log(
      `Subscriber added: ${subscriberId} (total: ${this.subscribers.size})`
    );

    // Return unsubscribe function
    return () => this.unsubscribe(subscriberId);
  }

  /**
   * Unsubscribe from processed frames
   * @param {string} subscriberId - Subscriber ID to remove
   */
  unsubscribe(subscriberId) {
    const removed = this.subscribers.delete(subscriberId);
    if (removed) {
      this.log(
        `Subscriber removed: ${subscriberId} (remaining: ${this.subscribers.size})`
      );
    }
    return removed;
  }

  /**
   * Update crop region
   * @param {Object} cropRegion - New crop region {x, y, width, height}
   */
  setCropRegion(cropRegion) {
    if (cropRegion && typeof cropRegion === "object") {
      this.updateConfig({
        cropRegion: {
          x: this.makeEven(cropRegion.x || 0),
          y: this.makeEven(cropRegion.y || 0),
          width: this.makeEven(cropRegion.width || 100),
          height: this.makeEven(cropRegion.height || 100),
        },
      });
      this.log("Crop region updated:", this.config.cropRegion);
    } else {
      this.updateConfig({ cropRegion: null });
      this.log("Crop region cleared");
    }
  }

  /**
   * Enable or disable cropping functionality
   * @param {boolean} enabled - Whether to enable cropping
   */
  setCroppingEnabled(enabled) {
    this.updateConfig({
      enableCropping: !!enabled,
    });
    this.log(`Cropping ${this.config.enableCropping ? "enabled" : "disabled"}`);
  }

  /**
   * Get current interceptor status
   * @returns {Object} - Status information
   */
  getStatus() {
    return {
      ...this.getMetadata(),
      enableCropping: this.config.enableCropping,
      cropRegion: this.config.cropRegion || this.config.defaultCropRegion,
      subscriberCount: this.subscribers.size,
      frameCount: this.frameCount,
      hasOriginalTrack: !!this.originalTrack,
      hasProcessedTrack: !!this.processedTrack,
    };
  }

  /**
   * Override cleanup to handle video-specific resources
   */
  cleanup() {
    this.log("Cleaning up video interceptor...");

    // Clear subscribers
    this.subscribers.clear();

    // Call parent cleanup
    super.cleanup();

    this.log("Video interceptor cleanup complete");
  }

  /**
   * Utility function to ensure even numbers for YUV 4:2:0 alignment
   * @param {number} value - Input value
   * @returns {number} - Even value
   */
  makeEven(value) {
    return Math.floor(value / 2) * 2;
  }
}

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = VideoFrameInterceptor;
} else if (typeof window !== "undefined") {
  window.VideoFrameInterceptor = VideoFrameInterceptor;
}
