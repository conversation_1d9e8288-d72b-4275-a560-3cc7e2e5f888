var VideoCropInterceptor=function(){"use strict";class e{constructor(t,i={}){if(this.isInitialized=!1,this.isEnabled=!0,this.processor=null,this.generator=null,this.transformStream=null,this.originalTrack=null,this.processedTrack=null,this.constructor===e)throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");this.name=t,this.type=this.constructor.name,this.config={debug:!1,enabled:!0,...i},this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} interceptor created`)}async initialize(e){if(!e||"video"!==e.kind)throw new Error(`${this.name} interceptor requires a valid video track`);this.originalTrack=e,this.log(`Initializing ${this.name} interceptor with video track:`,e.label);try{return this.processor=new MediaStreamTrackProcessor({track:e}),this.generator=new MediaStreamTrackGenerator({kind:"video"}),this.transformStream=new TransformStream({transform:this.processFrame.bind(this)}),this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(e=>{this.log(`Pipeline error in ${this.name}:`,e),this.stats.errorsEncountered++}),this.processedTrack=this.generator.track,this.isInitialized=!0,this.log(`${this.name} interceptor initialized successfully`),this.processedTrack}catch(e){throw this.log(`Error initializing ${this.name} interceptor:`,e),this.stats.errorsEncountered++,e}}async processFrame(e,t){const i=performance.now();try{if(this.stats.framesProcessed++,!this.isEnabled||!this.config.enabled)return void t.enqueue(e);const i=await this.processVideoFrame(e);t.enqueue(i),i!==e&&e.close()}catch(i){this.log(`Error processing frame in ${this.name}:`,i),this.stats.errorsEncountered++,t.enqueue(e)}finally{const e=performance.now()-i;this.stats.lastProcessingTime=e,this.stats.totalProcessingTime+=e,this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}}updateConfig(e){if(!e||"object"!=typeof e)return void this.log("warn","Invalid configuration provided to updateConfig");const t={...this.config};this.config={...this.config,...e},this.log(`${this.name} configuration updated:`,{old:t,new:this.config}),this.onConfigChange&&this.onConfigChange(t,this.config)}getConfig(){return{...this.config}}enable(){this.isEnabled=!0,this.config.enabled=!0,this.log(`${this.name} interceptor enabled`)}disable(){this.isEnabled=!1,this.config.enabled=!1,this.log(`${this.name} interceptor disabled`)}getStats(){return{...this.stats}}resetStats(){this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} stats reset`)}async cleanup(){try{this.processor&&(this.processor=null),this.generator&&(this.generator=null),this.transformStream&&(this.transformStream=null),this.originalTrack&&(this.originalTrack=null),this.processedTrack&&(this.processedTrack=null),this.isInitialized=!1,this.log(`${this.name} interceptor cleaned up`)}catch(e){this.log(`Error cleaning up ${this.name} interceptor:`,e)}}log(...e){this.config.debug&&console.log(`[${this.name}-Interceptor]`,...e)}getMetadata(){return{name:this.name,type:this.type,isInitialized:this.isInitialized,isEnabled:this.isEnabled,config:this.getConfig(),stats:this.getStats()}}}"undefined"!=typeof window&&(window.BaseInterceptor=e);class t extends e{constructor(e="video-crop",t={}){super(e,{debug:!1,frameRate:30,enableCropping:!0,...t}),this.subscribers=new Map,this.frameCount=0,this.lastFrameTime=0,this.log("VideoFrameInterceptor initialized",this.config)}async processVideoFrame(e){this.frameCount++;const t=performance.now();this.config.debug&&t-this.lastFrameTime>1e3&&(this.log(`Processed ${this.frameCount} frames`),this.lastFrameTime=t);let i=e;return this.config.enableCropping&&(this.config.cropRegion||this.config.defaultCropRegion)&&(i=this.applyCropping(e)),this.subscribers.size>0&&await this.notifySubscribers(i),i}applyCropping(e){const{codedWidth:t,codedHeight:i}=e,s=this.config.cropRegion||this.config.defaultCropRegion;if(!s)return e;const r={x:this.makeEven(Math.max(0,Math.min(s.x,t))),y:this.makeEven(Math.max(0,Math.min(s.y,i))),width:this.makeEven(Math.max(2,Math.min(s.width,t-s.x))),height:this.makeEven(Math.max(2,Math.min(s.height,i-s.y)))};try{const t=new VideoFrame(e,{visibleRect:r,displayWidth:r.width,displayHeight:r.height,timestamp:e.timestamp,duration:e.duration??void 0});return this.log("Frame cropped:",r),t}catch(t){return this.log("Cropping failed, using original frame:",t),e}}async notifySubscribers(e){const t=[];for(const[i,s]of this.subscribers)try{const r=new VideoFrame(e,{timestamp:e.timestamp,duration:e.duration??void 0}),n=s(r,{subscriberId:i,frameCount:this.frameCount,timestamp:performance.now(),cropRegion:this.config.cropRegion||this.config.defaultCropRegion||null});n instanceof Promise&&t.push(n)}catch(e){this.log(`Error notifying subscriber ${i}:`,e)}t.length>0&&await Promise.allSettled(t)}subscribe(e,t){if("function"!=typeof t)throw new Error("Callback must be a function");return this.subscribers.set(e,t),this.log(`Subscriber added: ${e} (total: ${this.subscribers.size})`),()=>this.unsubscribe(e)}unsubscribe(e){const t=this.subscribers.delete(e);return t&&this.log(`Subscriber removed: ${e} (remaining: ${this.subscribers.size})`),t}setCropRegion(e){e&&"object"==typeof e?(this.updateConfig({cropRegion:{x:this.makeEven(e.x||0),y:this.makeEven(e.y||0),width:this.makeEven(e.width||100),height:this.makeEven(e.height||100)}}),this.log("Crop region updated:",this.config.cropRegion)):(this.updateConfig({cropRegion:null}),this.log("Crop region cleared"))}setCroppingEnabled(e){this.updateConfig({enableCropping:!!e}),this.log("Cropping "+(this.config.enableCropping?"enabled":"disabled"))}getStatus(){return{...this.getMetadata(),enableCropping:this.config.enableCropping||!1,cropRegion:this.config.cropRegion||this.config.defaultCropRegion||null,subscriberCount:this.subscribers.size,frameCount:this.frameCount,hasOriginalTrack:!1,hasProcessedTrack:!1}}async cleanup(){this.log("Cleaning up video interceptor..."),this.subscribers.clear(),await super.cleanup(),this.log("Video interceptor cleanup complete")}makeEven(e){return 2*Math.floor(e/2)}}return"undefined"!=typeof window&&(window.VideoFrameInterceptor=t),t}();
//# sourceMappingURL=video-crop-interceptor.min.js.map
