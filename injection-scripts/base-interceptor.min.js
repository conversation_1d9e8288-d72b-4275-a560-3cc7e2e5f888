var BaseInterceptor=function(){"use strict";class t{constructor(e,s={}){if(this.isInitialized=!1,this.isEnabled=!0,this.processor=null,this.generator=null,this.transformStream=null,this.originalTrack=null,this.processedTrack=null,this.constructor===t)throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");this.name=e,this.type=this.constructor.name,this.config={debug:!1,enabled:!0,...s},this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} interceptor created`)}async initialize(t){if(!t||"video"!==t.kind)throw new Error(`${this.name} interceptor requires a valid video track`);this.originalTrack=t,this.log(`Initializing ${this.name} interceptor with video track:`,t.label);try{return this.processor=new MediaStreamTrackProcessor({track:t}),this.generator=new MediaStreamTrackGenerator({kind:"video"}),this.transformStream=new TransformStream({transform:this.processFrame.bind(this)}),this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(t=>{this.log(`Pipeline error in ${this.name}:`,t),this.stats.errorsEncountered++}),this.processedTrack=this.generator.track,this.isInitialized=!0,this.log(`${this.name} interceptor initialized successfully`),this.processedTrack}catch(t){throw this.log(`Error initializing ${this.name} interceptor:`,t),this.stats.errorsEncountered++,t}}async processFrame(t,e){const s=performance.now();try{if(this.stats.framesProcessed++,!this.isEnabled||!this.config.enabled)return void e.enqueue(t);const s=await this.processVideoFrame(t);e.enqueue(s),s!==t&&t.close()}catch(s){this.log(`Error processing frame in ${this.name}:`,s),this.stats.errorsEncountered++,e.enqueue(t)}finally{const t=performance.now()-s;this.stats.lastProcessingTime=t,this.stats.totalProcessingTime+=t,this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}}updateConfig(t){if(!t||"object"!=typeof t)return void this.log("warn","Invalid configuration provided to updateConfig");const e={...this.config};this.config={...this.config,...t},this.log(`${this.name} configuration updated:`,{old:e,new:this.config}),this.onConfigChange&&this.onConfigChange(e,this.config)}getConfig(){return{...this.config}}enable(){this.isEnabled=!0,this.config.enabled=!0,this.log(`${this.name} interceptor enabled`)}disable(){this.isEnabled=!1,this.config.enabled=!1,this.log(`${this.name} interceptor disabled`)}getStats(){return{...this.stats}}resetStats(){this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} stats reset`)}async cleanup(){try{this.processor&&(this.processor=null),this.generator&&(this.generator=null),this.transformStream&&(this.transformStream=null),this.originalTrack&&(this.originalTrack=null),this.processedTrack&&(this.processedTrack=null),this.isInitialized=!1,this.log(`${this.name} interceptor cleaned up`)}catch(t){this.log(`Error cleaning up ${this.name} interceptor:`,t)}}log(...t){this.config.debug&&console.log(`[${this.name}-Interceptor]`,...t)}getMetadata(){return{name:this.name,type:this.type,isInitialized:this.isInitialized,isEnabled:this.isEnabled,config:this.getConfig(),stats:this.getStats()}}}return"undefined"!=typeof window&&(window.BaseInterceptor=t),t}();
//# sourceMappingURL=base-interceptor.min.js.map
