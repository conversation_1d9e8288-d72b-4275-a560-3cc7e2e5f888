{"version": 3, "file": "interceptor-pipeline.js", "sources": ["../src/interceptor-pipeline.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;IAAA;;;;;;;;;;;;IAYG;IAoBH,MAAM,mBAAmB,CAAA;IAgBvB,IAAA,WAAA,CAAY,eAA2C,EAAE,EAAA;YAdlD,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;YAC/B,IAAS,CAAA,SAAA,GAAY,IAAI,CAAC;;YAIzB,IAAa,CAAA,aAAA,GAA4B,IAAI,CAAC;YAC9C,IAAc,CAAA,cAAA,GAA4B,IAAI,CAAC;;YAG/C,IAAS,CAAA,SAAA,GAAqC,IAAI,CAAC;YACnD,IAAS,CAAA,SAAA,GAAqC,IAAI,CAAC;YACnD,IAAe,CAAA,eAAA,GACrB,IAAI,CAAC;IAGL,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;;YAGtC,IAAI,CAAC,KAAK,GAAG;IACX,YAAA,eAAe,EAAE,CAAC;IAClB,YAAA,iBAAiB,EAAE,CAAC;IACpB,YAAA,qBAAqB,EAAE,CAAC;IACxB,YAAA,kBAAkB,EAAE,CAAC;IACrB,YAAA,mBAAmB,EAAE,CAAC;gBACtB,gBAAgB,EAAE,IAAI,GAAG,EAAE;aAC5B,CAAC;IAEF,QAAA,IAAI,CAAC,GAAG,CACN,kCAAkC,EAClC,IAAI,CAAC,YAAY,CAAC,MAAM,EACxB,cAAc,CACf,CAAC;SACH;IAED;;IAEG;QACH,MAAM,UAAU,CAAC,UAA4B,EAAA;YAC3C,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,EAAE;IAC9C,YAAA,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;aACrE;IAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;YAChC,IAAI,CAAC,GAAG,CAAC,yCAAyC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;IAEtE,QAAA,IAAI;;IAEF,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,yBAAyB,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;IACtE,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,yBAAyB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;;IAGlE,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC;oBACzC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;IACxC,aAAA,CAAC,CAAC;;gBAGH,IAAI,CAAC,SAAS,CAAC,QAAQ;IACpB,iBAAA,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;IACjC,iBAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC/B,iBAAA,KAAK,CAAC,CAAC,KAAK,KAAI;IACf,gBAAA,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;IACnC,gBAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACjC,aAAC,CAAC,CAAC;gBAEL,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC3C,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAE1B,YAAA,IAAI,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;gBAC9C,OAAO,IAAI,CAAC,cAAc,CAAC;aAC5B;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,IAAI,CAAC,GAAG,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;IAChD,YAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC/B,YAAA,MAAM,KAAK,CAAC;aACb;SACF;IAED;;IAEG;IACK,IAAA,MAAM,YAAY,CACxB,KAAiB,EACjB,UAAwD,EAAA;IAExD,QAAA,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;IAEpC,QAAA,IAAI;IACF,YAAA,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IAE7B,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;;IAErD,gBAAA,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC1B,OAAO;iBACR;IAED,YAAA,IAAI,KAAK,IAAI,IAAI,EAAE;;oBAEjB,OAAO;iBACR;gBAED,IAAI,YAAY,GAAG,KAAK,CAAC;gBACzB,MAAM,eAAe,GAAiB,EAAE,CAAC;;IAGzC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACjD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBACzC,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE;IAC/C,oBAAA,SAAS;qBACV;oBAED,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;IACvE,gBAAA,IAAI;IACF,oBAAA,MAAM,oBAAoB,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;;wBAG/C,MAAM,cAAc,GAAG,MAAM,WAAW,CAAC,iBAAiB,CACxD,YAAY,CACb,CAAC;;wBAGF,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,oBAAoB,CAAC;wBACjE,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;;wBAG/D,IAAI,cAAc,KAAK,YAAY,IAAI,YAAY,KAAK,KAAK,EAAE;IAC7D,wBAAA,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;yBACpC;wBAED,YAAY,GAAG,cAA4B,CAAC;qBAC7C;oBAAC,OAAO,KAAK,EAAE;wBACd,IAAI,CAAC,GAAG,CAAC,CAAwB,qBAAA,EAAA,WAAW,CAAC,IAAI,CAAG,CAAA,CAAA,EAAE,KAAK,CAAC,CAAC;IAC7D,oBAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;;;qBAIhC;iBACF;;IAGD,YAAA,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;;IAGjC,YAAA,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE;IAC5C,gBAAA,IAAI;wBACF,cAAc,CAAC,KAAK,EAAE,CAAC;qBACxB;oBAAC,OAAO,YAAY,EAAE;IACrB,oBAAA,IAAI,CAAC,GAAG,CAAC,uCAAuC,EAAE,YAAY,CAAC,CAAC;qBACjE;iBACF;;IAGD,YAAA,IAAI,YAAY,KAAK,KAAK,EAAE;oBAC1B,KAAK,CAAC,KAAK,EAAE,CAAC;iBACf;aACF;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,IAAI,CAAC,GAAG,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;IAC5D,YAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;;IAG/B,YAAA,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC3B;oBAAS;;gBAER,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;IACrD,YAAA,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,cAAc,CAAC;IAC/C,YAAA,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,cAAc,CAAC;gBACjD,IAAI,CAAC,KAAK,CAAC,qBAAqB;oBAC9B,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;aAC/D;SACF;IAED;;IAEG;QACK,sBAAsB,CAC5B,eAAuB,EACvB,cAAsB,EAAA;IAEtB,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;gBACrD,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,eAAe,EAAE;IAC/C,gBAAA,eAAe,EAAE,CAAC;IAClB,gBAAA,mBAAmB,EAAE,CAAC;IACtB,gBAAA,qBAAqB,EAAE,CAAC;IACzB,aAAA,CAAC,CAAC;aACJ;IAED,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,eAAe,CAAE,CAAC;YAChE,KAAK,CAAC,eAAe,EAAE,CAAC;IACxB,QAAA,KAAK,CAAC,mBAAmB,IAAI,cAAc,CAAC;IAC5C,QAAA,KAAK,CAAC,qBAAqB;IACzB,YAAA,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC,eAAe,CAAC;SACrD;IAED;;IAEG;IACH,IAAA,cAAc,CACZ,WAAqC,EACrC,KAAA,GAAgB,CAAC,CAAC,EAAA;YAElB,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,CAAC,iBAAiB,KAAK,UAAU,EAAE;IACvE,YAAA,MAAM,IAAI,KAAK,CACb,8DAA8D,CAC/D,CAAC;aACH;IAED,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;IAClD,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACpC,IAAI,CAAC,GAAG,CAAC,CAAA,kBAAA,EAAqB,WAAW,CAAC,IAAI,CAAqB,mBAAA,CAAA,CAAC,CAAC;aACtE;iBAAM;gBACL,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;gBAChD,IAAI,CAAC,GAAG,CAAC,CAAqB,kBAAA,EAAA,WAAW,CAAC,IAAI,CAAa,UAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;aACrE;SACF;IAED;;IAEG;IACH,IAAA,iBAAiB,CAAC,IAAY,EAAA;IAC5B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IAElE,QAAA,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;IAClD,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,CAAA,oBAAA,EAAuB,OAAO,CAAC,IAAI,CAAgB,cAAA,CAAA,CAAC,CAAC;IAC9D,YAAA,OAAO,IAAI,CAAC;aACb;IAED,QAAA,IAAI,CAAC,GAAG,CAAC,0BAA0B,IAAI,CAAA,CAAE,CAAC,CAAC;IAC3C,QAAA,OAAO,KAAK,CAAC;SACd;IAED;;IAEG;IACH,IAAA,cAAc,CAAC,UAA2B,EAAA;IACxC,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;IAEf,QAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;IAClC,YAAA,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC;aACnE;IAAM,aAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;gBACzC,KAAK,GAAG,UAAU,CAAC;aACpB;IAED,QAAA,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;IAClD,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;aACjC;IAED,QAAA,OAAO,IAAI,CAAC;SACb;IAED;;IAEG;QACH,mBAAmB,GAAA;IACjB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;SAC7C;IAED;;IAEG;QACH,MAAM,GAAA;IACJ,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,QAAA,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;SAC9B;IAED;;IAEG;QACH,OAAO,GAAA;IACL,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACvB,QAAA,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;SAC/B;IAED;;IAEG;QACH,uBAAuB,CACrB,IAAY,EACZ,MAAkC,EAAA;YAElC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,WAAW,EAAE;IACf,YAAA,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACjC,YAAA,OAAO,IAAI,CAAC;aACb;IACD,QAAA,OAAO,KAAK,CAAC;SACd;IAED;;IAEG;QACH,QAAQ,GAAA;YAIN,OAAO;gBACL,GAAG,IAAI,CAAC,KAAK;gBACb,gBAAgB,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACtD,YAAA,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;IAC1C,YAAA,uBAAuB,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;qBAClE,MAAM;aACV,CAAC;SACH;IAED;;IAEG;QACH,UAAU,GAAA;YACR,IAAI,CAAC,KAAK,GAAG;IACX,YAAA,eAAe,EAAE,CAAC;IAClB,YAAA,iBAAiB,EAAE,CAAC;IACpB,YAAA,qBAAqB,EAAE,CAAC;IACxB,YAAA,kBAAkB,EAAE,CAAC;IACrB,YAAA,mBAAmB,EAAE,CAAC;gBACtB,gBAAgB,EAAE,IAAI,GAAG,EAAE;aAC5B,CAAC;IACF,QAAA,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;SAClC;IAED;;IAEG;IACH,IAAA,wBAAwB,CACtB,kBAA8D,EAAA;YAE9D,IAAI,CAAC,kBAAkB,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE;IACjE,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,6CAA6C,CAAC,CAAC;gBAChE,OAAO;aACR;YAED,IAAI,CAAC,GAAG,CACN,MAAM,EACN,sCAAsC,EACtC,kBAAkB,CACnB,CAAC;IAEF,QAAA,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC3C,MAAM,SAAS,GAAG,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACvD,IAAI,SAAS,EAAE;IACb,gBAAA,IAAI;IACF,oBAAA,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;wBACpC,IAAI,CAAC,GAAG,CACN,MAAM,EACN,CAAmC,gCAAA,EAAA,WAAW,CAAC,IAAI,CAAE,CAAA,CACtD,CAAC;qBACH;oBAAC,OAAO,KAAK,EAAE;IACd,oBAAA,IAAI,CAAC,GAAG,CACN,OAAO,EACP,CAAA,wCAAA,EAA2C,WAAW,CAAC,IAAI,CAAA,CAAA,CAAG,EAC9D,KAAK,CACN,CAAC;qBACH;iBACF;aACF;SACF;IAED;;IAEG;IACH,IAAA,MAAM,OAAO,GAAA;IACX,QAAA,IAAI;IACF,YAAA,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;;IAGpC,YAAA,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE;IAC3C,gBAAA,IAAI;IACF,oBAAA,IAAI,WAAW,CAAC,OAAO,EAAE;IACvB,wBAAA,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;yBAC7B;qBACF;oBAAC,OAAO,KAAK,EAAE;wBACd,IAAI,CAAC,GAAG,CAAC,CAAiC,8BAAA,EAAA,WAAW,CAAC,IAAI,CAAG,CAAA,CAAA,EAAE,KAAK,CAAC,CAAC;qBACvE;iBACF;;IAGD,YAAA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;;IAG7B,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;IAClB,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;iBACvB;IAED,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;IAClB,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;iBACvB;IAED,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;IACxB,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;iBAC7B;IAED,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;IACtB,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;iBAC3B;IAED,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;IACvB,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;iBAC5B;IAED,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC3B,YAAA,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;aACvC;YAAC,OAAO,KAAK,EAAE;IACd,YAAA,IAAI,CAAC,GAAG,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;aACnD;SACF;IAED;;IAEG;QACK,GAAG,CAAC,GAAG,IAAW,EAAA;YACxB,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,GAAG,IAAI,CAAC,CAAC;SAC/C;IACF,CAAA;IAKD;IACA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;IAChC,IAAA,MAAc,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IAC5D;;;;;;;;"}