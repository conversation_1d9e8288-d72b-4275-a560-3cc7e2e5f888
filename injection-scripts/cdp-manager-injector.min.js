var CdpManagerInjector=function(t){"use strict";class e{constructor(t){this.ws=null,this.messageId=0,this.pendingMessages=new Map,this.targetInfo=t}async connect(){if(!this.ws)return this.ws=new WebSocket(this.targetInfo.webSocketDebuggerUrl),new Promise((t,e)=>{if(!this.ws)return e(new Error("WebSocket not initialized"));this.ws.onopen=()=>{console.log(`Connected to target: ${this.targetInfo.title}`),t()},this.ws.onerror=e,this.ws.onmessage=t=>{const e=JSON.parse(t.data);if(e.id&&this.pendingMessages.has(e.id)){const{resolve:t,reject:n}=this.pendingMessages.get(e.id);this.pendingMessages.delete(e.id),e.error?n(new Error(e.error.message)):t(e.result)}}})}async send(t,e={},n=null){this.ws||await this.connect();const s=++this.messageId,i={id:s,method:t,params:e};return n&&(i.sessionId=n),new Promise((t,e)=>{this.pendingMessages.set(s,{resolve:t,reject:e}),this.ws.send(JSON.stringify(i))})}get Runtime(){return{enable:(t={},e=null)=>this.send("Runtime.enable",t,e),evaluate:(t,e=null)=>this.send("Runtime.evaluate",t,e)}}get Target(){return{getTargets:(t={},e=null)=>this.send("Target.getTargets",t,e),createTarget:(t,e=null)=>this.send("Target.createTarget",t,e),attachToTarget:(t,e=null)=>this.send("Target.attachToTarget",t,e),closeTarget:(t,e=null)=>this.send("Target.closeTarget",t,e),activateTarget:(t,e=null)=>this.send("Target.activateTarget",t,e)}}get Input(){return{dispatchKeyEvent:(t,e=null)=>this.send("Input.dispatchKeyEvent",t,e),dispatchMouseEvent:(t,e=null)=>this.send("Input.dispatchMouseEvent",t,e)}}async close(){this.ws&&(this.ws.close(),this.ws=null)}}class n{constructor(t={}){this.connections=new Map,this.eventHandlers=new Map,this.options={debugPort:t.debugPort||9222,debug:t.debug||!1},this.log("[CDP-Manager] Initialized")}log(t,...e){this.options.debug&&console.log(`[CDP-Manager] ${t}`,...e)}async addConnection(t,n=null){try{if(this.connections.has(t))return this.connections.get(t);if(n||(n=await this.getTargetInfo(t)),!n)throw new Error(`Target tab ${t} not found`);const s=new e(n);await s.connect();const i=await s.Target.attachToTarget({targetId:t,flatten:!0}),o={client:s,sessionId:i.sessionId,targetInfo:n,createdAt:Date.now()};return this.connections.set(t,o),this.log(`CDP connection established for tab: ${t}`),o}catch(e){throw this.log(`Failed to add connection to tab ${t}:`,e),e}}async removeConnection(t){try{const e=this.connections.get(t);if(!e)return;await e.client.close(),this.connections.delete(t),this.log(`CDP connection removed for tab: ${t}`)}catch(e){throw this.log(`Failed to remove connection from tab ${t}:`,e),e}}getConnection(t){return this.connections.get(t)||null}getAllConnections(){return new Map(this.connections)}async executeCommand(t,e,n={}){try{const s=this.connections.get(t);if(!s)throw new Error(`No connection found for tab: ${t}`);const[i,o]=e.split("."),a=s.client[i];if(!a)throw new Error(`Unsupported domain: ${i}`);return await a[o](n,s.sessionId)}catch(n){throw this.log(`Failed to execute ${e} on tab ${t}:`,n),n}}async getTargetTabInfo(t){try{return(await this.executeCommand(t,"Runtime.evaluate",{expression:"({\n          width: window.innerWidth,\n          height: window.innerHeight,\n          url: window.location.href,\n          title: document.title\n        })",returnByValue:!0,awaitPromise:!0})).result.value}catch(e){return this.log(`Failed to get tab info for ${t}:`,e),{width:1920,height:1080,url:"unknown",title:"Unknown"}}}async executeScript(t,e){try{return(await this.executeCommand(t,"Runtime.evaluate",{expression:e,returnByValue:!0})).result.value}catch(e){return this.log(`Failed to execute script on tab ${t}:`,e),null}}registerEventHandler(t,e){this.eventHandlers.set(t,e)}unregisterEventHandler(t){this.eventHandlers.delete(t)}async handleUserEvent(t,e){try{const n=this.eventHandlers.get(t.eventType);if(!n)return void this.log(`No handler registered for event type: ${t.eventType}`);await n(t,e)}catch(t){throw this.log(`Failed to handle user event on tab ${e}:`,t),t}}async handleClickEvent(t,e){try{const n=await this.getTargetTabInfo(e);if(!n)throw new Error("Could not get target tab info");const s=t.x*n.width,i=t.y*n.height;await this.executeCommand(e,"Input.dispatchMouseEvent",{type:"mousePressed",x:Math.round(s),y:Math.round(i),button:"left",clickCount:1,buttons:1}),await new Promise(t=>setTimeout(t,50)),await this.executeCommand(e,"Input.dispatchMouseEvent",{type:"mouseReleased",x:Math.round(s),y:Math.round(i),button:"left",clickCount:1,buttons:0})}catch(t){throw this.log(`Failed to handle click event on tab ${e}:`,t),t}}async handleScrollEvent(t,e){try{const n=await this.getTargetTabInfo(e);if(!n)throw new Error("Could not get target tab info");const s=t.x*n.width,i=t.y*n.height;await this.executeCommand(e,"Input.dispatchMouseEvent",{type:"mouseWheel",x:Math.round(s),y:Math.round(i),deltaX:t.deltaX||0,deltaY:t.deltaY||0})}catch(t){throw this.log(`Failed to handle scroll event on tab ${e}:`,t),t}}async handleKeyEvent(t,e){try{await this.executeCommand(e,"Input.dispatchKeyEvent",{type:t.keyType||"keyDown",key:t.key,text:t.text,code:t.code,keyCode:t.keyCode})}catch(t){throw this.log(`Failed to handle key event on tab ${e}:`,t),t}}async getTargetInfo(t){try{const e=await fetch(`http://localhost:${this.options.debugPort}/json`);return(await e.json()).find(e=>e.id===t)||null}catch(e){return this.log(`Failed to get target info for ${t}:`,e),null}}initializeDefaultHandlers(){this.registerEventHandler("click",this.handleClickEvent.bind(this)),this.registerEventHandler("scroll",this.handleScrollEvent.bind(this)),this.registerEventHandler("keydown",this.handleKeyEvent.bind(this)),this.registerEventHandler("keyup",this.handleKeyEvent.bind(this)),this.registerEventHandler("keypress",this.handleKeyEvent.bind(this))}async cleanup(){const t=Array.from(this.connections.keys()).map(t=>this.removeConnection(t));await Promise.all(t),this.eventHandlers.clear(),this.log("CDP Manager cleanup completed")}getStats(){return{totalConnections:this.connections.size,eventHandlers:this.eventHandlers.size,connections:Array.from(this.connections.entries()).map(([t,e])=>({tabId:t,sessionId:e.sessionId,createdAt:e.createdAt,targetInfo:e.targetInfo}))}}}const s=new n({debug:!0});return"undefined"!=typeof window&&(window.CDPManager=s,window.CDP=e),t.CDP=e,t.CDPManager=n,t.default=n,Object.defineProperty(t,"__esModule",{value:!0}),t}({});
//# sourceMappingURL=cdp-manager-injector.min.js.map
