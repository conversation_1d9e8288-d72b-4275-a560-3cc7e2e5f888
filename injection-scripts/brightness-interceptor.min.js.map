{"version": 3, "file": "brightness-interceptor.min.js", "sources": ["../src/base-interceptor.ts", "../src/brightness-interceptor.ts"], "sourcesContent": [null, null], "names": ["BaseInterceptor", "constructor", "name", "defaultConfig", "this", "isInitialized", "isEnabled", "processor", "generator", "transformStream", "originalTrack", "processedTrack", "Error", "type", "config", "debug", "enabled", "stats", "framesProcessed", "errorsEncountered", "averageProcessingTime", "lastProcessingTime", "totalProcessingTime", "log", "initialize", "videoTrack", "kind", "label", "MediaStreamTrackProcessor", "track", "MediaStreamTrackGenerator", "TransformStream", "transform", "processFrame", "bind", "readable", "pipeThrough", "pipeTo", "writable", "catch", "error", "frame", "controller", "startTime", "performance", "now", "enqueue", "processedFrame", "processVideoFrame", "close", "processingTime", "updateConfig", "newConfig", "oldConfig", "old", "new", "onConfigChange", "getConfig", "enable", "disable", "getStats", "resetStats", "cleanup", "args", "console", "getMetadata", "window", "BrightnessInterceptor", "options", "super", "brightness", "minBrightness", "maxBrightness", "canvas", "ctx", "imageData", "onConfigUpdate", "oldBrightness", "newBrightness", "Math", "abs", "width", "codedWidth", "height", "codedHeight", "initializeCanvas", "drawImage", "getImageData", "data", "max", "min", "i", "length", "putImageData", "VideoFrame", "timestamp", "duration", "undefined", "document", "createElement", "getContext", "setBrightness", "clampedBrightness", "getBrightness", "getStatus", "canvasInitialized", "canvasSize"], "mappings": "kDAoBA,MAAeA,EAwBb,WAAAC,CAAYC,EAAcC,EAAmC,IAC3D,GAtBKC,KAAaC,eAAY,EACzBD,KAASE,WAAY,EAKpBF,KAASG,UAAqC,KAC9CH,KAASI,UAAqC,KAC9CJ,KAAeK,gBACrB,KAGML,KAAaM,cAA4B,KACzCN,KAAcO,eAA4B,KAS5CP,KAAKH,cAAgBD,EACvB,MAAM,IAAIY,MACR,4EAIJR,KAAKF,KAAOA,EACZE,KAAKS,KAAOT,KAAKH,YAAYC,KAG7BE,KAAKU,OAAS,CACZC,OAAO,EACPC,SAAS,KACNb,GAILC,KAAKa,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,GAGvBlB,KAAKmB,IAAI,GAAGnB,KAAKF,2BAClB,CAKD,gBAAMsB,CAAWC,GACf,IAAKA,GAAkC,UAApBA,EAAWC,KAC5B,MAAM,IAAId,MAAM,GAAGR,KAAKF,iDAG1BE,KAAKM,cAAgBe,EACrBrB,KAAKmB,IACH,gBAAgBnB,KAAKF,qCACrBuB,EAAWE,OAGb,IAuBE,OArBAvB,KAAKG,UAAY,IAAIqB,0BAA0B,CAAEC,MAAOJ,IACxDrB,KAAKI,UAAY,IAAIsB,0BAA0B,CAAEJ,KAAM,UAGvDtB,KAAKK,gBAAkB,IAAIsB,gBAAgB,CACzCC,UAAW5B,KAAK6B,aAAaC,KAAK9B,QAIpCA,KAAKG,UAAU4B,SACZC,YAAYhC,KAAKK,iBACjB4B,OAAOjC,KAAKI,UAAU8B,UACtBC,MAAOC,IACNpC,KAAKmB,IAAI,qBAAqBnB,KAAKF,QAASsC,GAC5CpC,KAAKa,MAAME,sBAGff,KAAKO,eAAiBP,KAAKI,UAAUqB,MACrCzB,KAAKC,eAAgB,EAErBD,KAAKmB,IAAI,GAAGnB,KAAKF,6CACVE,KAAKO,cACb,CAAC,MAAO6B,GAGP,MAFApC,KAAKmB,IAAI,sBAAsBnB,KAAKF,oBAAqBsC,GACzDpC,KAAKa,MAAME,oBACLqB,CACP,CACF,CAKD,kBAAMP,CACJQ,EACAC,GAEA,MAAMC,EAAYC,YAAYC,MAE9B,IAGE,GAFAzC,KAAKa,MAAMC,mBAENd,KAAKE,YAAcF,KAAKU,OAAOE,QAGlC,YADA0B,EAAWI,QAAQL,GAKrB,MAAMM,QAAuB3C,KAAK4C,kBAAkBP,GAGpDC,EAAWI,QAAQC,GAGfA,IAAmBN,GACrBA,EAAMQ,OAET,CAAC,MAAOT,GACPpC,KAAKmB,IAAI,6BAA6BnB,KAAKF,QAASsC,GACpDpC,KAAKa,MAAME,oBAGXuB,EAAWI,QAAQL,EACpB,CAAS,QAER,MAAMS,EAAiBN,YAAYC,MAAQF,EAC3CvC,KAAKa,MAAMI,mBAAqB6B,EAChC9C,KAAKa,MAAMK,qBAAuB4B,EAClC9C,KAAKa,MAAMG,sBACThB,KAAKa,MAAMK,oBAAsBlB,KAAKa,MAAMC,eAC/C,CACF,CAUD,YAAAiC,CAAaC,GACX,IAAKA,GAAkC,iBAAdA,EAEvB,YADAhD,KAAKmB,IAAI,OAAQ,kDAInB,MAAM8B,EAAY,IAAKjD,KAAKU,QAC5BV,KAAKU,OAAS,IAAKV,KAAKU,UAAWsC,GAEnChD,KAAKmB,IAAI,GAAGnB,KAAKF,8BAA+B,CAC9CoD,IAAKD,EACLE,IAAKnD,KAAKU,SAIRV,KAAKoD,gBACPpD,KAAKoD,eAAeH,EAAWjD,KAAKU,OAEvC,CAKD,SAAA2C,GACE,MAAO,IAAKrD,KAAKU,OAClB,CAKD,MAAA4C,GACEtD,KAAKE,WAAY,EACjBF,KAAKU,OAAOE,SAAU,EACtBZ,KAAKmB,IAAI,GAAGnB,KAAKF,2BAClB,CAKD,OAAAyD,GACEvD,KAAKE,WAAY,EACjBF,KAAKU,OAAOE,SAAU,EACtBZ,KAAKmB,IAAI,GAAGnB,KAAKF,4BAClB,CAKD,QAAA0D,GACE,MAAO,IAAKxD,KAAKa,MAClB,CAKD,UAAA4C,GACEzD,KAAKa,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,GAEvBlB,KAAKmB,IAAI,GAAGnB,KAAKF,mBAClB,CAKD,aAAM4D,GACJ,IACM1D,KAAKG,YACPH,KAAKG,UAAY,MAGfH,KAAKI,YACPJ,KAAKI,UAAY,MAGfJ,KAAKK,kBACPL,KAAKK,gBAAkB,MAGrBL,KAAKM,gBACPN,KAAKM,cAAgB,MAGnBN,KAAKO,iBACPP,KAAKO,eAAiB,MAGxBP,KAAKC,eAAgB,EACrBD,KAAKmB,IAAI,GAAGnB,KAAKF,8BAClB,CAAC,MAAOsC,GACPpC,KAAKmB,IAAI,qBAAqBnB,KAAKF,oBAAqBsC,EACzD,CACF,CAKD,GAAAjB,IAAOwC,GACD3D,KAAKU,OAAOC,OACdiD,QAAQzC,IAAI,IAAInB,KAAKF,uBAAwB6D,EAEhD,CAKD,WAAAE,GAQE,MAAO,CACL/D,KAAME,KAAKF,KACXW,KAAMT,KAAKS,KACXR,cAAeD,KAAKC,cACpBC,UAAWF,KAAKE,UAChBQ,OAAQV,KAAKqD,YACbxC,MAAOb,KAAKwD,WAEf,EAOmB,oBAAXM,SACRA,OAAelE,gBAAkBA,GChSpC,MAAMmE,UAA8BnE,EAWlC,WAAAC,CACEC,EAAe,oBACfkE,EAA4B,CAAA,GAW5BC,MAAMnE,EARgB,CACpBa,OAAO,EACPuD,WAAY,EACZC,cAAe,GACfC,cAAe,KACZJ,IAnBChE,KAAMqE,OAA6B,KACnCrE,KAAGsE,IAAoC,KACvCtE,KAASuE,UAAqB,KAuBpCvE,KAAKoD,eAAiBpD,KAAKwE,eAAe1C,KAAK9B,MAE/CA,KAAKmB,IAAI,oCAAqCnB,KAAKU,OACpD,CAKO,cAAA8D,CACNvB,EACAD,GAEA,MAAMyB,EAAiBxB,EAAkBiB,YAAc,EACjDQ,EAAiB1B,EAAkBkB,YAAc,EAGnDQ,EAAgB1E,KAAKU,OAAOyD,eAC9BnE,KAAKU,OAAOwD,WAAalE,KAAKU,OAAOyD,cACrCnE,KAAKmB,IACH,OACA,kCAAkCnB,KAAKU,OAAOyD,kBAEvCO,EAAgB1E,KAAKU,OAAO0D,gBACrCpE,KAAKU,OAAOwD,WAAalE,KAAKU,OAAO0D,cACrCpE,KAAKmB,IACH,OACA,kCAAkCnB,KAAKU,OAAO0D,kBAIlDpE,KAAKmB,IACH,OACA,2BAA2BsD,QAAoBzE,KAAKU,OAAOwD,aAE9D,CAKD,uBAAMtB,CAAkBP,GAEtB,GAAIsC,KAAKC,KAAK5E,KAAKU,OAAOwD,YAAc,GAAO,GAAO,IACpD,OAAO7B,EAGT,IAUE,GAPGrC,KAAKqE,QACNrE,KAAKqE,OAAOQ,QAAUxC,EAAMyC,YAC5B9E,KAAKqE,OAAOU,SAAW1C,EAAM2C,aAE7BhF,KAAKiF,iBAAiB5C,EAAMyC,WAAYzC,EAAM2C,cAG3ChF,KAAKsE,IACR,MAAM,IAAI9D,MAAM,gCAIlBR,KAAKsE,IAAIY,UAAU7C,EAAO,EAAG,GAG7BrC,KAAKuE,UAAYvE,KAAKsE,IAAIa,aACxB,EACA,EACAnF,KAAKqE,OAAQQ,MACb7E,KAAKqE,OAAQU,QAEf,MAAMK,EAAOpF,KAAKuE,UAAUa,KAGtBlB,EAAaS,KAAKU,IACtBrF,KAAKU,OAAOyD,cACZQ,KAAKW,IAAItF,KAAKU,OAAO0D,cAAepE,KAAKU,OAAOwD,YAAc,IAGhE,IAAK,IAAIqB,EAAI,EAAGA,EAAIH,EAAKI,OAAQD,GAAK,EAEpCH,EAAKG,GAAKZ,KAAKW,IAAI,IAAKF,EAAKG,GAAKrB,GAClCkB,EAAKG,EAAI,GAAKZ,KAAKW,IAAI,IAAKF,EAAKG,EAAI,GAAKrB,GAC1CkB,EAAKG,EAAI,GAAKZ,KAAKW,IAAI,IAAKF,EAAKG,EAAI,GAAKrB,GAK5ClE,KAAKsE,IAAImB,aAAazF,KAAKuE,UAAW,EAAG,GAQzC,OALuB,IAAImB,WAAW1F,KAAKqE,OAAS,CAClDsB,UAAWtD,EAAMsD,UACjBC,SAAUvD,EAAMuD,eAAYC,GAI/B,CAAC,MAAOzD,GAGP,OAFApC,KAAKmB,IAAI,+BAAgCiB,GAElCC,CACR,CACF,CAKO,gBAAA4C,CAAiBJ,EAAeE,GAMtC,GALA/E,KAAKqE,OAASyB,SAASC,cAAc,UACrC/F,KAAKqE,OAAOQ,MAAQA,EACpB7E,KAAKqE,OAAOU,OAASA,EACrB/E,KAAKsE,IAAMtE,KAAKqE,OAAO2B,WAAW,OAE7BhG,KAAKsE,IACR,MAAM,IAAI9D,MAAM,mCAGlBR,KAAKmB,IAAI,uBAAuB0D,KAASE,IAC1C,CAKD,aAAAkB,CAAc/B,GACZ,MAAMgC,EAAoBvB,KAAKU,IAC7BrF,KAAKU,OAAOyD,cACZQ,KAAKW,IAAItF,KAAKU,OAAO0D,cAAeF,IAEtClE,KAAK+C,aAAa,CAAEmB,WAAYgC,IAChClG,KAAKmB,IAAI,sBAAsB+E,IAChC,CAKD,aAAAC,GACE,OAAOnG,KAAKU,OAAOwD,YAAc,CAClC,CAKQ,aAAMR,GACb1D,KAAKmB,IAAI,yCAGLnB,KAAKqE,SACPrE,KAAKqE,OAAOQ,MAAQ,EACpB7E,KAAKqE,OAAOU,OAAS,EACrB/E,KAAKqE,OAAS,MAGhBrE,KAAKsE,IAAM,KACXtE,KAAKuE,UAAY,WAGXN,MAAMP,UAEZ1D,KAAKmB,IAAI,0CACV,CAKD,SAAAiF,GAWE,MAAO,IACFpG,KAAK6D,cACRK,WAAYlE,KAAKU,OAAOwD,YAAc,EACtCmC,oBAAqBrG,KAAKqE,OAC1BiC,WAAYtG,KAAKqE,OACb,GAAGrE,KAAKqE,OAAOQ,SAAS7E,KAAKqE,OAAOU,SACpC,KAEP,QAOmB,oBAAXjB,SACRA,OAAeC,sBAAwBA"}