{"version": 3, "file": "interceptor-registry.min.js", "sources": ["../src/interceptor-registry.ts"], "sourcesContent": [null], "names": ["InterceptorRegistry", "constructor", "this", "interceptorClasses", "Map", "defaultConfigs", "clientInterceptors", "clientConfigs", "log", "register", "name", "interceptorClass", "defaultConfig", "Error", "set", "unregister", "has", "delete", "getRegisteredInterceptors", "Array", "from", "keys", "isRegistered", "create", "config", "InterceptorClass", "get", "finalConfig", "error", "setClientConfiguration", "webClientId", "interceptorNames", "configs", "clientConfig", "userConfig", "getClientConfiguration", "createClientInterceptors", "existingInterceptors", "values", "interceptors", "interceptorMap", "interceptor", "push", "cleanup", "cleanupError", "message", "String", "getClientInterceptors", "map", "filter", "undefined", "updateClientInterceptorConfig", "interceptorName", "newConfig", "currentConfig", "updateConfig", "cleanupClientInterceptors", "getStats", "stats", "registeredInterceptors", "size", "activeClients", "totalActiveInterceptors", "clientStats", "length", "interceptorCount", "i", "type", "isEnabled", "args", "console", "interceptorRegistry", "window"], "mappings": "iDA0BA,MAAMA,EAgBJ,WAAAC,GAdQC,KAAAC,mBAAqB,IAAIC,IAGzBF,KAAAG,eAAiB,IAAID,IAGrBF,KAAAI,mBAAqB,IAAIF,IAMzBF,KAAAK,cAAgB,IAAIH,IAG1BF,KAAKM,IAAI,kCACV,CAKD,QAAAC,CACEC,EACAC,EACAC,EAAmC,CAAA,GAEnC,IAAKF,GAAwB,iBAATA,EAClB,MAAM,IAAIG,MAAM,+CAGlB,IAAKF,GAAgD,mBAArBA,EAC9B,MAAM,IAAIE,MAAM,oDAGlBX,KAAKC,mBAAmBW,IAAIJ,EAAMC,GAClCT,KAAKG,eAAeS,IAAIJ,EAAME,GAE9BV,KAAKM,IAAI,2BAA2BE,IACrC,CAKD,UAAAK,CAAWL,GACLR,KAAKC,mBAAmBa,IAAIN,KAC9BR,KAAKC,mBAAmBc,OAAOP,GAC/BR,KAAKG,eAAeY,OAAOP,GAC3BR,KAAKM,IAAI,6BAA6BE,KAEzC,CAKD,yBAAAQ,GACE,OAAOC,MAAMC,KAAKlB,KAAKC,mBAAmBkB,OAC3C,CAKD,YAAAC,CAAaZ,GACX,OAAOR,KAAKC,mBAAmBa,IAAIN,EACpC,CAKD,MAAAa,CACEb,EACAc,GAEA,MAAMC,EAAmBvB,KAAKC,mBAAmBuB,IAAIhB,GACrD,IAAKe,EAEH,OADAvB,KAAKM,IAAI,4CAA4CE,KAC9C,KAGT,MACMiB,EAAc,IADEzB,KAAKG,eAAeqB,IAAIhB,IAAS,MACZc,GAE3C,IACE,OAAO,IAAIC,EAAiBf,EAAMiB,EACnC,CAAC,MAAOC,GAEP,OADA1B,KAAKM,IAAI,8BAA8BE,KAASkB,GACzC,IACR,CACF,CAKD,sBAAAC,CACEC,EACAC,EAA6B,GAC7BC,EAA6C,CAAA,GAG7C,IAAK,MAAMtB,KAAQqB,EACjB,IAAK7B,KAAKC,mBAAmBa,IAAIN,GAC/B,MAAM,IAAIG,MAAM,wBAAwBH,KAK5C,MAAMuB,EAAoC,CACxCF,iBAAkB,IAAIA,GACtBC,QAAS,IAAI5B,KAIf,IAAK,MAAMM,KAAQqB,EAAkB,CACnC,MAAMnB,EAAgBV,KAAKG,eAAeqB,IAAIhB,IAAS,GACjDwB,EAAaF,EAAQtB,IAAS,CAAA,EACpCuB,EAAaD,QAAQlB,IAAIJ,EAAM,IAAKE,KAAkBsB,GACvD,CAEDhC,KAAKK,cAAcO,IAAIgB,EAAaG,GACpC/B,KAAKM,IAAI,gCAAgCsB,KAAgBC,EAC1D,CAKD,sBAAAI,CAAuBL,GACrB,MAAMN,EAAStB,KAAKK,cAAcmB,IAAII,GACtC,OAAKN,EAIE,CACLO,iBAAkB,IAAIP,EAAOO,kBAC7BC,QAAS,IAAI5B,IAAIoB,EAAOQ,UALjB,CAAED,iBAAkB,GAAIC,QAAS,IAAI5B,IAO/C,CAKD,wBAAAgC,CAAyBN,GAEvB,GAAI5B,KAAKI,mBAAmBU,IAAIc,GAAc,CAC5C5B,KAAKM,IACH,yCAAyCsB,mCAE3C,MAAMO,EAAuBnC,KAAKI,mBAAmBoB,IAAII,GACzD,OAAOX,MAAMC,KAAKiB,EAAqBC,SACxC,CAED,MAAML,EAAe/B,KAAKiC,uBAAuBL,GAC3CS,EAA2C,GAC3CC,EAAiB,IAAIpC,IAE3B,IACE,IAAK,MAAMM,KAAQuB,EAAaF,iBAAkB,CAChD,MAAMN,EAAmBvB,KAAKC,mBAAmBuB,IAAIhB,GAC/Cc,EAASS,EAAaD,QAAQN,IAAIhB,IAAS,GAEjD,IAAKe,EAAkB,CACrBvB,KAAKM,IACH,4CAA4CE,eAE9C,QACD,CAGD,MAAM+B,EAAc,IAAIhB,EAAiBf,EAAMc,GAC/Ce,EAAaG,KAAKD,GAClBD,EAAe1B,IAAIJ,EAAM+B,GAEzBvC,KAAKM,IAAI,WAAWE,4BAA+BoB,IACpD,CAKD,OAFA5B,KAAKI,mBAAmBQ,IAAIgB,EAAaU,GAElCD,CACR,CAAC,MAAOX,GAEP,IAAK,MAAMa,KAAeF,EACxB,IACEE,EAAYE,SACb,CAAC,MAAOC,GACP1C,KAAKM,IACH,yDACAoC,EAEH,CAGH,MAAM,IAAI/B,MACR,4CAA4CiB,MAC1CF,aAAiBf,MAAQe,EAAMiB,QAAUC,OAAOlB,KAGrD,CACF,CAKD,qBAAAmB,CAAsBjB,GACpB,MAAMU,EAAiBtC,KAAKI,mBAAmBoB,IAAII,GACnD,IAAKU,EACH,MAAO,GAIT,OADqBtC,KAAKiC,uBAAuBL,GAC7BC,iBACjBiB,IAAKtC,GAAS8B,EAAed,IAAIhB,IACjCuC,OACER,QACiBS,IAAhBT,EAEP,CAKD,6BAAAU,CACErB,EACAsB,EACAC,GAEA,MAAMpB,EAAe/B,KAAKK,cAAcmB,IAAII,GAC5C,IAAKG,EACH,MAAM,IAAIpB,MAAM,sCAAsCiB,KAIxD,MAAMwB,EAAgBrB,EAAaD,QAAQN,IAAI0B,IAAoB,GACnEnB,EAAaD,QAAQlB,IAAIsC,EAAiB,IACrCE,KACAD,IAIL,MAAMb,EAAiBtC,KAAKI,mBAAmBoB,IAAII,GACnD,GAAIU,GAAkBA,EAAexB,IAAIoC,GAAkB,CACrCZ,EAAed,IAAI0B,GAC3BG,aAAaF,EAC1B,CAEDnD,KAAKM,IAAI,WAAW4C,uBAAqCtB,IAC1D,CAKD,yBAAA0B,CAA0B1B,GACxB,MAAMU,EAAiBtC,KAAKI,mBAAmBoB,IAAII,GACnD,GAAIU,EAAgB,CAClB,IAAK,MAAO9B,EAAM+B,KAAgBD,EAChC,IACEC,EAAYE,UACZzC,KAAKM,IAAI,cAAcE,4BAA+BoB,IACvD,CAAC,MAAOF,GACP1B,KAAKM,IAAI,qBAAqBE,iBAAqBkB,EACpD,CAGH1B,KAAKI,mBAAmBW,OAAOa,EAChC,CAGD5B,KAAKK,cAAcU,OAAOa,GAE1B5B,KAAKM,IAAI,0CAA0CsB,IACpD,CAKD,QAAA2B,GAiBE,MAAMC,EAAQ,CACZC,uBAAwBzD,KAAKC,mBAAmByD,KAChDC,cAAe3D,KAAKI,mBAAmBsD,KACvCE,wBAAyB,EACzBC,YAAa,CAAyB,GAGxC,IAAK,MAAOjC,EAAaU,KAAmBtC,KAAKI,mBAAoB,CACnE,MAAMA,EAAqBa,MAAMC,KAAKoB,EAAeF,UACrDoB,EAAMI,yBAA2BxD,EAAmB0D,OAEpDN,EAAMK,YAAYjC,GAAe,CAC/BmC,iBAAkB3D,EAAmB0D,OACrCzB,aAAcjC,EAAmB0C,IAAKkB,IAAO,CAC3CxD,KAAMwD,EAAExD,KACRyD,KAAMD,EAAEC,KACRC,UAAWF,EAAEE,UACbV,MAAOQ,EAAET,cAGd,CAED,OAAOC,CACR,CAKO,GAAAlD,IAAO6D,GACbC,QAAQ9D,IAAI,2BAA4B6D,EACzC,EAIH,MAAME,EAAsB,IAAIvE,QAOV,oBAAXwE,SACRA,OAAexE,oBAAsBA,EACrCwE,OAAeD,oBAAsBA"}