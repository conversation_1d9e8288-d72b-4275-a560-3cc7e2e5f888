{"version": 3, "file": "video-crop-interceptor.min.js", "sources": ["../src/base-interceptor.ts", "../src/video-crop-interceptor.ts"], "sourcesContent": [null, null], "names": ["BaseInterceptor", "constructor", "name", "defaultConfig", "this", "isInitialized", "isEnabled", "processor", "generator", "transformStream", "originalTrack", "processedTrack", "Error", "type", "config", "debug", "enabled", "stats", "framesProcessed", "errorsEncountered", "averageProcessingTime", "lastProcessingTime", "totalProcessingTime", "log", "initialize", "videoTrack", "kind", "label", "MediaStreamTrackProcessor", "track", "MediaStreamTrackGenerator", "TransformStream", "transform", "processFrame", "bind", "readable", "pipeThrough", "pipeTo", "writable", "catch", "error", "frame", "controller", "startTime", "performance", "now", "enqueue", "processedFrame", "processVideoFrame", "close", "processingTime", "updateConfig", "newConfig", "oldConfig", "old", "new", "onConfigChange", "getConfig", "enable", "disable", "getStats", "resetStats", "cleanup", "args", "console", "getMetadata", "window", "VideoFrameInterceptor", "options", "super", "frameRate", "enableCropping", "subscribers", "Map", "frameCount", "lastFrameTime", "currentTime", "cropRegion", "defaultCropRegion", "applyCropping", "size", "notifySubscribers", "codedWidth", "codedHeight", "safeCropRegion", "x", "makeEven", "Math", "max", "min", "y", "width", "height", "croppedFrame", "VideoFrame", "visibleRect", "displayWidth", "displayHeight", "timestamp", "duration", "undefined", "promises", "subscriberId", "callback", "frameClone", "result", "Promise", "push", "length", "allSettled", "subscribe", "set", "unsubscribe", "removed", "delete", "setCropRegion", "setCroppingEnabled", "getStatus", "subscriberCount", "hasOriginalTrack", "hasProcessedTrack", "clear", "value", "floor"], "mappings": "iDAoBA,MAAeA,EAwBb,WAAAC,CAAYC,EAAcC,EAAmC,IAC3D,GAtBKC,KAAaC,eAAY,EACzBD,KAASE,WAAY,EAKpBF,KAASG,UAAqC,KAC9CH,KAASI,UAAqC,KAC9CJ,KAAeK,gBACrB,KAGML,KAAaM,cAA4B,KACzCN,KAAcO,eAA4B,KAS5CP,KAAKH,cAAgBD,EACvB,MAAM,IAAIY,MACR,4EAIJR,KAAKF,KAAOA,EACZE,KAAKS,KAAOT,KAAKH,YAAYC,KAG7BE,KAAKU,OAAS,CACZC,OAAO,EACPC,SAAS,KACNb,GAILC,KAAKa,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,GAGvBlB,KAAKmB,IAAI,GAAGnB,KAAKF,2BAClB,CAKD,gBAAMsB,CAAWC,GACf,IAAKA,GAAkC,UAApBA,EAAWC,KAC5B,MAAM,IAAId,MAAM,GAAGR,KAAKF,iDAG1BE,KAAKM,cAAgBe,EACrBrB,KAAKmB,IACH,gBAAgBnB,KAAKF,qCACrBuB,EAAWE,OAGb,IAuBE,OArBAvB,KAAKG,UAAY,IAAIqB,0BAA0B,CAAEC,MAAOJ,IACxDrB,KAAKI,UAAY,IAAIsB,0BAA0B,CAAEJ,KAAM,UAGvDtB,KAAKK,gBAAkB,IAAIsB,gBAAgB,CACzCC,UAAW5B,KAAK6B,aAAaC,KAAK9B,QAIpCA,KAAKG,UAAU4B,SACZC,YAAYhC,KAAKK,iBACjB4B,OAAOjC,KAAKI,UAAU8B,UACtBC,MAAOC,IACNpC,KAAKmB,IAAI,qBAAqBnB,KAAKF,QAASsC,GAC5CpC,KAAKa,MAAME,sBAGff,KAAKO,eAAiBP,KAAKI,UAAUqB,MACrCzB,KAAKC,eAAgB,EAErBD,KAAKmB,IAAI,GAAGnB,KAAKF,6CACVE,KAAKO,cACb,CAAC,MAAO6B,GAGP,MAFApC,KAAKmB,IAAI,sBAAsBnB,KAAKF,oBAAqBsC,GACzDpC,KAAKa,MAAME,oBACLqB,CACP,CACF,CAKD,kBAAMP,CACJQ,EACAC,GAEA,MAAMC,EAAYC,YAAYC,MAE9B,IAGE,GAFAzC,KAAKa,MAAMC,mBAENd,KAAKE,YAAcF,KAAKU,OAAOE,QAGlC,YADA0B,EAAWI,QAAQL,GAKrB,MAAMM,QAAuB3C,KAAK4C,kBAAkBP,GAGpDC,EAAWI,QAAQC,GAGfA,IAAmBN,GACrBA,EAAMQ,OAET,CAAC,MAAOT,GACPpC,KAAKmB,IAAI,6BAA6BnB,KAAKF,QAASsC,GACpDpC,KAAKa,MAAME,oBAGXuB,EAAWI,QAAQL,EACpB,CAAS,QAER,MAAMS,EAAiBN,YAAYC,MAAQF,EAC3CvC,KAAKa,MAAMI,mBAAqB6B,EAChC9C,KAAKa,MAAMK,qBAAuB4B,EAClC9C,KAAKa,MAAMG,sBACThB,KAAKa,MAAMK,oBAAsBlB,KAAKa,MAAMC,eAC/C,CACF,CAUD,YAAAiC,CAAaC,GACX,IAAKA,GAAkC,iBAAdA,EAEvB,YADAhD,KAAKmB,IAAI,OAAQ,kDAInB,MAAM8B,EAAY,IAAKjD,KAAKU,QAC5BV,KAAKU,OAAS,IAAKV,KAAKU,UAAWsC,GAEnChD,KAAKmB,IAAI,GAAGnB,KAAKF,8BAA+B,CAC9CoD,IAAKD,EACLE,IAAKnD,KAAKU,SAIRV,KAAKoD,gBACPpD,KAAKoD,eAAeH,EAAWjD,KAAKU,OAEvC,CAKD,SAAA2C,GACE,MAAO,IAAKrD,KAAKU,OAClB,CAKD,MAAA4C,GACEtD,KAAKE,WAAY,EACjBF,KAAKU,OAAOE,SAAU,EACtBZ,KAAKmB,IAAI,GAAGnB,KAAKF,2BAClB,CAKD,OAAAyD,GACEvD,KAAKE,WAAY,EACjBF,KAAKU,OAAOE,SAAU,EACtBZ,KAAKmB,IAAI,GAAGnB,KAAKF,4BAClB,CAKD,QAAA0D,GACE,MAAO,IAAKxD,KAAKa,MAClB,CAKD,UAAA4C,GACEzD,KAAKa,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,GAEvBlB,KAAKmB,IAAI,GAAGnB,KAAKF,mBAClB,CAKD,aAAM4D,GACJ,IACM1D,KAAKG,YACPH,KAAKG,UAAY,MAGfH,KAAKI,YACPJ,KAAKI,UAAY,MAGfJ,KAAKK,kBACPL,KAAKK,gBAAkB,MAGrBL,KAAKM,gBACPN,KAAKM,cAAgB,MAGnBN,KAAKO,iBACPP,KAAKO,eAAiB,MAGxBP,KAAKC,eAAgB,EACrBD,KAAKmB,IAAI,GAAGnB,KAAKF,8BAClB,CAAC,MAAOsC,GACPpC,KAAKmB,IAAI,qBAAqBnB,KAAKF,oBAAqBsC,EACzD,CACF,CAKD,GAAAjB,IAAOwC,GACD3D,KAAKU,OAAOC,OACdiD,QAAQzC,IAAI,IAAInB,KAAKF,uBAAwB6D,EAEhD,CAKD,WAAAE,GAQE,MAAO,CACL/D,KAAME,KAAKF,KACXW,KAAMT,KAAKS,KACXR,cAAeD,KAAKC,cACpBC,UAAWF,KAAKE,UAChBQ,OAAQV,KAAKqD,YACbxC,MAAOb,KAAKwD,WAEf,EAOmB,oBAAXM,SACRA,OAAelE,gBAAkBA,GC3QpC,MAAMmE,UAA8BnE,EAQlC,WAAAC,CAAYC,EAAe,aAAckE,EAA2B,CAAA,GASlEC,MAAMnE,EAPiC,CACrCa,OAAO,EACPuD,UAAW,GACXC,gBAAgB,KACbH,IAZChE,KAAAoE,YAAc,IAAIC,IAClBrE,KAAUsE,WAAW,EACrBtE,KAAauE,cAAW,EAe9BvE,KAAKmB,IAAI,oCAAqCnB,KAAKU,OACpD,CAKD,uBAAMkC,CAAkBP,GACtBrC,KAAKsE,aACL,MAAME,EAAchC,YAAYC,MAG5BzC,KAAKU,OAAOC,OAAS6D,EAAcxE,KAAKuE,cAAgB,MAC1DvE,KAAKmB,IAAI,aAAanB,KAAKsE,qBAC3BtE,KAAKuE,cAAgBC,GAGvB,IAAI7B,EAAiBN,EAerB,OAXErC,KAAKU,OAAOyD,iBACXnE,KAAKU,OAAO+D,YAAczE,KAAKU,OAAOgE,qBAEvC/B,EAAiB3C,KAAK2E,cAActC,IAIlCrC,KAAKoE,YAAYQ,KAAO,SACpB5E,KAAK6E,kBAAkBlC,GAGxBA,CACR,CAKO,aAAAgC,CAActC,GACpB,MAAMyC,WAAEA,EAAUC,YAAEA,GAAgB1C,EAC9BoC,EAAazE,KAAKU,OAAO+D,YAAczE,KAAKU,OAAOgE,kBAEzD,IAAKD,EACH,OAAOpC,EAIT,MAAM2C,EAA6B,CACjCC,EAAGjF,KAAKkF,SAASC,KAAKC,IAAI,EAAGD,KAAKE,IAAIZ,EAAWQ,EAAGH,KACpDQ,EAAGtF,KAAKkF,SAASC,KAAKC,IAAI,EAAGD,KAAKE,IAAIZ,EAAWa,EAAGP,KACpDQ,MAAOvF,KAAKkF,SACVC,KAAKC,IAAI,EAAGD,KAAKE,IAAIZ,EAAWc,MAAOT,EAAaL,EAAWQ,KAEjEO,OAAQxF,KAAKkF,SACXC,KAAKC,IAAI,EAAGD,KAAKE,IAAIZ,EAAWe,OAAQT,EAAcN,EAAWa,MAIrE,IACE,MAAMG,EAAe,IAAIC,WAAWrD,EAAO,CACzCsD,YAAaX,EACbY,aAAcZ,EAAeO,MAC7BM,cAAeb,EAAeQ,OAC9BM,UAAWzD,EAAMyD,UACjBC,SAAU1D,EAAM0D,eAAYC,IAI9B,OADAhG,KAAKmB,IAAI,iBAAkB6D,GACpBS,CACR,CAAC,MAAOrD,GAEP,OADApC,KAAKmB,IAAI,yCAA0CiB,GAC5CC,CACR,CACF,CAKO,uBAAMwC,CAAkBxC,GAC9B,MAAM4D,EAA4B,GAElC,IAAK,MAAOC,EAAcC,KAAanG,KAAKoE,YAC1C,IAEE,MAAMgC,EAAa,IAAIV,WAAWrD,EAAO,CACvCyD,UAAWzD,EAAMyD,UACjBC,SAAU1D,EAAM0D,eAAYC,IAWxBK,EAASF,EAASC,EARa,CACnCF,eACA5B,WAAYtE,KAAKsE,WACjBwB,UAAWtD,YAAYC,MACvBgC,WACEzE,KAAKU,OAAO+D,YAAczE,KAAKU,OAAOgE,mBAAqB,OAM3D2B,aAAkBC,SACpBL,EAASM,KAAKF,EAEjB,CAAC,MAAOjE,GACPpC,KAAKmB,IAAI,8BAA8B+E,KAAiB9D,EACzD,CAIC6D,EAASO,OAAS,SACdF,QAAQG,WAAWR,EAE5B,CAKD,SAAAS,CACER,EACAC,GAEA,GAAwB,mBAAbA,EACT,MAAM,IAAI3F,MAAM,+BASlB,OANAR,KAAKoE,YAAYuC,IAAIT,EAAcC,GACnCnG,KAAKmB,IACH,qBAAqB+E,aAAwBlG,KAAKoE,YAAYQ,SAIzD,IAAM5E,KAAK4G,YAAYV,EAC/B,CAKD,WAAAU,CAAYV,GACV,MAAMW,EAAU7G,KAAKoE,YAAY0C,OAAOZ,GAMxC,OALIW,GACF7G,KAAKmB,IACH,uBAAuB+E,iBAA4BlG,KAAKoE,YAAYQ,SAGjEiC,CACR,CAKD,aAAAE,CAActC,GACRA,GAAoC,iBAAfA,GACvBzE,KAAK+C,aAAa,CAChB0B,WAAY,CACVQ,EAAGjF,KAAKkF,SAAST,EAAWQ,GAAK,GACjCK,EAAGtF,KAAKkF,SAAST,EAAWa,GAAK,GACjCC,MAAOvF,KAAKkF,SAAST,EAAWc,OAAS,KACzCC,OAAQxF,KAAKkF,SAAST,EAAWe,QAAU,QAG/CxF,KAAKmB,IAAI,uBAAwBnB,KAAKU,OAAO+D,cAE7CzE,KAAK+C,aAAa,CAAE0B,WAAY,OAChCzE,KAAKmB,IAAI,uBAEZ,CAKD,kBAAA6F,CAAmBpG,GACjBZ,KAAK+C,aAAa,CAChBoB,iBAAkBvD,IAEpBZ,KAAKmB,IAAI,aAAYnB,KAAKU,OAAOyD,eAAiB,UAAY,YAC/D,CAKD,SAAA8C,GAcE,MAAO,IACFjH,KAAK6D,cACRM,eAAgBnE,KAAKU,OAAOyD,iBAAkB,EAC9CM,WACEzE,KAAKU,OAAO+D,YAAczE,KAAKU,OAAOgE,mBAAqB,KAC7DwC,gBAAiBlH,KAAKoE,YAAYQ,KAClCN,WAAYtE,KAAKsE,WACjB6C,kBAAkB,EAClBC,mBAAmB,EAEtB,CAKQ,aAAM1D,GACb1D,KAAKmB,IAAI,oCAGTnB,KAAKoE,YAAYiD,cAGXpD,MAAMP,UAEZ1D,KAAKmB,IAAI,qCACV,CAKO,QAAA+D,CAASoC,GACf,OAA+B,EAAxBnC,KAAKoC,MAAMD,EAAQ,EAC3B,QAOmB,oBAAXxD,SACRA,OAAeC,sBAAwBA"}