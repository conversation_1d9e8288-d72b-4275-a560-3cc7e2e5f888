var BrightnessInterceptor=function(){"use strict";class t{constructor(i,s={}){if(this.isInitialized=!1,this.isEnabled=!0,this.processor=null,this.generator=null,this.transformStream=null,this.originalTrack=null,this.processedTrack=null,this.constructor===t)throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");this.name=i,this.type=this.constructor.name,this.config={debug:!1,enabled:!0,...s},this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} interceptor created`)}async initialize(t){if(!t||"video"!==t.kind)throw new Error(`${this.name} interceptor requires a valid video track`);this.originalTrack=t,this.log(`Initializing ${this.name} interceptor with video track:`,t.label);try{return this.processor=new MediaStreamTrackProcessor({track:t}),this.generator=new MediaStreamTrackGenerator({kind:"video"}),this.transformStream=new TransformStream({transform:this.processFrame.bind(this)}),this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(t=>{this.log(`Pipeline error in ${this.name}:`,t),this.stats.errorsEncountered++}),this.processedTrack=this.generator.track,this.isInitialized=!0,this.log(`${this.name} interceptor initialized successfully`),this.processedTrack}catch(t){throw this.log(`Error initializing ${this.name} interceptor:`,t),this.stats.errorsEncountered++,t}}async processFrame(t,i){const s=performance.now();try{if(this.stats.framesProcessed++,!this.isEnabled||!this.config.enabled)return void i.enqueue(t);const s=await this.processVideoFrame(t);i.enqueue(s),s!==t&&t.close()}catch(s){this.log(`Error processing frame in ${this.name}:`,s),this.stats.errorsEncountered++,i.enqueue(t)}finally{const t=performance.now()-s;this.stats.lastProcessingTime=t,this.stats.totalProcessingTime+=t,this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}}updateConfig(t){if(!t||"object"!=typeof t)return void this.log("warn","Invalid configuration provided to updateConfig");const i={...this.config};this.config={...this.config,...t},this.log(`${this.name} configuration updated:`,{old:i,new:this.config}),this.onConfigChange&&this.onConfigChange(i,this.config)}getConfig(){return{...this.config}}enable(){this.isEnabled=!0,this.config.enabled=!0,this.log(`${this.name} interceptor enabled`)}disable(){this.isEnabled=!1,this.config.enabled=!1,this.log(`${this.name} interceptor disabled`)}getStats(){return{...this.stats}}resetStats(){this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} stats reset`)}async cleanup(){try{this.processor&&(this.processor=null),this.generator&&(this.generator=null),this.transformStream&&(this.transformStream=null),this.originalTrack&&(this.originalTrack=null),this.processedTrack&&(this.processedTrack=null),this.isInitialized=!1,this.log(`${this.name} interceptor cleaned up`)}catch(t){this.log(`Error cleaning up ${this.name} interceptor:`,t)}}log(...t){this.config.debug&&console.log(`[${this.name}-Interceptor]`,...t)}getMetadata(){return{name:this.name,type:this.type,isInitialized:this.isInitialized,isEnabled:this.isEnabled,config:this.getConfig(),stats:this.getStats()}}}"undefined"!=typeof window&&(window.BaseInterceptor=t);class i extends t{constructor(t="brightness-filter",i={}){super(t,{debug:!1,brightness:1,minBrightness:.1,maxBrightness:3,...i}),this.canvas=null,this.ctx=null,this.imageData=null,this.onConfigChange=this.onConfigUpdate.bind(this),this.log("BrightnessInterceptor initialized",this.config)}onConfigUpdate(t,i){const s=t.brightness||1,e=i.brightness||1;e<this.config.minBrightness?(this.config.brightness=this.config.minBrightness,this.log("warn",`Brightness clamped to minimum: ${this.config.minBrightness}`)):e>this.config.maxBrightness&&(this.config.brightness=this.config.maxBrightness,this.log("warn",`Brightness clamped to maximum: ${this.config.maxBrightness}`)),this.log("info",`Brightness updated from ${s} to ${this.config.brightness}`)}async processVideoFrame(t){if(Math.abs((this.config.brightness||1)-1)<.01)return t;try{if(this.canvas&&this.canvas.width===t.codedWidth&&this.canvas.height===t.codedHeight||this.initializeCanvas(t.codedWidth,t.codedHeight),!this.ctx)throw new Error("Canvas context not available");this.ctx.drawImage(t,0,0),this.imageData=this.ctx.getImageData(0,0,this.canvas.width,this.canvas.height);const i=this.imageData.data,s=Math.max(this.config.minBrightness,Math.min(this.config.maxBrightness,this.config.brightness||1));for(let t=0;t<i.length;t+=4)i[t]=Math.min(255,i[t]*s),i[t+1]=Math.min(255,i[t+1]*s),i[t+2]=Math.min(255,i[t+2]*s);this.ctx.putImageData(this.imageData,0,0);return new VideoFrame(this.canvas,{timestamp:t.timestamp,duration:t.duration??void 0})}catch(i){return this.log("Error processing brightness:",i),t}}initializeCanvas(t,i){if(this.canvas=document.createElement("canvas"),this.canvas.width=t,this.canvas.height=i,this.ctx=this.canvas.getContext("2d"),!this.ctx)throw new Error("Failed to get 2D canvas context");this.log(`Canvas initialized: ${t}x${i}`)}setBrightness(t){const i=Math.max(this.config.minBrightness,Math.min(this.config.maxBrightness,t));this.updateConfig({brightness:i}),this.log(`Brightness set to: ${i}`)}getBrightness(){return this.config.brightness||1}async cleanup(){this.log("Cleaning up brightness interceptor..."),this.canvas&&(this.canvas.width=0,this.canvas.height=0,this.canvas=null),this.ctx=null,this.imageData=null,await super.cleanup(),this.log("Brightness interceptor cleanup complete")}getStatus(){return{...this.getMetadata(),brightness:this.config.brightness||1,canvasInitialized:!!this.canvas,canvasSize:this.canvas?`${this.canvas.width}x${this.canvas.height}`:null}}}return"undefined"!=typeof window&&(window.BrightnessInterceptor=i),i}();
//# sourceMappingURL=brightness-interceptor.min.js.map
