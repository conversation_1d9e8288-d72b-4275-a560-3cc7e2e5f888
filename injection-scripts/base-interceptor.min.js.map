{"version": 3, "file": "base-interceptor.min.js", "sources": ["../src/base-interceptor.ts"], "sourcesContent": [null], "names": ["BaseInterceptor", "constructor", "name", "defaultConfig", "this", "isInitialized", "isEnabled", "processor", "generator", "transformStream", "originalTrack", "processedTrack", "Error", "type", "config", "debug", "enabled", "stats", "framesProcessed", "errorsEncountered", "averageProcessingTime", "lastProcessingTime", "totalProcessingTime", "log", "initialize", "videoTrack", "kind", "label", "MediaStreamTrackProcessor", "track", "MediaStreamTrackGenerator", "TransformStream", "transform", "processFrame", "bind", "readable", "pipeThrough", "pipeTo", "writable", "catch", "error", "frame", "controller", "startTime", "performance", "now", "enqueue", "processedFrame", "processVideoFrame", "close", "processingTime", "updateConfig", "newConfig", "oldConfig", "old", "new", "onConfigChange", "getConfig", "enable", "disable", "getStats", "resetStats", "cleanup", "args", "console", "getMetadata", "window"], "mappings": "4CAoBA,MAAeA,EAwBb,WAAAC,CAAYC,EAAcC,EAAmC,IAC3D,GAtBKC,KAAaC,eAAY,EACzBD,KAASE,WAAY,EAKpBF,KAASG,UAAqC,KAC9CH,KAASI,UAAqC,KAC9CJ,KAAeK,gBACrB,KAGML,KAAaM,cAA4B,KACzCN,KAAcO,eAA4B,KAS5CP,KAAKH,cAAgBD,EACvB,MAAM,IAAIY,MACR,4EAIJR,KAAKF,KAAOA,EACZE,KAAKS,KAAOT,KAAKH,YAAYC,KAG7BE,KAAKU,OAAS,CACZC,OAAO,EACPC,SAAS,KACNb,GAILC,KAAKa,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,GAGvBlB,KAAKmB,IAAI,GAAGnB,KAAKF,2BAClB,CAKD,gBAAMsB,CAAWC,GACf,IAAKA,GAAkC,UAApBA,EAAWC,KAC5B,MAAM,IAAId,MAAM,GAAGR,KAAKF,iDAG1BE,KAAKM,cAAgBe,EACrBrB,KAAKmB,IACH,gBAAgBnB,KAAKF,qCACrBuB,EAAWE,OAGb,IAuBE,OArBAvB,KAAKG,UAAY,IAAIqB,0BAA0B,CAAEC,MAAOJ,IACxDrB,KAAKI,UAAY,IAAIsB,0BAA0B,CAAEJ,KAAM,UAGvDtB,KAAKK,gBAAkB,IAAIsB,gBAAgB,CACzCC,UAAW5B,KAAK6B,aAAaC,KAAK9B,QAIpCA,KAAKG,UAAU4B,SACZC,YAAYhC,KAAKK,iBACjB4B,OAAOjC,KAAKI,UAAU8B,UACtBC,MAAOC,IACNpC,KAAKmB,IAAI,qBAAqBnB,KAAKF,QAASsC,GAC5CpC,KAAKa,MAAME,sBAGff,KAAKO,eAAiBP,KAAKI,UAAUqB,MACrCzB,KAAKC,eAAgB,EAErBD,KAAKmB,IAAI,GAAGnB,KAAKF,6CACVE,KAAKO,cACb,CAAC,MAAO6B,GAGP,MAFApC,KAAKmB,IAAI,sBAAsBnB,KAAKF,oBAAqBsC,GACzDpC,KAAKa,MAAME,oBACLqB,CACP,CACF,CAKD,kBAAMP,CACJQ,EACAC,GAEA,MAAMC,EAAYC,YAAYC,MAE9B,IAGE,GAFAzC,KAAKa,MAAMC,mBAENd,KAAKE,YAAcF,KAAKU,OAAOE,QAGlC,YADA0B,EAAWI,QAAQL,GAKrB,MAAMM,QAAuB3C,KAAK4C,kBAAkBP,GAGpDC,EAAWI,QAAQC,GAGfA,IAAmBN,GACrBA,EAAMQ,OAET,CAAC,MAAOT,GACPpC,KAAKmB,IAAI,6BAA6BnB,KAAKF,QAASsC,GACpDpC,KAAKa,MAAME,oBAGXuB,EAAWI,QAAQL,EACpB,CAAS,QAER,MAAMS,EAAiBN,YAAYC,MAAQF,EAC3CvC,KAAKa,MAAMI,mBAAqB6B,EAChC9C,KAAKa,MAAMK,qBAAuB4B,EAClC9C,KAAKa,MAAMG,sBACThB,KAAKa,MAAMK,oBAAsBlB,KAAKa,MAAMC,eAC/C,CACF,CAUD,YAAAiC,CAAaC,GACX,IAAKA,GAAkC,iBAAdA,EAEvB,YADAhD,KAAKmB,IAAI,OAAQ,kDAInB,MAAM8B,EAAY,IAAKjD,KAAKU,QAC5BV,KAAKU,OAAS,IAAKV,KAAKU,UAAWsC,GAEnChD,KAAKmB,IAAI,GAAGnB,KAAKF,8BAA+B,CAC9CoD,IAAKD,EACLE,IAAKnD,KAAKU,SAIRV,KAAKoD,gBACPpD,KAAKoD,eAAeH,EAAWjD,KAAKU,OAEvC,CAKD,SAAA2C,GACE,MAAO,IAAKrD,KAAKU,OAClB,CAKD,MAAA4C,GACEtD,KAAKE,WAAY,EACjBF,KAAKU,OAAOE,SAAU,EACtBZ,KAAKmB,IAAI,GAAGnB,KAAKF,2BAClB,CAKD,OAAAyD,GACEvD,KAAKE,WAAY,EACjBF,KAAKU,OAAOE,SAAU,EACtBZ,KAAKmB,IAAI,GAAGnB,KAAKF,4BAClB,CAKD,QAAA0D,GACE,MAAO,IAAKxD,KAAKa,MAClB,CAKD,UAAA4C,GACEzD,KAAKa,MAAQ,CACXC,gBAAiB,EACjBC,kBAAmB,EACnBC,sBAAuB,EACvBC,mBAAoB,EACpBC,oBAAqB,GAEvBlB,KAAKmB,IAAI,GAAGnB,KAAKF,mBAClB,CAKD,aAAM4D,GACJ,IACM1D,KAAKG,YACPH,KAAKG,UAAY,MAGfH,KAAKI,YACPJ,KAAKI,UAAY,MAGfJ,KAAKK,kBACPL,KAAKK,gBAAkB,MAGrBL,KAAKM,gBACPN,KAAKM,cAAgB,MAGnBN,KAAKO,iBACPP,KAAKO,eAAiB,MAGxBP,KAAKC,eAAgB,EACrBD,KAAKmB,IAAI,GAAGnB,KAAKF,8BAClB,CAAC,MAAOsC,GACPpC,KAAKmB,IAAI,qBAAqBnB,KAAKF,oBAAqBsC,EACzD,CACF,CAKD,GAAAjB,IAAOwC,GACD3D,KAAKU,OAAOC,OACdiD,QAAQzC,IAAI,IAAInB,KAAKF,uBAAwB6D,EAEhD,CAKD,WAAAE,GAQE,MAAO,CACL/D,KAAME,KAAKF,KACXW,KAAMT,KAAKS,KACXR,cAAeD,KAAKC,cACpBC,UAAWF,KAAKE,UAChBQ,OAAQV,KAAKqD,YACbxC,MAAOb,KAAKwD,WAEf,QAOmB,oBAAXM,SACRA,OAAelE,gBAAkBA"}