# Build System Documentation

## Overview

This project uses Rollup to build TypeScript files into IIFE (Immediately Invoked Function Expression) format for browser injection. This ensures compatibility with browser environments that don't support ES modules.

## Build Process

### TypeScript to IIFE Compilation

The build system converts TypeScript files from `src/` directory into browser-compatible IIFE JavaScript files in the `injection-scripts/` directory.

**Input:** `src/*.ts` files  
**Output:** `injection-scripts/*.js` and `injection-scripts/*.min.js` files

### Build Commands

```bash
# Build all TypeScript files to IIFE format
npm run build

# Build with watch mode (rebuilds on file changes)
npm run build-watch

# Legacy build (TypeScript compiler only - produces ES modules)
npm run build-legacy

# Clean build artifacts
npm run clean
```

### File Structure

```
src/
├── base-interceptor.ts
├── blur-interceptor.ts
├── brightness-interceptor.ts
├── cdp-manager-injector.ts
├── change-detector-interceptor.ts
├── control-tab-script.ts
├── interceptor-pipeline.ts
├── interceptor-registry.ts
├── target-tab-streamer.ts
└── video-crop-interceptor.ts

injection-scripts/          # IIFE output (browser-compatible)
├── base-interceptor.js
├── base-interceptor.min.js
├── blur-interceptor.js
├── blur-interceptor.min.js
├── ...
└── *.js.map                # Source maps

out/                        # Legacy TypeScript output (ES modules)
├── *.js
├── *.min.js
└── *.d.ts
```

## Rollup Configuration

The `rollup.config.js` file:

1. **Processes all TypeScript files** in the `src/` directory
2. **Generates IIFE format** with global variable names based on file names
3. **Creates both regular and minified versions** of each file
4. **Includes source maps** for debugging
5. **Resolves dependencies** and bundles them appropriately

### Key Features

- **IIFE Format**: Each file becomes a self-executing function that can be injected into browser contexts
- **Global Variables**: Files are accessible via global variables (e.g., `ControlTabScript`, `BaseInterceptor`)
- **Minification**: Automatic minification with Terser for production use
- **Source Maps**: Full source map support for debugging
- **Dependency Resolution**: Handles imports and dependencies automatically

## Script Injection Priority

The script injector (`script-injector.js`) looks for files in this order:

1. `injection-scripts/*.min.js` (IIFE minified - preferred)
2. `injection-scripts/*.js` (IIFE regular)
3. `out/*.min.js` (Legacy ES modules minified)
4. `out/*.js` (Legacy ES modules regular)

## Development Workflow

### For Active Development

```bash
# Start development with auto-rebuild
npm run build-watch

# In another terminal, start the application
npm run start
```

### For Production

```bash
# Build optimized files
npm run build

# Start the application
npm run start
```

## Browser Compatibility

The IIFE format ensures compatibility with:

- Chrome DevTools Protocol injection
- Browser contexts without ES module support
- Strict Content Security Policy environments
- Legacy browser environments

## Troubleshooting

### Common Issues

1. **"Unexpected token 'export'" Error**
   - This indicates ES module files are being injected instead of IIFE files
   - Ensure `npm run build` has been run to generate IIFE files
   - Check that files exist in `injection-scripts/` directory

2. **Missing Dependencies**
   - Run `npm install` to ensure all Rollup plugins are installed
   - Check that `@rollup/plugin-typescript`, `@rollup/plugin-node-resolve`, `@rollup/plugin-commonjs`, and `@rollup/plugin-terser` are installed

3. **Build Failures**
   - Check TypeScript compilation errors
   - Ensure `tsconfig.json` is properly configured
   - Verify all source files are valid TypeScript

### Debugging

- Use source maps to debug in browser DevTools
- Check browser console for injection errors
- Verify file paths in script injector logs
