{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "lib": ["ES2020", "DOM", "DOM.Iterable"], "outDir": "./out", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnreachableCode": false, "allowUnusedLabels": false}, "include": ["src/**/*"], "exclude": ["node_modules", "out", "injection-scripts"]}