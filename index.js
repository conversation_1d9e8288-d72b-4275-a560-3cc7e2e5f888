/**
 * Simple Single-Stream Test
 *
 * A minimal test to verify the single-stream functionality works
 */

import { POCStreamingSystem } from "./streaming-system.js";

class SimpleSingleStreamTest {
  constructor() {
    this.system = null;
    this.config = {
      browserPort: 9222,
      signalingPort: 8080,
      webServerPort: 3000,
      headless: false,
      autoOpenBrowser: false,
    };
  }

  async start() {
    console.log("🧪 Simple Single-Stream Test Starting...\n");

    try {
      // 1. Start servers
      console.log("📡 Starting servers...");
      this.system = new POCStreamingSystem(this.config);
      await this.system.startSignalingServer();
      await this.system.startWebServer();
      console.log("✅ Servers started");

      // 2. Wait for Chrome to connect
      console.log("\n🔍 Waiting for Chrome...");
      await this.waitForChrome();
      console.log("✅ Chrome connected");

      // 3. Initialize browser
      console.log("\n🔧 Initializing browser...");
      await this.system.initializeBrowserManager();
      await this.system.initializeScriptInjector();
      console.log("✅ Browser initialized");

      // 4. Create test tabs
      console.log("\n📄 Creating test tabs...");
      const tab1 = await this.system.browserManager.createTargetTab(
        "https://youtube.com"
      );
      await this.sleep(2000);
      const tab2 = await this.system.browserManager.createTargetTab(
        "https://2captcha.com/demo/recaptcha-v2"
      );
      await this.sleep(2000);
      console.log("✅ Test tabs created");

      // 5. Inject scripts
      console.log("\n💉 Injecting scripts...");
      const signalingUrl = `ws://localhost:${this.config.signalingPort}`;
      await this.system.scriptInjector.injectTargetTabScript(
        tab1.id,
        signalingUrl
      );
      await this.system.scriptInjector.injectTargetTabScript(
        tab2.id,
        signalingUrl
      );
      console.log("✅ Scripts injected");

      // 6. Display instructions
      this.displayInstructions();

      // Keep running
      await this.keepAlive();
    } catch (error) {
      console.error("❌ Test failed:", error);
      process.exit(1);
    }
  }

  async waitForChrome() {
    const maxAttempts = 60;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const response = await fetch(
          `http://localhost:${this.config.browserPort}/json/version`
        );
        if (response.ok) return;
      } catch (error) {
        // Chrome not ready
      }

      attempts++;
      await this.sleep(1000);

      if (attempts % 10 === 0) {
        console.log(`  ⏳ Still waiting... (${attempts}/${maxAttempts})`);
      }
    }

    throw new Error("Chrome not found");
  }

  displayInstructions() {
    console.log("\n" + "=".repeat(60));
    console.log("🎉 SINGLE-STREAM TEST READY!");
    console.log("=".repeat(60));

    console.log("\n📋 NEXT STEPS:");
    console.log("1. Open web client: http://localhost:3000");
    console.log("2. You should see 2 available tabs");
    console.log("3. Click on any tab to start streaming");
    console.log("4. Click on another tab to switch streams");
    console.log("5. Only one stream should be active at a time");

    console.log("\n✅ EXPECTED BEHAVIOR:");
    console.log("• Tabs show 'Click to Stream' when not active");
    console.log("• Clicking a tab starts streaming from that tab");
    console.log("• Only one tab streams at a time");
    console.log("• Stream switches immediately when clicking different tabs");

    console.log("\n⏹️  Press Ctrl+C to stop");
    console.log("=".repeat(60) + "\n");
  }

  async keepAlive() {
    process.on("SIGINT", async () => {
      console.log("\n🛑 Stopping test...");
      if (this.system) {
        await this.system.stop();
      }
      process.exit(0);
    });

    // Keep alive
    while (true) {
      await this.sleep(1000);
    }
  }

  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// Run the test
const test = new SimpleSingleStreamTest();
test.start().catch(console.error);
