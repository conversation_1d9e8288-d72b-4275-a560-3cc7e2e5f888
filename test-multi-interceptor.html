<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Interceptor System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .video-section {
            flex: 1;
            text-align: center;
        }
        video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 4px;
        }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .control-group {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        input[type="range"] {
            width: 200px;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .pipeline-config {
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
        }
        .interceptor-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 5px 0;
            padding: 5px;
            background: white;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Multi-Interceptor System Test</h1>
        <p>This page demonstrates the new multi-interceptor architecture for video frame processing.</p>

        <div class="video-container">
            <div class="video-section">
                <h3>Original Video</h3>
                <video id="originalVideo" autoplay muted></video>
            </div>
            <div class="video-section">
                <h3>Processed Video</h3>
                <video id="processedVideo" autoplay muted></video>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <button id="startCapture" class="btn-primary">Start Screen Capture</button>
                <button id="stopCapture" class="btn-danger" disabled>Stop Capture</button>
            </div>
        </div>

        <div class="pipeline-config">
            <h3>Interceptor Pipeline Configuration</h3>
            <div id="interceptorList">
                <div class="interceptor-item">
                    <input type="checkbox" id="crop-enabled" checked>
                    <label for="crop-enabled">Video Crop</label>
                    <button onclick="configureInterceptor('video-crop')" class="btn-secondary">Configure</button>
                </div>
                <div class="interceptor-item">
                    <input type="checkbox" id="brightness-enabled">
                    <label for="brightness-enabled">Brightness Filter</label>
                    <input type="range" id="brightness-slider" min="0.1" max="3.0" step="0.1" value="1.0">
                    <span id="brightness-value">1.0</span>
                </div>
                <div class="interceptor-item">
                    <input type="checkbox" id="blur-enabled">
                    <label for="blur-enabled">Blur Effect</label>
                    <input type="range" id="blur-slider" min="0" max="20" step="1" value="0">
                    <span id="blur-value">0px</span>
                </div>
            </div>
            <div class="control-group">
                <button id="applyPipeline" class="btn-success">Apply Pipeline</button>
                <button id="resetPipeline" class="btn-secondary">Reset</button>
            </div>
        </div>

        <div class="status info" id="status">
            Ready to test multi-interceptor system...
        </div>

        <div class="status info" id="performance">
            Performance stats will appear here...
        </div>
    </div>

    <!-- Load interceptor system scripts -->
    <script src="injection-scripts/base-interceptor.js"></script>
    <script src="injection-scripts/interceptor-registry.js"></script>
    <script src="injection-scripts/interceptor-pipeline.js"></script>
    <script src="injection-scripts/video-crop-interceptor.js"></script>
    <script src="injection-scripts/brightness-interceptor.js"></script>
    <script src="injection-scripts/blur-interceptor.js"></script>

    <script>
        class MultiInterceptorTest {
            constructor() {
                this.originalStream = null;
                this.processedStream = null;
                this.pipeline = null;
                this.registry = null;
                
                this.setupEventListeners();
                this.initializeRegistry();
                this.updateStatus('Multi-interceptor test initialized');
            }

            setupEventListeners() {
                document.getElementById('startCapture').addEventListener('click', () => this.startCapture());
                document.getElementById('stopCapture').addEventListener('click', () => this.stopCapture());
                document.getElementById('applyPipeline').addEventListener('click', () => this.applyPipeline());
                document.getElementById('resetPipeline').addEventListener('click', () => this.resetPipeline());
                
                // Brightness control
                const brightnessSlider = document.getElementById('brightness-slider');
                const brightnessValue = document.getElementById('brightness-value');
                brightnessSlider.addEventListener('input', (e) => {
                    brightnessValue.textContent = e.target.value;
                    this.updateInterceptorConfig('brightness-filter', { brightness: parseFloat(e.target.value) });
                });
                
                // Blur control
                const blurSlider = document.getElementById('blur-slider');
                const blurValue = document.getElementById('blur-value');
                blurSlider.addEventListener('input', (e) => {
                    blurValue.textContent = e.target.value + 'px';
                    this.updateInterceptorConfig('blur-effect', { blurRadius: parseInt(e.target.value) });
                });
            }

            initializeRegistry() {
                this.registry = new InterceptorRegistry();
                
                // Register interceptors
                this.registry.register('video-crop', VideoFrameInterceptor, {
                    debug: true,
                    enableCropping: true,
                    croppingEnabled: false
                });
                
                this.registry.register('brightness-filter', BrightnessInterceptor, {
                    debug: true,
                    brightness: 1.0
                });
                
                this.registry.register('blur-effect', BlurInterceptor, {
                    debug: true,
                    blurRadius: 0
                });
                
                this.updateStatus('Interceptor registry initialized with: ' + this.registry.getRegisteredInterceptors().join(', '));
            }

            async startCapture() {
                try {
                    this.updateStatus('Starting screen capture...');
                    
                    this.originalStream = await navigator.mediaDevices.getDisplayMedia({
                        video: { width: 1280, height: 720, frameRate: 30 },
                        audio: false
                    });
                    
                    document.getElementById('originalVideo').srcObject = this.originalStream;
                    document.getElementById('startCapture').disabled = true;
                    document.getElementById('stopCapture').disabled = false;
                    
                    this.updateStatus('Screen capture started. Configure interceptors and apply pipeline.');
                } catch (error) {
                    this.updateStatus('Error starting capture: ' + error.message, 'error');
                }
            }

            stopCapture() {
                if (this.originalStream) {
                    this.originalStream.getTracks().forEach(track => track.stop());
                    this.originalStream = null;
                }
                
                if (this.processedStream) {
                    this.processedStream.getTracks().forEach(track => track.stop());
                    this.processedStream = null;
                }
                
                if (this.pipeline) {
                    this.pipeline.cleanup();
                    this.pipeline = null;
                }
                
                document.getElementById('originalVideo').srcObject = null;
                document.getElementById('processedVideo').srcObject = null;
                document.getElementById('startCapture').disabled = false;
                document.getElementById('stopCapture').disabled = true;
                
                this.updateStatus('Capture stopped and resources cleaned up.');
            }

            applyPipeline() {
                if (!this.originalStream) {
                    this.updateStatus('Please start screen capture first.', 'error');
                    return;
                }

                try {
                    // Get enabled interceptors
                    const enabledInterceptors = [];
                    const configs = {};
                    
                    if (document.getElementById('crop-enabled').checked) {
                        enabledInterceptors.push('video-crop');
                        configs['video-crop'] = { croppingEnabled: false }; // Configure as needed
                    }
                    
                    if (document.getElementById('brightness-enabled').checked) {
                        enabledInterceptors.push('brightness-filter');
                        configs['brightness-filter'] = { 
                            brightness: parseFloat(document.getElementById('brightness-slider').value) 
                        };
                    }
                    
                    if (document.getElementById('blur-enabled').checked) {
                        enabledInterceptors.push('blur-effect');
                        configs['blur-effect'] = { 
                            blurRadius: parseInt(document.getElementById('blur-slider').value) 
                        };
                    }
                    
                    if (enabledInterceptors.length === 0) {
                        this.updateStatus('No interceptors enabled. Select at least one interceptor.', 'error');
                        return;
                    }
                    
                    // Set client configuration
                    this.registry.setClientConfiguration('test-client', enabledInterceptors, configs);
                    
                    // Create interceptors
                    const interceptors = this.registry.createClientInterceptors('test-client');
                    
                    // Create pipeline
                    if (this.pipeline) {
                        this.pipeline.cleanup();
                    }
                    this.pipeline = new InterceptorPipeline(interceptors);
                    
                    // Process video track
                    const videoTrack = this.originalStream.getVideoTracks()[0];
                    const processedTrack = this.pipeline.initialize(videoTrack);
                    
                    // Create processed stream
                    this.processedStream = new MediaStream([processedTrack]);
                    document.getElementById('processedVideo').srcObject = this.processedStream;
                    
                    this.updateStatus(`Pipeline applied with interceptors: ${enabledInterceptors.join(' → ')}`);
                    
                    // Start performance monitoring
                    this.startPerformanceMonitoring();
                    
                } catch (error) {
                    this.updateStatus('Error applying pipeline: ' + error.message, 'error');
                }
            }

            resetPipeline() {
                // Reset all controls
                document.getElementById('crop-enabled').checked = true;
                document.getElementById('brightness-enabled').checked = false;
                document.getElementById('blur-enabled').checked = false;
                document.getElementById('brightness-slider').value = 1.0;
                document.getElementById('brightness-value').textContent = '1.0';
                document.getElementById('blur-slider').value = 0;
                document.getElementById('blur-value').textContent = '0px';
                
                // Clean up current pipeline
                if (this.pipeline) {
                    this.pipeline.cleanup();
                    this.pipeline = null;
                }
                
                document.getElementById('processedVideo').srcObject = null;
                this.updateStatus('Pipeline reset to default configuration.');
            }

            updateInterceptorConfig(interceptorName, config) {
                if (this.registry && this.pipeline) {
                    try {
                        this.registry.updateClientInterceptorConfig('test-client', interceptorName, config);
                        this.updateStatus(`Updated ${interceptorName} configuration`);
                    } catch (error) {
                        this.updateStatus(`Error updating ${interceptorName}: ${error.message}`, 'error');
                    }
                }
            }

            startPerformanceMonitoring() {
                if (this.performanceInterval) {
                    clearInterval(this.performanceInterval);
                }
                
                this.performanceInterval = setInterval(() => {
                    if (this.pipeline) {
                        const stats = this.pipeline.getStats();
                        const perfText = `Frames: ${stats.framesProcessed}, Avg Time: ${stats.averageProcessingTime.toFixed(2)}ms, Errors: ${stats.errorsEncountered}`;
                        document.getElementById('performance').textContent = perfText;
                    }
                }, 1000);
            }

            updateStatus(message, type = 'info') {
                const statusEl = document.getElementById('status');
                statusEl.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                statusEl.className = `status ${type}`;
            }
        }

        // Initialize test when page loads
        window.addEventListener('load', () => {
            window.multiInterceptorTest = new MultiInterceptorTest();
        });
    </script>
</body>
</html>
