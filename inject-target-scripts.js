import { ScriptInjector } from './script-injector.js';
import { BrowserManager } from './browser-manager.js';

// Connect to existing Chrome instance
const browserManager = new BrowserManager({ port: 9222 });

try {
  // Get list of tabs
  const response = await fetch('http://localhost:9222/json');
  const tabs = await response.json();
  
  // Filter for actual web pages (not about:blank)
  const targetTabs = tabs.filter(tab => 
    tab.url && 
    !tab.url.startsWith('about:') && 
    !tab.url.startsWith('chrome:') &&
    !tab.url.startsWith('devtools:')
  );
  
  console.log('Found target tabs:', targetTabs.map(t => ({ id: t.id, url: t.url })));
  
  // Create script injector
  const scriptInjector = new ScriptInjector(browserManager, null);
  
  // Inject scripts into target tabs
  for (const tab of targetTabs) {
    try {
      console.log(`Injecting script into tab: ${tab.id} (${tab.url})`);
      await scriptInjector.injectTargetTabScript(tab.id, 'ws://localhost:8081');
      console.log(`✅ Script injected successfully into tab: ${tab.id}`);
    } catch (error) {
      console.error(`❌ Failed to inject script into tab ${tab.id}:`, error.message);
    }
  }
  
  console.log('✅ Script injection completed');
  
} catch (error) {
  console.error('❌ Failed to inject scripts:', error);
}
