import WebSocket from 'ws';

// Test multiple web client connections to verify the refactored system
const SIGNALING_SERVER_URL = 'ws://localhost:8081';

class TestWebClient {
  constructor(clientName) {
    this.clientName = clientName;
    this.ws = null;
    this.clientId = null;
  }

  async connect() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(SIGNALING_SERVER_URL);
      
      this.ws.onopen = () => {
        console.log(`✅ ${this.clientName} connected to signaling server`);
      };
      
      this.ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        console.log(`📨 ${this.clientName} received:`, message.type);
        
        if (message.type === 'welcome') {
          this.clientId = message.clientId;
          console.log(`🆔 ${this.clientName} got client ID: ${this.clientId}`);
          
          // Register as web client
          this.ws.send(JSON.stringify({
            type: 'register-web-client',
            metadata: {
              userAgent: `TestClient-${this.clientName}`,
              timestamp: Date.now(),
            },
          }));
          
          resolve();
        } else if (message.type === 'available-streams') {
          console.log(`📺 ${this.clientName} received available streams:`, message.targetTabs.length);
        }
      };
      
      this.ws.onerror = (error) => {
        console.error(`❌ ${this.clientName} WebSocket error:`, error);
        reject(error);
      };
      
      this.ws.onclose = () => {
        console.log(`🔌 ${this.clientName} disconnected`);
      };
    });
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

async function testMultiClientConnections() {
  console.log('🧪 Testing multi-client connections...');
  
  const clients = [
    new TestWebClient('Client-1'),
    new TestWebClient('Client-2'),
    new TestWebClient('Client-3'),
  ];
  
  try {
    // Connect all clients
    for (const client of clients) {
      await client.connect();
      // Small delay between connections
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('✅ All clients connected successfully!');
    console.log('🔍 Check the signaling server logs to verify:');
    console.log('   - Each client gets a unique ID');
    console.log('   - Control tab receives web-client-registered messages');
    console.log('   - Peer connections are created for each client');
    
    // Keep connections open for a bit
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Disconnect all clients
    console.log('🔌 Disconnecting all clients...');
    for (const client of clients) {
      client.disconnect();
    }
    
    console.log('✅ Multi-client test completed!');
    
  } catch (error) {
    console.error('❌ Multi-client test failed:', error);
  }
}

testMultiClientConnections();
